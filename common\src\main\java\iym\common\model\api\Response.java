package iym.common.model.api;

import iym.common.enums.ResultCode;
import lombok.Data;

import java.io.Serializable;

@Data
public class Response<T> implements Serializable {

    private ResultCode resultCode;

    private String resultDetails;

    private Throwable exception;

    private T result;

    public Response(T result) {
        this.result = result;
        this.resultCode = ResultCode.SUCCESS;
    }

    public Response(ResultCode resultCode) {
        this.resultCode = resultCode;
    }

    public Response(ResultCode resultCode, String resultDetails) {
        this(resultCode);
        this.resultDetails = resultDetails;
    }

    public Response(ResultCode resultCode, String resultDetails, Throwable exception) {
        this(resultCode, resultDetails);
        this.exception = exception;
    }

    public boolean isSuccess() {
        return resultCode == ResultCode.SUCCESS;
    }
}
