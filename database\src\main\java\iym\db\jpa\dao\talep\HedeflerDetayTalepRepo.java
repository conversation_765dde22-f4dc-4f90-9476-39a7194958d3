package iym.db.jpa.dao.talep;

import iym.common.model.entity.iym.talep.HedeflerDetayTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for HedeflerTalep entity
 */
@Repository
public interface HedeflerDetayTalepRepo extends JpaRepository<HedeflerDetayTalep, Long> {

    List<HedeflerDetayTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

    //    private String hedefNo;
    //
    //    @Column(name = "HEDEF_TIPI", nullable = false)
    //    @NotNull
    //    private Integer hedefTipi;
    //mahkemeKararTalepId
    Optional<HedeflerDetayTalep> findByMahkemeKararTalepIdAndHedefNoAndHedefTipi(Long mahkemeKararTalepId, String hedefNo, Integer hedefTipi);


}
