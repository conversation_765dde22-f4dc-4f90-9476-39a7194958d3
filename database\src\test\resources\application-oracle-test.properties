# Oracle Testcontainer Test Profile Configuration for Database Module
# This profile is used for integration tests with Oracle Testcontainer
# The actual connection properties are set dynamically by AbstractOracleTestContainer
# Profile is activated via @ActiveProfiles("oracle-test") annotation in test classes

# JPA/Hibernate configuration for Oracle
spring.jpa.database-platform=org.hibernate.dialect.Oracle12cDialect
spring.jpa.properties.hibernate.default_schema=iym
spring.jpa.hibernate.ddl-auto=create
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# Connection pool configuration optimized for testing
spring.datasource.hikari.connectionTimeout=20000
spring.datasource.hikari.maximumPoolSize=5
spring.datasource.hikari.minimumIdle=2
spring.datasource.hikari.idleTimeout=300000
spring.datasource.hikari.maxLifetime=1200000
spring.datasource.hikari.leakDetectionThreshold=60000

# SQL initialization - disabled since we use init script in container
spring.sql.init.mode=never
spring.jpa.defer-datasource-initialization=false

# Comprehensive logging configuration for debugging integration test issues
# Hibernate/JPA logging
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.hibernate.engine.jdbc.connections=DEBUG
logging.level.org.hibernate.engine.jdbc.env.internal=DEBUG
logging.level.org.springframework.jdbc.core=DEBUG
logging.level.org.springframework.orm.jpa=DEBUG
logging.level.org.springframework.transaction=DEBUG

# Testcontainers detailed logging
logging.level.org.testcontainers=DEBUG
logging.level.org.testcontainers.containers=DEBUG
logging.level.org.testcontainers.containers.OracleContainer=DEBUG
logging.level.org.testcontainers.dockerclient=DEBUG
logging.level.com.github.dockerjava=DEBUG

# HikariCP connection pool detailed logging
logging.level.com.zaxxer.hikari=TRACE
logging.level.com.zaxxer.hikari.HikariConfig=TRACE
logging.level.com.zaxxer.hikari.HikariDataSource=TRACE
logging.level.com.zaxxer.hikari.pool=TRACE
logging.level.com.zaxxer.hikari.pool.HikariPool=TRACE

# Oracle JDBC driver detailed logging
logging.level.oracle.jdbc=TRACE
logging.level.oracle.net=TRACE
logging.level.oracle.security=DEBUG
logging.level.oracle.sql=DEBUG

# Application logging
logging.level.iym=DEBUG
logging.level.iym.common.testcontainer=TRACE

# Spring Boot DataSource logging
logging.level.org.springframework.boot.autoconfigure.jdbc=DEBUG
logging.level.org.springframework.boot.jdbc=DEBUG

# Test-specific configurations
spring.test.database.replace=none
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false

# Disable banner for cleaner test output
spring.main.banner-mode=off

# Transaction configuration for tests
spring.jpa.properties.hibernate.connection.autocommit=false
spring.transaction.default-timeout=30

# Oracle-specific configurations
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true

# Database module specific configurations
spring.application.name=database-test
