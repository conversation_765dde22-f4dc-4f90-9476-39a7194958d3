-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for HEDEFLER if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'HEDEFLER_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.HEDEFLER_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

--TODO : byte lar degisecek
-- PK eklenecek
DECLARE
  table_exists NUMBER;

BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'HEDEFLER';
  IF table_exists = 0 THEN

    EXECUTE IMMEDIATE 'CREATE TABLE iym.HEDEFLER (
              ID NUMBER
            , BIRIM_KOD NUMBER
            , KULLANICI_ID NUMBER
            , TEK_MASA_KUL_ID NUMBER
            , HEDEF_NO VARCHAR2(100) NOT NULL
            , HEDEF_TIPI VARCHAR2(20) NOT NULL
            , HEDEF_ADI VARCHAR2(100)
            , HEDEF_SOYADI VARCHAR2(100)
            , BASLAMA_TARIHI DATE
            , SURESI NUMBER
            , SURE_TIPI NUMBER
            , UZATMA_SAYISI NUMBER
            , DURUMU VARCHAR2(100)
            , ACIKLAMA VARCHAR2(250)
            , MAHKEME_KARAR_ID NUMBER NOT NULL
            , HEDEF_AIDIYAT_ID NUMBER
            , GRUP_KOD NUMBER
            , AIDIYAT_KOD VARCHAR2(35)
            , UNIQ_KOD NUMBER
            , KAYIT_TARIHI DATE
            , TANIMLAMA_TARIHI DATE
            , KAPATMA_KARAR_ID NUMBER
            , KAPATMA_TARIHI DATE
            , IMHA VARCHAR2(20)
            , IMHA_TARIHI DATE
            , UZATMA_ID NUMBER
            , DEGISME_DURUMU VARCHAR2(500)
            , OPERATOR VARCHAR2(30)
            , ACILMI  VARCHAR2(1)
            , HEDEF_118_ADI VARCHAR2(128)
            , HEDEF_118_SOYADI VARCHAR2(128)
            , HEDEF_118_ADRES VARCHAR2(512)
            , CANAK_NO VARCHAR2(100)
            , TCKN VARCHAR2(11)
            , CONSTRAINT HEDEFLER_PK PRIMARY KEY (ID) ENABLE
    )';

  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.HEDEFLER;
  IF row_count = 0 THEN
    -- Make sure we have mahkeme_karar and hedef_tipleri records
    DECLARE
      mahkeme_count NUMBER;
      hedef_tip_count NUMBER;
      evrak_id1 NUMBER;
      mahkeme_karar_id1 NUMBER;
    BEGIN
      SELECT COUNT(*) INTO mahkeme_count FROM iym.MAHKEME_KARAR;
      SELECT COUNT(*) INTO hedef_tip_count FROM iym.HEDEF_TIPLERI;

      -- Get the ID of the first evrak
      SELECT ID INTO evrak_id1 FROM iym.EVRAK_KAYIT WHERE EVRAK_SIRA_NO = 'TEST-2025-001';
      SELECT ID INTO mahkeme_karar_id1 FROM iym.MAHKEME_KARAR WHERE EVRAK_ID=evrak_id1;
      IF(mahkeme_karar_id1 > 0) THEN

            -- Sample data 1 - Telefon hedefi
            INSERT INTO iym.HEDEFLER (
            ID,  KULLANICI_ID, HEDEF_NO, HEDEF_TIPI, HEDEF_ADI, HEDEF_SOYADI,
            BASLAMA_TARIHI, SURESI, SURE_TIPI, UZATMA_SAYISI, DURUMU,
             MAHKEME_KARAR_ID, KAYIT_TARIHI,  ACILMI,  CANAK_NO
            ) VALUES (
            iym.HEDEFLER_SEQ.NEXTVAL, 1,  '55511121111', 10, 'Ahmet', 'Yılmaz',
            SYSDATE, 30, 1, 0, 'AKTIF',
            mahkeme_karar_id1, sysdate, 'E', '');

            INSERT INTO iym.HEDEFLER (
            ID,  KULLANICI_ID, HEDEF_NO, HEDEF_TIPI, HEDEF_ADI, HEDEF_SOYADI,
            BASLAMA_TARIHI, SURESI, SURE_TIPI, UZATMA_SAYISI, DURUMU,
             MAHKEME_KARAR_ID, KAYIT_TARIHI,  ACILMI,  CANAK_NO
            ) VALUES (
            iym.HEDEFLER_SEQ.NEXTVAL, 1,  '55511131111', 10, 'Ahmet', 'Yılmaz',
            SYSDATE, 30, 1, 0, 'AKTIF',
            mahkeme_karar_id1, sysdate, 'E', '');

            INSERT INTO iym.HEDEFLER (
            ID,  KULLANICI_ID, HEDEF_NO, HEDEF_TIPI, HEDEF_ADI, HEDEF_SOYADI,
            BASLAMA_TARIHI, SURESI, SURE_TIPI, UZATMA_SAYISI, DURUMU,
             MAHKEME_KARAR_ID, KAYIT_TARIHI,  ACILMI,  CANAK_NO
            ) VALUES (
            iym.HEDEFLER_SEQ.NEXTVAL, 1,  '55511141111', 10, 'Ahmet', 'Yılmaz',
            SYSDATE, 30, 1, 0, 'AKTIF',
            mahkeme_karar_id1, sysdate, 'E', '');


      END IF;



    END;
  END IF;
END;
/



COMMIT;
