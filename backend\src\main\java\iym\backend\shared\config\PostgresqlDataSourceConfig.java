package iym.backend.shared.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EntityScan(basePackages = "iym.backend")
@EnableJpaRepositories(basePackages = "iym.backend")
@EnableTransactionManagement
public class PostgresqlDataSourceConfig {
    // PostgreSQL-specific configuration will be here
    // Entity scanning and repository configuration for PostgreSQL
    // Uses Spring Boot auto-configuration for DataSource and EntityManagerFactory
}
