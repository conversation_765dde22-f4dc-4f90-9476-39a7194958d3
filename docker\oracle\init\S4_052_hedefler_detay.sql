-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for HEDEFLER_DETAY_SEQ if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'HEDEFLER_DETAY_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.HEDEFLER_DETAY_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create HEDEFLER_DETAY table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'HEDEFLER_DETAY';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.HEDEFLER_DETAY (
      ID NUMBER NOT NULL,
      HEDEF_NO VARCHAR2(100 BYTE),
      HEDEF_TIPI NUMBER,
      HEDEF_ADI VARCHAR2(100 BYTE),
      HEDEF_SOYADI VARCHAR2(100 BYTE),
      KAYIT_TARIHI DATE,
      DURUMU VARCHAR2(20 BYTE),
      ILISKILI_HEDEF_ID NUMBER,
      MAHKEME_KARAR_ID NUMBER,
      MAHKEME_KARAR_DETAY_ID NUMBER,
      CANAK_NO VARCHAR2(100 BYTE),
      TCKN VARCHAR2(11 BYTE),
      UPDATE_COLUMN_NAMES VARCHAR2(400 BYTE),
      CONSTRAINT HED_DETAY_PK PRIMARY KEY (ID) ENABLE
    )';

  END IF;
END;
/


COMMIT;
