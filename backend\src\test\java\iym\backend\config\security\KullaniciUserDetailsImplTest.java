package iym.backend.config.security;

import iym.backend.kullanici.entity.Kullanici;
import iym.backend.kullanici.enums.enumKullaniciStatus;
import iym.backend.kullanicigrup.entity.KullaniciGrup;
import iym.backend.kullanicigrupyetki.entity.KullaniciGrupYetki;
import iym.backend.kullanicikullanicigrup.entity.KullaniciKullaniciGrup;
import iym.backend.shared.security.KullaniciUserDetailsImpl;
import iym.backend.yetki.entity.Yetki;
import iym.common.enums.KullaniciKurum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.security.core.GrantedAuthority;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for KullaniciUserDetailsImpl
 */
class KullaniciUserDetailsImplTest {

    private Kullanici kullanici;
    private KullaniciUserDetailsImpl userDetails;

    @BeforeEach
    void setUp() {
        kullanici = new Kullanici();
        kullanici.setId(1L);
        kullanici.setKullaniciAdi("testuser");
        kullanici.setParola("testpassword");
        kullanici.setEmail("<EMAIL>");
        kullanici.setAd("Test");
        kullanici.setSoyad("User");
        kullanici.setStatus(enumKullaniciStatus.AKTIF);
        kullanici.setKurum(KullaniciKurum.BTK);
    }

    @Test
    void testConstructorWithKullaniciWithoutRoles() {
        // Test constructor with Kullanici that has no roles
        userDetails = new KullaniciUserDetailsImpl(kullanici);

        assertEquals(1L, userDetails.getUserId());
        assertEquals("testuser", userDetails.getUsername());
        assertEquals("testpassword", userDetails.getPassword());
        assertEquals(KullaniciKurum.BTK, userDetails.getKurum());

        // Should have default role
        Collection<? extends GrantedAuthority> authorities = userDetails.getAuthorities();
        assertEquals(1, authorities.size());
        assertTrue(authorities.stream().anyMatch(auth -> auth.getAuthority().equals("ROLE_USER")));
    }

    @Test
    void testConstructorWithKullaniciWithRoles() {
        // Setup test data with roles
        setupKullaniciWithRoles();

        userDetails = new KullaniciUserDetailsImpl(kullanici);

        assertEquals(1L, userDetails.getUserId());
        assertEquals("testuser", userDetails.getUsername());
        assertEquals("testpassword", userDetails.getPassword());
        assertEquals(KullaniciKurum.BTK, userDetails.getKurum());

        // Should have the assigned roles
        Collection<? extends GrantedAuthority> authorities = userDetails.getAuthorities();
        assertEquals(2, authorities.size());
        assertTrue(authorities.stream().anyMatch(auth -> auth.getAuthority().equals("ROLE_ADMIN")));
        assertTrue(authorities.stream().anyMatch(auth -> auth.getAuthority().equals("USER_VIEW")));
    }

    @Test
    void testConstructorWithParameters() {
        List<String> authorities = Arrays.asList("ROLE_ADMIN", "USER_EDIT");

        userDetails = new KullaniciUserDetailsImpl(1L, "testuser", "testpassword", KullaniciKurum.BTK, authorities);

        assertEquals(1L, userDetails.getUserId());
        assertEquals("testuser", userDetails.getUsername());
        assertEquals("testpassword", userDetails.getPassword());
        assertEquals(KullaniciKurum.BTK, userDetails.getKurum());

        Collection<? extends GrantedAuthority> grantedAuthorities = userDetails.getAuthorities();
        assertEquals(2, grantedAuthorities.size());
        assertTrue(grantedAuthorities.stream().anyMatch(auth -> auth.getAuthority().equals("ROLE_ADMIN")));
        assertTrue(grantedAuthorities.stream().anyMatch(auth -> auth.getAuthority().equals("USER_EDIT")));
    }

    @Test
    void testConstructorWithEmptyAuthorities() {
        userDetails = new KullaniciUserDetailsImpl(1L, "testuser", "testpassword", KullaniciKurum.BTK, new ArrayList<>());

        // Should have default role
        Collection<? extends GrantedAuthority> authorities = userDetails.getAuthorities();
        assertEquals(1, authorities.size());
        assertTrue(authorities.stream().anyMatch(auth -> auth.getAuthority().equals("ROLE_USER")));
    }

    @Test
    void testHasAuthority() {
        List<String> authorities = Arrays.asList("ROLE_ADMIN", "USER_VIEW", "USER_EDIT");
        userDetails = new KullaniciUserDetailsImpl(1L, "testuser", "testpassword", KullaniciKurum.BTK, authorities);

        assertTrue(userDetails.hasAuthority("ROLE_ADMIN"));
        assertTrue(userDetails.hasAuthority("USER_VIEW"));
        assertTrue(userDetails.hasAuthority("USER_EDIT"));
        assertFalse(userDetails.hasAuthority("ROLE_SUPER_ADMIN"));
        assertFalse(userDetails.hasAuthority("USER_DELETE"));
    }

    @Test
    void testHasAnyAuthority() {
        List<String> authorities = Arrays.asList("USER_VIEW", "USER_EDIT");
        userDetails = new KullaniciUserDetailsImpl(1L, "testuser", "testpassword", KullaniciKurum.BTK, authorities);

        assertTrue(userDetails.hasAnyAuthority("ROLE_ADMIN", "USER_VIEW"));
        assertTrue(userDetails.hasAnyAuthority("USER_EDIT", "ROLE_SUPER_ADMIN"));
        assertFalse(userDetails.hasAnyAuthority("ROLE_ADMIN", "ROLE_SUPER_ADMIN"));
    }

    @Test
    void testUserDetailsInterfaceMethods() {
        userDetails = new KullaniciUserDetailsImpl(kullanici);

        assertTrue(userDetails.isAccountNonExpired());
        assertTrue(userDetails.isAccountNonLocked());
        assertTrue(userDetails.isCredentialsNonExpired());
        assertTrue(userDetails.isEnabled());
    }

    @Test
    void testEqualsAndHashCode() {
        userDetails = new KullaniciUserDetailsImpl(kullanici);
        KullaniciUserDetailsImpl userDetails2 = new KullaniciUserDetailsImpl(kullanici);

        assertEquals(userDetails, userDetails2);
        assertEquals(userDetails.hashCode(), userDetails2.hashCode());

        // Different user
        Kullanici kullanici2 = new Kullanici();
        kullanici2.setId(2L);
        kullanici2.setKullaniciAdi("testuser2");
        kullanici2.setParola("testpassword2");
        kullanici2.setStatus(enumKullaniciStatus.AKTIF);

        KullaniciUserDetailsImpl userDetails3 = new KullaniciUserDetailsImpl(kullanici2);
        assertNotEquals(userDetails, userDetails3);
    }

    private void setupKullaniciWithRoles() {
        Yetki yetki1 = new Yetki();
        yetki1.setId(1L);
        yetki1.setAd("ROLE_ADMIN");

        Yetki yetki2 = new Yetki();
        yetki2.setId(2L);
        yetki2.setAd("USER_VIEW");

        KullaniciGrup grup = new KullaniciGrup();
        grup.setId(1L);
        grup.setAd("Admin Group");

        KullaniciGrupYetki kgy1 = new KullaniciGrupYetki();
        kgy1.setKullaniciGrup(grup);
        kgy1.setYetki(yetki1);

        KullaniciGrupYetki kgy2 = new KullaniciGrupYetki();
        kgy2.setKullaniciGrup(grup);
        kgy2.setYetki(yetki2);

        List<KullaniciGrupYetki> grupYetkiler = new ArrayList<>();
        grupYetkiler.add(kgy1);
        grupYetkiler.add(kgy2);
        grup.setKullaniciGrupYetkiler(grupYetkiler);

        KullaniciKullaniciGrup kkg = new KullaniciKullaniciGrup();
        kkg.setKullanici(kullanici);
        kkg.setKullaniciGrup(grup);

        List<KullaniciKullaniciGrup> kullaniciGruplar = new ArrayList<>();
        kullaniciGruplar.add(kkg);
        kullanici.setKullaniciKullaniciGruplar(kullaniciGruplar);
    }
}
