-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MK_GUNCELLEME_SEQ if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MK_GUNCELLEME_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MK_GUNCELLEME_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create MAHKEME_KARAR_GUNCELLEME_ISLEM table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_KARAR_GUNCELLEME';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_KARAR_GUNCELLEME (
      ID NUMBER NOT NULL,
      MAHKEME_KARAR_DETAY_ID NUMBER NOT NULL,
      MAHKEME_KODU VARCHAR2(25 BYTE),
      SORUSTURMA_NO VARCHAR2(50 BYTE),
      MAHKEME_KARAR_NO VARCHAR2(50 BYTE),
      DURUMU VARCHAR2(15 BYTE),
      UPDATE_COLUMN_NAMES VARCHAR2(400 BYTE),
      CONSTRAINT MK_GUNCELLEME_IDX PRIMARY KEY (ID) ENABLE
    )';

  END IF;
END;
/


COMMIT;
