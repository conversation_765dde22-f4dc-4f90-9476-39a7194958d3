-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;


-- Create TESPIT_TURLERI table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'TESPIT_TURLERI';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE '
        CREATE TABLE iym.TESPIT_TURLERI (
             TESPIT_TURU NUMBER NOT NULL,
             TESPIT_ACIKLAMA VARCHAR2(1000) NOT NULL,
             CONSTRAINT TESPIT_TURLERI_PRK PRIMARY KEY (TESPIT_TURU)ENABLE
        )
    ';

  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.TESPIT_TURLERI;
  IF row_count = 0 THEN


    INSERT INTO TESPIT_TURLERI (TESPIT_TURU,TESPIT_ACIKLAMA) VALUES (100,'İLETİŞİMİN TESPİTİ') ;
    INSERT INTO TESPIT_TURLERI (TESPIT_TURU,TESPIT_ACIKLAMA) VALUES (200,'MESAJ ALMA/ATMA') ;
    INSERT INTO TESPIT_TURLERI (TESPIT_TURU,TESPIT_ACIKLAMA) VALUES (300,'SADECE GPRS') ;
    INSERT INTO TESPIT_TURLERI (TESPIT_TURU,TESPIT_ACIKLAMA) VALUES (400,'SADECE ARAMA') ;
    INSERT INTO TESPIT_TURLERI (TESPIT_TURU,TESPIT_ACIKLAMA) VALUES (500,'SADECE ARANMA') ;
    INSERT INTO TESPIT_TURLERI (TESPIT_TURU,TESPIT_ACIKLAMA) VALUES (600,'SADECE BAZ') ;
    INSERT INTO TESPIT_TURLERI (TESPIT_TURU,TESPIT_ACIKLAMA) VALUES (700,'SADECE MESAJ ALMA') ;
    INSERT INTO TESPIT_TURLERI (TESPIT_TURU,TESPIT_ACIKLAMA) VALUES (800,'SADECE MESAJ ATMA') ;



  END IF;
END;
/

COMMIT;
