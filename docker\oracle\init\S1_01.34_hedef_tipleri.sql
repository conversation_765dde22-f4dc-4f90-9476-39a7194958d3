-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for HEDEF_TIPLERI if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'HEDEF_TIPLERI_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.HEDEF_TIPLERI_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create HEDEF_TIPLERI table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'HEDEF_TIPLERI';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.HEDEF_TIPLERI (
        ID NUMBER
           , HEDEF_KODU NUMBER NOT NULL
           , HEDEF_TIPI VARCHAR2(50 BYTE)
           , <PERSON>ONLANDIRMAMI CHAR(1 BYTE)
           , <PERSON><PERSON><PERSON><PERSON><PERSON>I NUMBER
           , SNO NUMBER
           , HEDEF_TANIM VARCHAR2(16 BYTE)
           , DURUM VARCHAR2(8 BYTE)
           , HITAP_TIP VARCHAR2(8 BYTE)
           , HITAP_ICERIK_TIP VARCHAR2(8 BYTE)
           , HITAP_ICINDEMI VARCHAR2(8 BYTE)
           , HITAP_EH CHAR(1 BYTE)
           , MINL NUMBER
           , MAXL NUMBER
           , IMHA_YAPILSINMI VARCHAR2(8 BYTE)
           , TASINABILIRMI VARCHAR2(1 BYTE)
           , AKTIFMI NUMBER DEFAULT 1
           , HITAPA_GONDERILECEKMI NUMBER(1, 0) DEFAULT 0
           , CONSTRAINT HEDEF_TIPLERI_PRM PRIMARY KEY (HEDEF_KODU)      ENABLE

    )';
    
    -- Create index
    EXECUTE IMMEDIATE 'CREATE INDEX iym.IND_HEDEF_TIPI_UPPER ON iym.HEDEF_TIPLERI (UPPER(HEDEF_TIPI) ASC)';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.HEDEF_TIPLERI;
  IF row_count = 0 THEN

        Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (15,51,'IP TAKIP',null,151,21,null,'UNIM','IP','xxCC','EVET','E',null,null,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (16,151,'IP T.SONLANDIRMA','S',51,22,null,'UNIM','IP','xxCC','EVET','E',null,null,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (1,10,'GSM',null,110,1,'GSM-GSM','H','MSISDN','CC','EVET','E',12,12,'EVET','E',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (2,20,'SABİT',null,120,31,'PSTN','H','MSISDN','CC','EVET','E',12,12,'EVET','E',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (3,30,'UYDU',null,130,13,'UYDU','H','MSISDN','CC','HAYIR','H',null,null,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (4,40,'YURT DIŞI',null,140,5,'Y.DIŞI','H','MSISDN','CC','HAYIR','H',null,null,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (5,50,'E-POSTA',null,150,17,'E-POSTA','UNIM','E-POSTA','xxCC','EVET','E',5,50,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (6,60,'IMEI',null,160,3,'GSM-IMEI','H','IMEI','CC','EVET','E',13,16,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (15,200,'GSMYer Tespiti',null,210,15,null,'Y',null,null,null,null,12,12,'EVET','H',1,0);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (16,210,'GSMYer Tespiti SONLANDIR','S',200,16,null,'Y',null,null,null,null,12,12,null,'H',1,0);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (7,70,'IMSI',null,170,11,'GSM-IMSI','H','IMSI','CC','EVET','E',null,null,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (8,110,'GSM-SONLANDIRMA','S',10,2,null,'H','MSISDN','CC','EVET','E',12,12,null,'E',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (9,120,'SABİT-SONLANDIRMA','S',20,32,null,'H','MSISDN','CC','EVET','E',12,12,null,'E',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (10,130,'UYDU-SONLANDIRMA','S',30,14,null,'H','MSISDN','CC','HAYIR','H',null,null,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (11,140,'YURT DIŞI-SONLANDIRMA','S',40,6,null,'H','MSISDN','CC','HAYIR','H',null,null,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (12,150,'E-POSTA-SONLANDIRMA','S',50,18,null,'UNIM','E-POSTA','xxCC','EVET','E',null,null,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (13,160,'IMEI-SONLANDIRMA','S',60,4,null,'H','IMEI','CC','EVET','E',13,16,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (14,170,'IMSI-SONLANDIRMA','S',70,12,null,'H','IMSI','CC','EVET','E',null,null,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (17,52,'URL-WEB ADRESI TAKIP',null,152,19,null,'UNIM','URL','xxCC','EVET','E',null,null,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (18,152,'URL-WEB T.SONLANDIRMA','S',52,20,null,'UNIM','URL','xxCC','EVET','E',null,null,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (19,53,'ADSL-ABONE TAKIP',null,153,23,'ADSL',null,null,null,null,null,10,12,'EVET','H',1,0);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (20,153,'ADSL-ABONE T.SONLANDIRMA','S',53,24,null,null,null,null,null,null,10,12,null,'H',1,0);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (21,54,'GPRS',null,154,9,'GSM-GPRS','I',null,null,null,null,12,12,'EVET','H',1,0);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (22,154,'GPRS-SONLANDIRMA','S',54,10,null,'I',null,null,null,null,null,null,null,'H',1,0);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (23,55,'IP ENGELLEME',null,155,null,null,'I',null,null,null,null,null,null,null,'H',1,0);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (24,155,'IP EN.SONLANDIR','S',55,null,null,'I',null,null,null,null,null,null,null,'H',1,0);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (25,56,'DOMAIN ENGELLEME',null,156,null,null,'I',null,null,null,null,null,null,null,'H',1,0);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (26,156,'DOMAIN EN.SONLANDIR','S',56,null,null,'I',null,null,null,null,null,null,null,'H',1,0);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (34,80,'TT-xDSL(MSISDN)',null,180,35,'xDSL','H','MSISDN','xCC','EVET','E',12,12,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (35,81,'TT-xDSL(TEMOSNO)',null,181,37,'xDSL','H','TEMOSNO','xCC','EVET','E',10,10,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (36,82,'TT-xDSL(USERNAME)',null,182,39,'xDSL','H','USERNAME','xCC','EVET','H',null,null,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (37,83,'TT-xDSL(IP)',null,183,41,'xDSL','H','IP','xCC','EVET','H',null,null,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (38,41,'UMTH-(MSISDN)',null,141,43,'UMTH',null,'MSISDN','CC','EVET','E',null,null,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (39,42,'UMTH-(USERNAME)',null,142,45,'UMTH',null,'USERNAME','CC','EVET','H',null,null,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (40,43,'UMTH-(IP)',null,143,47,'UMTH',null,'IP','CC','EVET','H',null,null,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (41,44,'UMTH-(PINCODE)',null,144,49,'UMTH',null,'PINCODE','CC','EVET','H',null,null,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (43,180,'TT-xDSL(MSISDN)-SONLAN.','S',80,36,'xDSL','H','MSISDN','xCC','EVET','E',12,12,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (44,181,'TT-xDSL(TEMOSNO)-SONLAN.','S',81,38,'xDSL','H','TEMOSNO','xCC','EVET','E',10,10,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (45,182,'TT-xDSL(USERNAME)-SONLAN.','S',82,40,'xDSL','H','USERNAME','xCC','EVET','H',null,null,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (46,183,'TT-xDSL(IP)-SONLAN.','S',83,42,'xDSL','H','IP','xCC','EVET','H',null,null,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (47,141,'UMTH-(MSISDN)-SONLAN.','S',41,44,'UMTH',null,'MSISDN','CC','EVET','E',null,null,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (48,142,'UMTH-(USERNAME)-SONLAN.','S',42,46,'UMTH',null,'USERNAME','CC','EVET','H',null,null,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (49,143,'UMTH-(IP)-SONLAN.','S',43,48,'UMTH',null,'IP','CC','EVET','H',null,null,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (50,144,'UMTH-(PINCODE)-SONLAN.','S',44,50,'UMTH',null,'PINCODE','CC','EVET','H',null,null,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (51,71,'GPRS-IMSI',null,171,33,null,'H','IMSI','GPRS','EVET','E',null,null,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (52,171,'GPRS-IMSI SONLANDIRMA','S',71,34,null,'H','IMSI','GPRS','EVET','E',null,null,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (27,90,'GPRS-GSM',null,190,25,'GSM-GPRS','H','MSISDN','GPRS','EVET','E',12,12,'EVET','E',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (28,91,'GPRS-IMEI',null,191,27,'GSM-GPRS','H','IMEI','GPRS','EVET','E',13,16,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (29,92,'GPRS-Y.DIŞI',null,192,29,'GSM-GPRS','H','MSISDN','GPRS','HAYIR','H',null,null,'EVET','H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (30,190,'GPRS-GSM SONLANDIRMA','S',90,26,null,'H','MSISDN','GPRS','EVET','E',12,12,null,'E',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (31,191,'GPRS-IMEI SONLANDIRMA','S',91,28,null,'H','IMEI','GPRS','EVET','E',13,16,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (32,192,'GPRS-Y.DIŞI SONLANDIRMA','S',92,30,null,'H','MSISDN','GPRS','HAYIR','H',null,null,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (53,99,'TRUNK',null,199,null,'TRUNK','H','TRUNK','CC','EVET','E',7,11,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (54,199,'TRUNK-SONLANDIRMA',null,99,null,null,'H','TRUNK','CC','EVET','E',7,11,null,'H',1,1);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (33,0,'Hedef Tipi Seçilmemiş',null,null,null,null,'Y',null,null,null,null,null,null,null,'H',1,0);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (55,201,'YURTDIŞI Yer Tespiti',null,211,55,null,'Y',null,null,null,null,12,12,'EVET','H',1,0);
          Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (56,211,'YURTDIŞI Yer Tespiti SON','S',201,56,null,'Y',null,null,null,null,12,12,null,'H',1,0);




  END IF;
END;
/

COMMIT;
