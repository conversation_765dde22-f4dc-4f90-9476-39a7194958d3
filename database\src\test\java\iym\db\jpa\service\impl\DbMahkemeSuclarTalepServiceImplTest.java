package iym.db.jpa.service.impl;

import iym.common.model.entity.iym.talep.MahkemeSuclarTalep;
import iym.db.jpa.dao.talep.MahkemeSuclarTalepRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DbMahkemeSuclarTalepServiceImplTest {

    @Mock
    private MahkemeSuclarTalepRepo mahkemeSuclarTalepRepo;

    @InjectMocks
    private DbMahkemeSuclarTalepServiceImpl dbMahkemeSuclarTalepService;

    private MahkemeSuclarTalep mahkemeSuclarTalep;
    private List<MahkemeSuclarTalep> mahkemeSuclarTalepList;

    @BeforeEach
    void setUp() {
        mahkemeSuclarTalep = MahkemeSuclarTalep.builder()
                .id(1L)
                .mahkemeKararTalepId(100L)
                .sucTipKodu("01")
                .durumu("AKTIF")
                .build();

        mahkemeSuclarTalepList = Arrays.asList(
                mahkemeSuclarTalep,
                MahkemeSuclarTalep.builder()
                        .id(2L)
                        .mahkemeKararTalepId(200L)
                        .sucTipKodu("02")
                        .durumu("PASIF")
                        .build()
        );
    }

    @Test
    void findByMahkemeKararId_shouldReturnMahkemeSuclarTalepList() {
        // Given
        when(mahkemeSuclarTalepRepo.findByMahkemeKararTalepId(100L)).thenReturn(mahkemeSuclarTalepList);

        // When
        List<MahkemeSuclarTalep> result = dbMahkemeSuclarTalepService.findByMahkemeKararTalepId(100L);

        // Then
        assertEquals(mahkemeSuclarTalepList, result);
        verify(mahkemeSuclarTalepRepo).findByMahkemeKararTalepId(100L);
    }



    @Test
    void findByMahkemeKararIdAndMahkemeSucTipKod_shouldReturnMahkemeSuclarTalep() {
        // Given
        when(mahkemeSuclarTalepRepo.findByMahkemeKararTalepIdAndSucTipKodu(100L, "01"))
                .thenReturn(Optional.of(mahkemeSuclarTalep));

        // When
        Optional<MahkemeSuclarTalep> result = dbMahkemeSuclarTalepService.findByMahkemeKararTalepIdAndSucTipKodu(100L, "01");

        // Then
        assertTrue(result.isPresent());
        assertEquals(mahkemeSuclarTalep, result.get());
        verify(mahkemeSuclarTalepRepo).findByMahkemeKararTalepIdAndSucTipKodu(100L, "01");
    }

    @Test
    void findById_shouldReturnMahkemeSuclarTalep() {
        // Given
        when(mahkemeSuclarTalepRepo.findById(1L)).thenReturn(Optional.of(mahkemeSuclarTalep));

        // When
        Optional<MahkemeSuclarTalep> result = dbMahkemeSuclarTalepService.findById(1L);

        // Then
        assertTrue(result.isPresent());
        assertEquals(mahkemeSuclarTalep, result.get());
        verify(mahkemeSuclarTalepRepo).findById(1L);
    }

    @Test
    void findAll_shouldReturnAllMahkemeSuclarTalep() {
        // Given
        when(mahkemeSuclarTalepRepo.findAll()).thenReturn(mahkemeSuclarTalepList);

        // When
        List<MahkemeSuclarTalep> result = dbMahkemeSuclarTalepService.findAll();

        // Then
        assertEquals(mahkemeSuclarTalepList, result);
        verify(mahkemeSuclarTalepRepo).findAll();
    }

    @Test
    void findAll_withPageable_shouldReturnPageOfMahkemeSuclarTalep() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<MahkemeSuclarTalep> mahkemeSuclarTalepPage = new PageImpl<>(mahkemeSuclarTalepList, pageable, mahkemeSuclarTalepList.size());
        
        when(mahkemeSuclarTalepRepo.findAll(pageable)).thenReturn(mahkemeSuclarTalepPage);

        // When
        Page<MahkemeSuclarTalep> result = dbMahkemeSuclarTalepService.findAll(pageable);

        // Then
        assertEquals(mahkemeSuclarTalepPage, result);
        verify(mahkemeSuclarTalepRepo).findAll(pageable);
    }

    @Test
    void save_shouldSaveMahkemeSuclarTalep() {
        // When
        dbMahkemeSuclarTalepService.save(mahkemeSuclarTalep);

        // Then
        verify(mahkemeSuclarTalepRepo).save(mahkemeSuclarTalep);
    }

    @Test
    void update_shouldUpdateMahkemeSuclarTalep() {
        // When
        dbMahkemeSuclarTalepService.update(mahkemeSuclarTalep);

        // Then
        verify(mahkemeSuclarTalepRepo).save(mahkemeSuclarTalep);
    }

    @Test
    void delete_shouldDeleteMahkemeSuclarTalep() {
        // When
        dbMahkemeSuclarTalepService.delete(mahkemeSuclarTalep);

        // Then
        verify(mahkemeSuclarTalepRepo).delete(mahkemeSuclarTalep);
    }

    @Test
    void deleteById_shouldDeleteMahkemeSuclarTalepById() {
        // When
        dbMahkemeSuclarTalepService.deleteById(1L);

        // Then
        verify(mahkemeSuclarTalepRepo).deleteById(1L);
    }
}
