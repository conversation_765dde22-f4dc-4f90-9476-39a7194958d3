package iym.common.service.db;

import iym.common.model.entity.iym.talep.MahkemeAidiyatDetayTalep;

import java.util.Date;
import java.util.List;

/**
 * Service interface for MahkemeAidiyatDetayTalep entity
 */
public interface DbMahkemeAidiyatDetayTalepService extends GenericDbService<MahkemeAidiyatDetayTalep, Long> {

    List<MahkemeAidiyatDetayTalep> findByMahkemeKararId(Long mahkemeKararId);

    List<MahkemeAidiyatDetayTalep> findByIliskiliMahkemeKararId(Long iliskiliMahkemeKararId);

    List<MahkemeAidiyatDetayTalep> findByMahkemeKararDetayId(Long mahkemeKararDetayId);

    List<MahkemeAidiyatDetayTalep> findByDurum(String durum);

    List<MahkemeAidiyatDetayTalep> findByTarihBetween(Date startDate, Date endDate);

}
