----------------<PERSON>H_<PERSON><PERSON><PERSON>_TIPLERI

CREATE INDEX IYM.IND_MAH_KRR_TPLR_KT ON IYM.MAH_KARAR_TIPLERI (KARAR_TIPI ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)CREATE TABLE IYM.MAH_KARAR_TIPLERI 
(
  ID NUMBER 
, <PERSON><PERSON><PERSON>_KODU NUMBER NOT NULL 
, <PERSON><PERSON><PERSON>_TIPI VARCHAR2(50 BYTE) 
, <PERSON>ARAR_TURU VARCHAR2(10 BYTE) 
, <PERSON><PERSON><PERSON>NDIRMAMI NUMBER(1, 0) DEFAULT 0 
, CONSTRAINT MAH_KAR_TIP_PRM PRIMARY KEY 
  (
    KARAR_KODU 
  )
  ENABLE 
) 

Insert into IYM.MAH_KARAR_TIPLERI (ID,<PERSON><PERSON><PERSON>_KODU,<PERSON><PERSON><PERSON>_TIPI,<PERSON><PERSON><PERSON>_TURU,SONLANDIRMAMI) values (30,410,'<PERSON><PERSON><PERSON> KHK YAZILI EMİR','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (31,730,'ADLİ KHK SONLANDIRMA','ADLI',1);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (1,100,'ÖNLEYİCİ-HAKİM KARARI','ONLEYICI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (2,200,'ÖNLEYİCİ-YAZILI EMİR','ONLEYICI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (3,300,'ADLİ-HAKİM KARARI','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (4,400,'ADLİ-YAZILI EMİR','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (5,500,'BİLGİ ALMA','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (10,530,'MAHKEME_AIDIYAT_DEGISTIRME','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (11,520,'MAHKEME_KODU_DEGISTIRME','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (12,510,'HEDEF AD/SOYAD DEGİSTİRME','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (13,810,'ADLI-ASKERI SAVCILIK KARARI','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (14,910,'ADLI-ASKERI SAVCILIK SONLANDIRMA','ADLI',1);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (15,820,'ADLI-ASKERI SAVCILIK YER TESP.','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (6,600,'ÖNLEYİCİ-SONLANDIRMA','ONLEYICI',1);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (7,700,'ADLİ-SONLANDIRMA','ADLI',1);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (16,920,'ADLI-ASKERI SAVCILIK YER TESP. SONLANDIRM','ADLI',1);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (17,550,'ADLI-ASKERI BILGI ALMA','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (18,560,'ADLI BILGI ALMA','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (19,570,'ONLEYICI BILGI ALMA','ONLEYICI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (20,310,'ADLI-SAVCILIK KARARI','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (21,710,'ADLI-SAVCILIK SONLANDIRMA','ADLI',1);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (22,320,'ADLI-SAVCILIK KARARI(YER TESP.)','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (23,720,'ADLI-SAVCILIK KARARI(YER TESP.) SONLANDIRMA','ADLI',1);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (8,800,'ADLİ-ASKERİ HAKİM KARARI','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (9,900,'ADLİ-ASKERİ SONLANDIRMA','ADLI',1);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (24,150,'SİNYAL BİLGİLERİNİN DEĞERLENDİRİLMESİ KARARI','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (26,350,'ADLİ-HAKİM İLETİŞİMİN TESPİTİ KARARI','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (25,151,'ABONE KÜTÜK VE BAZ KÜTÜK BİLGİLERİ KARARI','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (27,1000,'6532 KARARLARI','ONLEYICI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (28,2000,'TEST TANIMLAMA','ADLI',0);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (29,2100,'Test Sonlandırma','ADLI',1);
Insert into IYM.MAH_KARAR_TIPLERI (ID,KARAR_KODU,KARAR_TIPI,KARAR_TURU,SONLANDIRMAMI) values (32,450,'ADLİ-SAVCILIK İLETİŞİMİN TESPİTİ KARARI','ADLI',0);

_________________________



---------------HEDEF TIPLERİ


CREATE INDEX IYM.IND_HEDEF_TIPI_UPPER ON IYM.HEDEF_TIPLERI (UPPER(HEDEF_TIPI) ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)CREATE TABLE IYM.HEDEF_TIPLERI 
(
  ID NUMBER 
, HEDEF_KODU NUMBER NOT NULL 
, HEDEF_TIPI VARCHAR2(25 BYTE) 
, SONLANDIRMAMI CHAR(1 BYTE) 
, KARSILIGI NUMBER 
, SNO NUMBER 
, HEDEF_TANIM VARCHAR2(16 BYTE) 
, DURUM VARCHAR2(8 BYTE) 
, HITAP_TIP VARCHAR2(8 BYTE) 
, HITAP_ICERIK_TIP VARCHAR2(8 BYTE) 
, HITAP_ICINDEMI VARCHAR2(8 BYTE) 
, HITAP_EH CHAR(1 BYTE) 
, MINL NUMBER 
, MAXL NUMBER 
, IMHA_YAPILSINMI VARCHAR2(8 BYTE) 
, TASINABILIRMI VARCHAR2(1 BYTE) 
, AKTIFMI NUMBER DEFAULT 1 
, HITAPA_GONDERILECEKMI NUMBER(1, 0) DEFAULT 0 
, CONSTRAINT HEDEF_TIPLERI_PRM PRIMARY KEY 
  (
    HEDEF_KODU 
  )
  ENABLE 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL KEEP 
)
REM INSERTING into IYM.HEDEF_TIPLERI
SET DEFINE OFF;
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (15,51,'IP TAKIP',null,151,21,null,'UNIM','IP','xxCC','EVET','E',null,null,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (16,151,'IP T.SONLANDIRMA','S',51,22,null,'UNIM','IP','xxCC','EVET','E',null,null,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (1,10,'GSM',null,110,1,'GSM-GSM','H','MSISDN','CC','EVET','E',12,12,'EVET','E',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (2,20,'SABİT',null,120,31,'PSTN','H','MSISDN','CC','EVET','E',12,12,'EVET','E',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (3,30,'UYDU',null,130,13,'UYDU','H','MSISDN','CC','HAYIR','H',null,null,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (4,40,'YURT DIŞI',null,140,5,'Y.DIŞI','H','MSISDN','CC','HAYIR','H',null,null,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (5,50,'E-POSTA',null,150,17,'E-POSTA','UNIM','E-POSTA','xxCC','EVET','E',5,50,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (6,60,'IMEI',null,160,3,'GSM-IMEI','H','IMEI','CC','EVET','E',13,16,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (15,200,'GSMYer Tespiti',null,210,15,null,'Y',null,null,null,null,12,12,'EVET','H',1,0);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (16,210,'GSMYer Tespiti SONLANDIR','S',200,16,null,'Y',null,null,null,null,12,12,null,'H',1,0);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (7,70,'IMSI',null,170,11,'GSM-IMSI','H','IMSI','CC','EVET','E',null,null,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (8,110,'GSM-SONLANDIRMA','S',10,2,null,'H','MSISDN','CC','EVET','E',12,12,null,'E',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (9,120,'SABİT-SONLANDIRMA','S',20,32,null,'H','MSISDN','CC','EVET','E',12,12,null,'E',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (10,130,'UYDU-SONLANDIRMA','S',30,14,null,'H','MSISDN','CC','HAYIR','H',null,null,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (11,140,'YURT DIŞI-SONLANDIRMA','S',40,6,null,'H','MSISDN','CC','HAYIR','H',null,null,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (12,150,'E-POSTA-SONLANDIRMA','S',50,18,null,'UNIM','E-POSTA','xxCC','EVET','E',null,null,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (13,160,'IMEI-SONLANDIRMA','S',60,4,null,'H','IMEI','CC','EVET','E',13,16,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (14,170,'IMSI-SONLANDIRMA','S',70,12,null,'H','IMSI','CC','EVET','E',null,null,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (17,52,'URL-WEB ADRESI TAKIP',null,152,19,null,'UNIM','URL','xxCC','EVET','E',null,null,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (18,152,'URL-WEB T.SONLANDIRMA','S',52,20,null,'UNIM','URL','xxCC','EVET','E',null,null,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (19,53,'ADSL-ABONE TAKIP',null,153,23,'ADSL',null,null,null,null,null,10,12,'EVET','H',1,0);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (20,153,'ADSL-ABONE T.SONLANDIRMA','S',53,24,null,null,null,null,null,null,10,12,null,'H',1,0);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (21,54,'GPRS',null,154,9,'GSM-GPRS','I',null,null,null,null,12,12,'EVET','H',1,0);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (22,154,'GPRS-SONLANDIRMA','S',54,10,null,'I',null,null,null,null,null,null,null,'H',1,0);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (23,55,'IP ENGELLEME',null,155,null,null,'I',null,null,null,null,null,null,null,'H',1,0);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (24,155,'IP EN.SONLANDIR','S',55,null,null,'I',null,null,null,null,null,null,null,'H',1,0);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (25,56,'DOMAIN ENGELLEME',null,156,null,null,'I',null,null,null,null,null,null,null,'H',1,0);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (26,156,'DOMAIN EN.SONLANDIR','S',56,null,null,'I',null,null,null,null,null,null,null,'H',1,0);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (34,80,'TT-xDSL(MSISDN)',null,180,35,'xDSL','H','MSISDN','xCC','EVET','E',12,12,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (35,81,'TT-xDSL(TEMOSNO)',null,181,37,'xDSL','H','TEMOSNO','xCC','EVET','E',10,10,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (36,82,'TT-xDSL(USERNAME)',null,182,39,'xDSL','H','USERNAME','xCC','EVET','H',null,null,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (37,83,'TT-xDSL(IP)',null,183,41,'xDSL','H','IP','xCC','EVET','H',null,null,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (38,41,'UMTH-(MSISDN)',null,141,43,'UMTH',null,'MSISDN','CC','EVET','E',null,null,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (39,42,'UMTH-(USERNAME)',null,142,45,'UMTH',null,'USERNAME','CC','EVET','H',null,null,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (40,43,'UMTH-(IP)',null,143,47,'UMTH',null,'IP','CC','EVET','H',null,null,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (41,44,'UMTH-(PINCODE)',null,144,49,'UMTH',null,'PINCODE','CC','EVET','H',null,null,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (43,180,'TT-xDSL(MSISDN)-SONLAN.','S',80,36,'xDSL','H','MSISDN','xCC','EVET','E',12,12,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (44,181,'TT-xDSL(TEMOSNO)-SONLAN.','S',81,38,'xDSL','H','TEMOSNO','xCC','EVET','E',10,10,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (45,182,'TT-xDSL(USERNAME)-SONLAN.','S',82,40,'xDSL','H','USERNAME','xCC','EVET','H',null,null,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (46,183,'TT-xDSL(IP)-SONLAN.','S',83,42,'xDSL','H','IP','xCC','EVET','H',null,null,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (47,141,'UMTH-(MSISDN)-SONLAN.','S',41,44,'UMTH',null,'MSISDN','CC','EVET','E',null,null,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (48,142,'UMTH-(USERNAME)-SONLAN.','S',42,46,'UMTH',null,'USERNAME','CC','EVET','H',null,null,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (49,143,'UMTH-(IP)-SONLAN.','S',43,48,'UMTH',null,'IP','CC','EVET','H',null,null,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (50,144,'UMTH-(PINCODE)-SONLAN.','S',44,50,'UMTH',null,'PINCODE','CC','EVET','H',null,null,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (51,71,'GPRS-IMSI',null,171,33,null,'H','IMSI','GPRS','EVET','E',null,null,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (52,171,'GPRS-IMSI SONLANDIRMA','S',71,34,null,'H','IMSI','GPRS','EVET','E',null,null,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (27,90,'GPRS-GSM',null,190,25,'GSM-GPRS','H','MSISDN','GPRS','EVET','E',12,12,'EVET','E',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (28,91,'GPRS-IMEI',null,191,27,'GSM-GPRS','H','IMEI','GPRS','EVET','E',13,16,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (29,92,'GPRS-Y.DIŞI',null,192,29,'GSM-GPRS','H','MSISDN','GPRS','HAYIR','H',null,null,'EVET','H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (30,190,'GPRS-GSM SONLANDIRMA','S',90,26,null,'H','MSISDN','GPRS','EVET','E',12,12,null,'E',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (31,191,'GPRS-IMEI SONLANDIRMA','S',91,28,null,'H','IMEI','GPRS','EVET','E',13,16,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (32,192,'GPRS-Y.DIŞI SONLANDIRMA','S',92,30,null,'H','MSISDN','GPRS','HAYIR','H',null,null,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (53,99,'TRUNK',null,199,null,'TRUNK','H','TRUNK','CC','EVET','E',7,11,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (54,199,'TRUNK-SONLANDIRMA',null,99,null,null,'H','TRUNK','CC','EVET','E',7,11,null,'H',1,1);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (33,0,'Hedef Tipi Seçilmemiş',null,null,null,null,'Y',null,null,null,null,null,null,null,'H',1,0);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (55,201,'YURTDIŞI Yer Tespiti',null,211,55,null,'Y',null,null,null,null,12,12,'EVET','H',1,0);
Insert into IYM.HEDEF_TIPLERI (ID,HEDEF_KODU,HEDEF_TIPI,SONLANDIRMAMI,KARSILIGI,SNO,HEDEF_TANIM,DURUM,HITAP_TIP,HITAP_ICERIK_TIP,HITAP_ICINDEMI,HITAP_EH,MINL,MAXL,IMHA_YAPILSINMI,TASINABILIRMI,AKTIFMI,HITAPA_GONDERILECEKMI) values (56,211,'YURTDIŞI Yer Tespiti SON','S',201,56,null,'Y',null,null,null,null,12,12,null,'H',1,0);


-------------------------------


CREATE INDEX IYM.MAHKEME_KARAR_ISLEM_E_ID ON IYM.MAHKEME_KARAR_ISLEM (EVRAK_ID ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)
CREATE INDEX IYM.MAHKEME_KARAR_ISLEM_ID ON IYM.MAHKEME_KARAR_ISLEM (ID ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)CREATE TABLE IYM.MAHKEME_KARAR_ISLEM 
(
  ID NUMBER NOT NULL 
, EVRAK_ID NUMBER NOT NULL 
, KULLANICI_ID NUMBER NOT NULL 
, KAYIT_TARIHI DATE NOT NULL 
, DURUM VARCHAR2(20 BYTE) 
, HUKUK_BIRIM VARCHAR2(50 BYTE) 
, KARAR_TIP VARCHAR2(20 BYTE) 
, MAH_KARAR_BAS_TAR DATE 
, MAH_KARAR_BITIS_TAR DATE 
, MAHKEME_ADI VARCHAR2(250 BYTE) 
, MAHKEME_KARAR_NO VARCHAR2(50 BYTE) 
, MAHKEME_ILI NUMBER 
, ACIKLAMA VARCHAR2(500 BYTE) 
, HAKIM_SICIL_NO VARCHAR2(20 BYTE) 
, SORUSTURMA_NO VARCHAR2(50 BYTE) 
, ISLEM_TIPI VARCHAR2(10 BYTE) 
, MAHKEME_KARAR_ID NUMBER 
, ISLEM_NEDENI VARCHAR2(15 BYTE) 
, MAHKEME_KODU VARCHAR2(10 BYTE) 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)


------------------------


-- Unable to render TABLE DDL for object IYM.MAHKEME_KARAR_ITIRAZ with DBMS_METADATA attempting internal generator.
CREATE TABLE IYM.MAHKEME_KARAR_ITIRAZ 
(
  ID NUMBER NOT NULL 
, TALEP_KISI_ID NUMBER NOT NULL 
, TALEP_TARIHI DATE NOT NULL 
, TALEP_ACIKLAMA VARCHAR2(1000 BYTE) 
, ITIRAZ_EDILEN_EVRAK_ID NUMBER NOT NULL 
, ITIRAZ_EDILEN_KURUM VARCHAR2(10 BYTE) NOT NULL 
, DURUM VARCHAR2(100 BYTE) NOT NULL 
, ITIRAZ_NEDENI VARCHAR2(10 BYTE) NOT NULL 
, ISLEYEN_KISI_ID NUMBER 
, ISLEME_TARIHI DATE 
, ITIRAZ_YAZI_ID NUMBER 
, ISLEYEN_ACIKLAMA VARCHAR2(1000 BYTE) 
, ITIRAZA_CEVAP_EVRAK_ID NUMBER 
, BEKLEMEDE VARCHAR2(1 BYTE) 
, ARSIV VARCHAR2(1 BYTE) 
, ITIRAZ_SONUC VARCHAR2(20 BYTE) 
, ITIRAZ_SONUC_EVRAK_ID NUMBER 
, ITIRAZ_SONUC_TARIH DATE 
, ITIRAZ_SONUC_KAYIT_KISI_ID NUMBER 
, SILINME_NEDENI VARCHAR2(1000 BYTE) 
, SILINME_TARIHI DATE 
, SILEN_KISI_ID NUMBER 
, CONSTRAINT PK_MAHKEME_KARAR_ITIRAZ PRIMARY KEY 
  (
    ID 
  )
  ENABLE 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)



--------------------------------------

-- Unable to render TABLE DDL for object IYM.MAHKEME_KARAR_ATAMA with DBMS_METADATA attempting internal generator.
CREATE BITMAP INDEX IYM.IND_MAH_KAR_AT_KUL_ID ON IYM.MAHKEME_KARAR_ATAMA (KULLANICI_ID ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)
CREATE INDEX IYM.MAH_KAR_ATA_EVRAK_ID ON IYM.MAHKEME_KARAR_ATAMA (EVRAK_ID ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)
CREATE INDEX IYM.MAH_KAR_ATA_EVRAK_ID_KULL_ID ON IYM.MAHKEME_KARAR_ATAMA (EVRAK_ID ASC, KULLANICI_ID ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)CREATE TABLE IYM.MAHKEME_KARAR_ATAMA 
(
  ID NUMBER 
, EVRAK_ID NUMBER 
, KULLANICI_ID NUMBER 
, TARIH DATE 
, DURUM VARCHAR2(10 BYTE) 
, SEVIYE VARCHAR2(1 BYTE) DEFAULT 0 
, ACIKLAMA VARCHAR2(2000 BYTE) 
, SEBEBI NUMBER 
, GONDEREN_ID NUMBER 
, GONDERILEN_ID NUMBER 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)


------------------------------------------------------




-- Unable to render TABLE DDL for object IYM.DMAHKEME_KARAR with DBMS_METADATA attempting internal generator.
CREATE TABLE IYM.DMAHKEME_KARAR 
(
  MAHKEME_KARAR_ID NUMBER NOT NULL 
, EVRAK_ID NUMBER NOT NULL 
, KULLANICI_ID NUMBER NOT NULL 
, KAYIT_TARIHI DATE NOT NULL 
, DURUM VARCHAR2(20 BYTE) 
, KARAR_TIP_DETAY VARCHAR2(20 BYTE) 
, MAHKEME_KODU_DETAY VARCHAR2(10 BYTE) 
, MAHKEME_ADI_DETAY VARCHAR2(250 BYTE) 
, MAHKEME_KARAR_NO_DETAY VARCHAR2(50 BYTE) 
, MAHKEME_ILI_DETAY VARCHAR2(6 BYTE) 
, SORUSTURMA_NO_DETAY VARCHAR2(50 BYTE) 
, ILISKILI_MAHKEME_KARAR_ID NUMBER 
, ACIKLAMA_DETAY VARCHAR2(500 BYTE) 
, ID NUMBER 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)



_________________________

-- Unable to render TABLE DDL for object IYM.DMAHKEME_KARAR_ISLEM with DBMS_METADATA attempting internal generator.
CREATE INDEX IYM.IND_DMAH_KRR_IS_SNO ON IYM.DMAHKEME_KARAR_ISLEM (SORUSTURMA_NO_DETAY ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)
CREATE INDEX IYM.IND_MAH_KARARNO_DETAY ON IYM.DMAHKEME_KARAR_ISLEM (MAHKEME_KARAR_NO_DETAY ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)
CREATE INDEX IYM.IND_MAH_KOD_DETAY ON IYM.DMAHKEME_KARAR_ISLEM (MAHKEME_KODU_DETAY ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)
CREATE INDEX IYM.IND_MAH_TALEP_MKID ON IYM.DMAHKEME_KARAR_ISLEM (MAHKEME_KARAR_ID ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)CREATE TABLE IYM.DMAHKEME_KARAR_ISLEM 
(
  MAHKEME_KARAR_ID NUMBER NOT NULL 
, EVRAK_ID NUMBER NOT NULL 
, KULLANICI_ID NUMBER NOT NULL 
, KAYIT_TARIHI DATE NOT NULL 
, DURUM VARCHAR2(20 BYTE) 
, KARAR_TIP_DETAY VARCHAR2(20 BYTE) 
, MAHKEME_KODU_DETAY VARCHAR2(10 BYTE) 
, MAHKEME_ADI_DETAY VARCHAR2(250 BYTE) 
, MAHKEME_KARAR_NO_DETAY VARCHAR2(50 BYTE) 
, MAHKEME_ILI_DETAY VARCHAR2(6 BYTE) 
, SORUSTURMA_NO_DETAY VARCHAR2(50 BYTE) 
, ILISKILI_MAHKEME_KARAR_ID NUMBER 
, ACIKLAMA_DETAY VARCHAR2(500 BYTE) 
, ID NUMBER 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)


-------------------------------

-- Unable to render TABLE DDL for object IYM.MAHKEME_AIDIYAT_DETAY with DBMS_METADATA attempting internal generator.
CREATE TABLE IYM.MAHKEME_AIDIYAT_DETAY 
(
  ID NUMBER NOT NULL 
, ILISKILI_MAHKEME_KARAR_ID NUMBER 
, MAHKEME_KARAR_ID NUMBER 
, MAHKEME_AIDIYAT_KODU_EKLE VARCHAR2(25 BYTE) 
, MAHKEME_AIDIYAT_KODU_CIKAR VARCHAR2(25 BYTE) 
, TARIH DATE NOT NULL 
, DURUM VARCHAR2(25 BYTE) 
, MAHKEME_KARAR_DETAY_ID NUMBER 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)


---------------------------------


-- Unable to render TABLE DDL for object IYM.MAHKEME_AIDIYAT_DETAY_ISLEM with DBMS_METADATA attempting internal generator.
CREATE TABLE IYM.MAHKEME_AIDIYAT_DETAY_ISLEM 
(
  ID NUMBER NOT NULL 
, ILISKILI_MAHKEME_KARAR_ID NUMBER 
, MAHKEME_KARAR_ID NUMBER 
, MAHKEME_AIDIYAT_KODU_EKLE VARCHAR2(25 BYTE) 
, MAHKEME_AIDIYAT_KODU_CIKAR VARCHAR2(25 BYTE) 
, TARIH DATE NOT NULL 
, DURUM VARCHAR2(25 BYTE) 
, MAHKEME_KARAR_DETAY_ID NUMBER 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)


-------------------------------------------

-- Unable to render TABLE DDL for object IYM.MAHKEME_AIDIYAT_ISLEM with DBMS_METADATA attempting internal generator.
CREATE INDEX IYM.MAHKEME_AIDIYAT_ISLEM_M_ID ON IYM.MAHKEME_AIDIYAT_ISLEM (MAHKEME_ID ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)CREATE TABLE IYM.MAHKEME_AIDIYAT_ISLEM 
(
  ID NUMBER NOT NULL 
, MAHKEME_ID NUMBER NOT NULL 
, AIDIYAT_KOD VARCHAR2(25 BYTE) NOT NULL 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)


-----------------------------

,-- Unable to render TABLE DDL for object IYM.MAHKEME_HEDEFLER_AIDIYAT with DBMS_METADATA attempting internal generator.
CREATE INDEX IYM.M_IND_HED_AID_HED_ID ON IYM.MAHKEME_HEDEFLER_AIDIYAT (HEDEF_ID ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)
CREATE INDEX IYM.M_IND_HED_AID_MAH_ID ON IYM.MAHKEME_HEDEFLER_AIDIYAT (MAHKEME_KARAR_ID ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)ALTER TABLE IYM.MAHKEME_HEDEFLER_AIDIYAT
ADD CONSTRAINT M_HEDEF_AIDIYAT_HEDEFID_A_IDX UNIQUE 
(
  HEDEF_ID 
, AIDIYAT_KOD 
)
DISABLECREATE TABLE IYM.MAHKEME_HEDEFLER_AIDIYAT 
(
  ID NUMBER NOT NULL 
, HEDEF_ID NUMBER NOT NULL 
, AIDIYAT_KOD VARCHAR2(15 BYTE) NOT NULL 
, TARIH DATE NOT NULL 
, KULLANICI_ID NUMBER NOT NULL 
, MAHKEME_KARAR_ID NUMBER NOT NULL 
, CONSTRAINT M_HEDEF_AIDIYAT_ID_IDX PRIMARY KEY 
  (
    ID 
  )
  ENABLE 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)

----------------------------


-- Unable to render TABLE DDL for object IYM.MAHKEME_HEDEFLER_AIDIYAT_ISLEM with DBMS_METADATA attempting internal generator.
CREATE INDEX IYM.MI_IND_HED_AID_HED_ID ON IYM.MAHKEME_HEDEFLER_AIDIYAT_ISLEM (HEDEF_ID ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)
CREATE INDEX IYM.MI_IND_HED_AID_MAH_ID ON IYM.MAHKEME_HEDEFLER_AIDIYAT_ISLEM (MAHKEME_KARAR_ID ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)ALTER TABLE IYM.MAHKEME_HEDEFLER_AIDIYAT_ISLEM
ADD CONSTRAINT MI_HEDEF_AIDIYAT_HEDEFID_A_IDX UNIQUE 
(
  HEDEF_ID 
, AIDIYAT_KOD 
)
DISABLECREATE TABLE IYM.MAHKEME_HEDEFLER_AIDIYAT_ISLEM 
(
  ID NUMBER NOT NULL 
, HEDEF_ID NUMBER NOT NULL 
, AIDIYAT_KOD VARCHAR2(15 BYTE) NOT NULL 
, TARIH DATE NOT NULL 
, KULLANICI_ID NUMBER NOT NULL 
, MAHKEME_KARAR_ID NUMBER NOT NULL 
, DURUMU VARCHAR2(10 BYTE) 
, CONSTRAINT MI_HEDEF_AIDIYAT_ID_IDX PRIMARY KEY 
  (
    ID 
  )
  ENABLE 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)



-------------------------------------


-- Unable to render TABLE DDL for object IYM.HEDEFLER_DETAY_ISLEM with DBMS_METADATA attempting internal generator.
CREATE TABLE IYM.HEDEFLER_DETAY_ISLEM 
(
  ID NUMBER NOT NULL 
, HEDEF_NO VARCHAR2(100 BYTE) 
, HEDEF_TIPI NUMBER 
, HEDEF_ADI VARCHAR2(100 BYTE) 
, HEDEF_SOYADI VARCHAR2(100 BYTE) 
, KAYIT_TARIHI DATE 
, DURUMU VARCHAR2(50 BYTE) 
, ILISKILI_HEDEF_ID NUMBER 
, MAHKEME_KARAR_ID NUMBER 
, MAHKEME_KARAR_DETAY_ID NUMBER 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)


---------------------------

-- Unable to render TABLE DDL for object IYM.HEDEFLER_DETAY with DBMS_METADATA attempting internal generator.
CREATE TABLE IYM.HEDEFLER_DETAY 
(
  ID NUMBER NOT NULL 
, HEDEF_NO VARCHAR2(100 BYTE) 
, HEDEF_TIPI NUMBER 
, HEDEF_ADI VARCHAR2(100 BYTE) 
, HEDEF_SOYADI VARCHAR2(100 BYTE) 
, KAYIT_TARIHI DATE 
, DURUMU VARCHAR2(50 BYTE) 
, ILISKILI_HEDEF_ID NUMBER 
, MAHKEME_KARAR_ID NUMBER 
, MAHKEME_KARAR_DETAY_ID NUMBER 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)


----------------------------------


-- Unable to render TABLE DDL for object IYM.HEDEFLER_ISLEM with DBMS_METADATA attempting internal generator.
CREATE INDEX IYM.HEDEFLER_ISLEM_ID ON IYM.HEDEFLER_ISLEM (ID ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)
CREATE INDEX IYM.HEDEFLER_ISLEM_MAH_ID ON IYM.HEDEFLER_ISLEM (MAHKEME_KARAR_ID ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)CREATE TABLE IYM.HEDEFLER_ISLEM 
(
  ID NUMBER 
, BIRIM_KOD NUMBER 
, KULLANICI_ID NUMBER 
, TEK_MASA_KUL_ID NUMBER 
, HEDEF_NO VARCHAR2(100 BYTE) NOT NULL 
, HEDEF_TIPI VARCHAR2(20 BYTE) NOT NULL 
, HEDEF_ADI VARCHAR2(100 BYTE) 
, HEDEF_SOYADI VARCHAR2(100 BYTE) 
, BASLAMA_TARIHI DATE 
, SURESI NUMBER 
, SURE_TIPI NUMBER 
, UZATMA_SAYISI NUMBER 
, DURUMU VARCHAR2(100 BYTE) 
, ACIKLAMA VARCHAR2(250 BYTE) 
, MAHKEME_KARAR_ID NUMBER NOT NULL 
, HEDEF_AIDIYAT_ID NUMBER 
, GRUP_KOD NUMBER 
, AIDIYAT_KOD VARCHAR2(35 BYTE) 
, UNIQ_KOD NUMBER 
, KAYIT_TARIHI DATE 
, TANIMLAMA_TARIHI DATE 
, KAPATMA_KARAR_ID NUMBER 
, KAPATMA_TARIHI DATE 
, IMHA VARCHAR2(20 BYTE) 
, IMHA_TARIHI DATE 
, UZATMA_ID NUMBER 
, DEGISME_DURUMU VARCHAR2(500 BYTE) 
, OPERATOR VARCHAR2(30 BYTE) 
, ACILMI CHAR(1 BYTE) 
, HEDEF_118_ADI VARCHAR2(128 BYTE) 
, HEDEF_118_SOYADI VARCHAR2(128 BYTE) 
, HEDEF_118_ADRES VARCHAR2(512 BYTE) 
, CANAK_NO VARCHAR2(100 CHAR) 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)




-------------------------

--------------------------------------------------------
--  File created - Tuesday-July-29-2025   
--------------------------------------------------------
-- Unable to render TABLE DDL for object IYM.MAHKEME_ISLEM_TIP with DBMS_METADATA attempting internal generator.
CREATE TABLE IYM.MAHKEME_ISLEM_TIP 
(
  ISLEM_TIPI VARCHAR2(10 BYTE) 
, ISLEM_ADI VARCHAR2(128 BYTE) 
, ISLEM_ACIKLAMA VARCHAR2(200 BYTE) 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)
REM INSERTING into IYM.MAHKEME_ISLEM_TIP
SET DEFINE OFF;
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.16','SORUMLULUK ALANI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.17','YETKİSİZLİK VE GÖREVSİZLİK',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('NORMAL','NORMAL','rutin işlemler');
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('ITIRAZE.','ITIRAZ EDILDI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('YEMAHKEME','YAZ.EMİR-C.S.K.ONAMA',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('ITIRAZK.','ITIRAZ KABUL-DÜZELTME',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('ITIRAZR.','ITIRAZ RED',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('ITIRAZKS.','ITIRAZ KABUL-SONLANDIRMA',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('YEMAHKEMER','YAZ.EMİR-C.S.K.RED',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.01','SORUŞTURMA -KAYIT / DİNLEME/S.BİLGİSİ EKSİKLİĞİ ',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.02','KATOLOG DIŞI SUÇ',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.03','ŞÜPHELİ YADA SANIK DIŞI DİNLEME',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.04','SUÇ TÜRÜ BULUNMAMASI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.05','YAKALAMA AMACIYLA DİNLEME',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.06','ŞÜPHELİ KİMLİĞİNİN BULUNMAMASI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.07','ŞÜPHELİ KİMLİĞİNİN BULUNMAMASI, KATOLOG DIŞI SUÇ',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.08','KAYIT / DİNLEME/S.BİLGİSİ EKSİKLİĞİ',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.09','TANIKLIKTAN ÇEKİNEBİLECEK KİŞİLERİN DİNLENMESİ',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.10','MAĞDUR/MÜŞTEKİ DİNLEMESİ',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.11','KALDIRILAN MADDEYE İLİŞKİN DİNLEME',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.12','SÜRE BULUNMAMASI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.13','TASNİF DIŞI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.00','DİĞER',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.14','ŞORUŞTURMA NO YOK',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.15','SUC TIPI ACIK DEĞİL',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('I.09','UZATILAMADI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('I.10','SORUŞTURMA NO DEĞİŞİKLİĞİ VAR',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('I.11','İMZA/MÜHÜR',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('I.12','DİĞER',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.18','SORUŞTURMA NO DEĞİŞİKLİĞİ',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.21','ŞÜPHELİ SANIK DIŞI TALEP',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.22','UZATMA SÜRELERİNE UYULMAMASI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.20','SİNYAL BİLGİLERİNİN DEĞERLENDİRİLMESİ(KATALOG DIŞI)',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.19','KULLANICI T.C. KİMLİK NO. BULUNMAMASI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('I.01','EVRAK GÖRÜNMÜYOR',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('I.02','HEDEF EKSİK YADA HATALI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('I.03','MÜKERRER HEDEF',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('I.04','SORUŞTURMA NO HATALI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('I.05','MAHKEME KARAR NO HATALI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('I.06','HEDEF TİPİ HATALI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('I.07','SÜRE HATALI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('I.08','BAŞLAMA TARİHİ HATALI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('ITIRAZT.','ITIRAZ TALEP EDILDI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.23','ABONE UYUMLU DEĞİL',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.24','TEDBİR KOŞULLARININ BELİRTİLMEMESİ',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.25','SÜRE AŞIMI',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.26','ŞÜPHELİ YAKALAMA AMACININ BELİRTİLMEMESİ (GSM YER TESPİTİ TALEPLERİNDE)',null);
Insert into IYM.MAHKEME_ISLEM_TIP (ISLEM_TIPI,ISLEM_ADI,ISLEM_ACIKLAMA) values ('N.27','IMEI DİNLEME (ADLİ EVRAKLARDA)',null);




--------------------------------



-- Unable to render TABLE DDL for object IYM.MAHKEME_AIDIYAT with DBMS_METADATA attempting internal generator.
CREATE INDEX IYM.IDX_MAHAID_MAHID ON IYM.MAHKEME_AIDIYAT (MAHKEME_ID ASC) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)
CREATE UNIQUE INDEX IYM.MAH_AIDIYAT_ID ON IYM.MAHKEME_AIDIYAT (MAHKEME_ID ASC, AIDIYAT_KOD ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)
CREATE INDEX IYM.MAH_AIDIYAT_KOD_IDX ON IYM.MAHKEME_AIDIYAT (AIDIYAT_KOD ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)ALTER TABLE IYM.MAHKEME_AIDIYAT
ADD CONSTRAINT CONS_MAH_AIDI_ID_KOD UNIQUE 
(
  MAHKEME_ID 
, AIDIYAT_KOD 
)
ENABLECREATE TABLE IYM.MAHKEME_AIDIYAT 
(
  ID NUMBER NOT NULL 
, MAHKEME_ID NUMBER NOT NULL 
, AIDIYAT_KOD VARCHAR2(25 BYTE) NOT NULL 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)


_________________________





-- Unable to render TABLE DDL for object IYM.MAHKEME_SUCLAR_ISLEM with DBMS_METADATA attempting internal generator.
CREATE INDEX IYM.MAHKEME_SUCLAR_ISLEM_M_ID ON IYM.MAHKEME_SUCLAR_ISLEM (MAHKEME_KARAR_ID ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)CREATE TABLE IYM.MAHKEME_SUCLAR_ISLEM 
(
  ID NUMBER NOT NULL 
, MAHKEME_KARAR_ID NUMBER NOT NULL 
, MAHKEME_SUC_TIP_KOD VARCHAR2(10 BYTE) NOT NULL 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)


-- Unable to render TABLE DDL for object IYM.MAHKEME_SUCLAR_TALEP with DBMS_METADATA attempting internal generator.
CREATE UNIQUE INDEX IYM.MAH_SUCLAR_TALEP_IDX ON IYM.MAHKEME_SUCLAR_TALEP (MAHKEME_KARAR_ID ASC, MAHKEME_SUC_TIP_KOD ASC) 
NOLOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 2 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)CREATE TABLE IYM.MAHKEME_SUCLAR_TALEP 
(
  ID NUMBER NOT NULL 
, MAHKEME_KARAR_ID NUMBER NOT NULL 
, MAHKEME_SUC_TIP_KOD VARCHAR2(10 BYTE) NOT NULL 
, DURUMU VARCHAR2(20 BYTE) 
) 
LOGGING 
TABLESPACE "IYM" 
PCTFREE 10 
INITRANS 1 
STORAGE 
( 
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1 
  MAXEXTENTS 2147483645 
  BUFFER_POOL DEFAULT 
)

















