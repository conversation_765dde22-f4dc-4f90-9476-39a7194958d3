package iym.common.service.db.mk;

import iym.common.model.entity.iym.mk.MahkemeKararDetay;
import iym.common.service.db.GenericDbService;

import java.util.List;


public interface DbMahkemeKararDetayService extends GenericDbService<MahkemeKararDetay, Long> {

    List<MahkemeKararDetay> findByEvrakId(Long evrakId);

    List<MahkemeKararDetay> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

}
