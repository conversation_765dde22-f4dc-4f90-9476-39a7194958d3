package gov.tib.iym.mahkemekarar.xmlparse;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Connection;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.Enumeration;
import java.util.GregorianCalendar;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
import javax.servlet.http.HttpSession;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.TransformerFactoryConfigurationError;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;
import org.zkoss.util.media.Media;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.Desktop;
import org.zkoss.zk.ui.util.Clients;

import com.enterprisedt.net.ftp.FTPConnectMode;
import com.enterprisedt.net.ftp.FTPTransferType;
import com.enterprisedt.net.ftp.pro.ProFTPClient;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Image;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.PdfWriter;
import com.lowagie.text.pdf.RandomAccessFileOrArray;
import com.lowagie.text.pdf.codec.TiffImage;

import gov.tib.iym.mahkemekarar.base.model.HEDEF_TIPLERI;
import gov.tib.iym.mahkemekarar.base.model.HedefDurumPojo;
import gov.tib.iym.mahkemekarar.base.model.MahkemeKodlariPojo;
import gov.tib.iym.mahkemekarar.model.EvrakDurumSorguPojo;
import gov.tib.iym.mahkemekarar.model.HariciYazilarPojo;
import gov.tib.iym.mahkemekarar.model.HedeflerAidiyatTalepPojo;
import gov.tib.iym.mahkemekarar.model.HedeflerDetayTalepPojo;
import gov.tib.iym.mahkemekarar.model.HedeflerTalepPojo;
import gov.tib.iym.mahkemekarar.model.HtsHedeflerTalepPojo;
import gov.tib.iym.mahkemekarar.model.HtsMahkemeKararTalepPojo;
import gov.tib.iym.mahkemekarar.model.IllerPojo;
import gov.tib.iym.mahkemekarar.model.MahkemeAidiyatDetayTalepPojo;
import gov.tib.iym.mahkemekarar.model.MahkemeAidiyatTalepPojo;
import gov.tib.iym.mahkemekarar.model.MahkemeHedeflerAidiyatTalepPojo;
import gov.tib.iym.mahkemekarar.model.MahkemeKararDetayTalepPojo;
import gov.tib.iym.mahkemekarar.model.MahkemeKararTalepPojo;
import gov.tib.iym.mahkemekarar.model.MahkemeSuclarTalepPojo;
import gov.tib.iym.mahkemekarar.model.XmlEvrakPojo;
import gov.tib.iym.mahkemekarar.webservice.FileDownloader;
import gov.tib.iym.mahkemekarar.webservice.GelenEvrakSonuc;
import gov.tib.iym.model.Canli118LogPojo;
import gov.tib.iym.model.IymFilePojo;
import gov.tib.iym.model.IymYaziDagitimPojo;
import gov.tib.iym.model.IymYaziEkDosyaPojo;
import gov.tib.iym.model.IymYaziPojo;
import gov.tib.iym.model.LoginInfo;
import gov.tib.iym.model.ThreadPojo;
import gov.tib.iym.service.IymService;
import gov.tib.services.AuthenticationService;
import gov.tib.util.PropertyHelper;
import gov.tib.util.TemelIslemler;
import service.Log4jService;
import service.ServiceManager;


public   class Utility {
	
	private static final Logger logger = LoggerFactory.getLogger(Utility.class);
	
	public static final String seperator = File.separator;
	public static final String testContextPath = "/iym_test";
	public static final String testPath = seperator+"test";
	public static final String TEST = ServiceManager.getTestPath()+"";
	public static final String tempFolderPath =  PropertyHelper.getProperty("iym_temp_folder_path");
	
	//TODO: 2014'den 2015'e geçerken klasörler otomatik oluşturuldu fakat yetkisi yeterli olmadığı için sorun çıktı. İlgili klasörler için chmod 777 ile yetki verildi.
	//2016'da da kontrol edilmesi gerekir.
	
	public static final String ARSIV_XML_PATH =  seperator+"iym300"+seperator+"iym"+seperator+"arsivzipfiles"+seperator+TEST;
	public static final String EVRAK_FILES_PATH = seperator+"iym300"+seperator+"iym"+seperator+"files"+seperator+TEST;
	public static final String GELEN_EVRAK_ZIP_FILES_PATH = seperator+"iym300"+seperator+"iym"+seperator+"gelenevrakzipfiles"+seperator+TEST;
	public static final String DOGRU_XML_LOG_PATH = seperator+"iym300"+seperator+"iym"+seperator+"tmp"+seperator+TEST;
	public static final String HARICI_ZIP_FILES_PATH = seperator+"iym300"+seperator+"iym"+seperator+"haricizipfiles"+seperator+TEST;	
	
	
	/*
	public static final String ARSIV_XML_PATH =  PropertyHelper.getProperty("arsiv_xml_path");
	public static final String EVRAK_FILES_PATH = PropertyHelper.getProperty("evrak_files_path");
	public static final String GELEN_EVRAK_ZIP_FILES_PATH = PropertyHelper.getProperty("gelen_evrak_zip_files_path");
	public static final String DOGRU_XML_LOG_PATH = PropertyHelper.getProperty("xml_log_path");
	public static final String HARICI_ZIP_FILES_PATH = PropertyHelper.getProperty("harici_zip_files_path");
*/
	
	
	public static final String FTP_SERVER= PropertyHelper.getProperty("iym_ftp_server");//"***********";
	public static final String FTP_SERVER_KULLANICI_ADI = PropertyHelper.getProperty("iym_ftp_server_username"); //"ftpkurumlar";
	public static final String FTP_SERVER_SIFRE = PropertyHelper.getProperty("iym_ftp_server_password"); //"3vRak-1";

	private static final Pattern rfc2822Mail = Pattern
			.compile("^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$");

	private static final Pattern rfc2822User = Pattern
			.compile("^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?)");

	//private static Calendar tarih = Calendar.getInstance();
	
	

	public static IymService getIymService(){
		IymService serv = null; 
		BeanFactory factory = null;
		try{
			 serv = ServiceManager.getIymservice();//(IymService) ServiceManager.getService("iymservice");
			if(serv == null)
				throw new Exception();
		}catch(Exception e1){
			try {
				 serv = ServiceManager.getIymservice(); //(IymService) ServiceManager.getService3("iymservice");
	        } catch (BeansException e) {
	        }
		}
        return serv;
		
	}

	public static AuthenticationService getAuthService(){
		AuthenticationService serv = null; 
		BeanFactory factory = null;
		try{
			 serv = ServiceManager.getAuthenticationService();// (AuthenticationService) ServiceManager.getService("authenticationService");
			if(serv == null)
				throw new Exception();
		}catch(Exception e1){
			try {
				 serv =ServiceManager.getAuthenticationService();// (AuthenticationService) ServiceManager.getService2("authenticationService");
	        } catch (BeansException e) {
	        }
		}
        return serv;
		
	}
	

	public static Date addDay(Date tarih, int eklenecekGun) {
		Date date = new Date();
		Long eklenecekZaman = eklenecekGun * 1000 * 60 * 60 * 24l;
		date.setTime(tarih.getTime() + eklenecekZaman);
		return date;
	}

	public static Date addMonth(Date tarih, int eklenecekAy) {
		Calendar c = convertDateToCalendar(tarih);
		c.add(Calendar.MONTH, eklenecekAy);
		return c.getTime();
	}

	public static Date addTime(Date tarih, int zamanBirimi, int eklenecek) {
		Calendar c = convertDateToCalendar(tarih);
		c.add(zamanBirimi, eklenecek);
		return c.getTime();
	}

	// public static Date addMonthAndDay(Date basTarihi, int eklenecekAy, int
	// eklenecekGun){
	// Date sonlanmaTarihi = new Date();
	// Date guncelTarih = new Date();
	// int gecerliAy = guncelTarih.getMonth();
	// int ekZaman = eklenecekAy;
	// for(int s=1; s<=eklenecekAy; s++){
	// if(gecerliAy==0){
	//
	// }
	// }
	// sonlanmaTarihi.setTime(basTarihi.getTime());
	//
	//
	// return null;
	// }

	public static Calendar convertDateToCalendar(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		return cal;
	}

	public static Date createDate(int yil, int ay, int gun) {
		Calendar c = GregorianCalendar.getInstance();
		c.set(yil, ay - 1, gun, 0, 0, 0);
		return c.getTime();
	}

	public static Date createDate(int yil, int ay, int gun, int saat, int dak) {
		Calendar c = GregorianCalendar.getInstance();
		c.set(yil, ay - 1, gun, saat, dak);
		return c.getTime();
	}

	public static Date createDate(Date date, boolean trim) {
		Calendar c = GregorianCalendar.getInstance();
		c.setTime(date);
		if (trim) {
			c.set(c.HOUR, 0);
			c.set(c.MINUTE, 0);
			c.set(c.SECOND, 0);
		}
		return c.getTime();
	}

	public static Date createDateNoHour(Date date) {
		return new Date(date.getYear(), date.getMonth(), date.getDate());
	}

	public static Date createDateTime(String date) {
		if (isNullOrEmpty(date))
			return null;
		date = date.replace(".", "/");
		String ayrac = tarihAyraciniVer(date);
		String[] dizi = date.split(ayrac);
		if (dizi.length != 3)
			return null;
		String[] yilSaatDizi = dizi[2].split(" ");
		if (yilSaatDizi.length != 2)
			return null;
		String[] saatDizi = yilSaatDizi[1].split(":");
		if (saatDizi.length != 2)
			return null;

		return Utility.createDate(Integer.parseInt(yilSaatDizi[0]),
				Integer.parseInt(dizi[1]), Integer.parseInt(dizi[0]),
				Integer.parseInt(saatDizi[0]), Integer.parseInt(saatDizi[1]));

	}

	public static Date createDate(String date) {
		if (isNullOrEmpty(date))
			return null;
		if (date.length() == 16)
			return createDateTime(date);
		date = date.replace(".", "/");
		String ayrac = tarihAyraciniVer(date);
		String[] dizi = date.split(ayrac);
		if (dizi.length != 3)
			return null;
		return Utility.createDate(Integer.parseInt(dizi[2]),
				Integer.parseInt(dizi[1]), Integer.parseInt(dizi[0]));

	}

	
	private static String getDosyaBoyutu(double size){
		String boyut="";
		
		double bytes = size / 1024;
		double kB = 0;
		double mB = 0;
		double gB = 0;
		double tB = 0;
		if(bytes>=1024){
			 kB= bytes / 1024;
			if(kB>=1024){
				 mB = kB / 1024;
				if(mB>=1024){
					gB = mB / 1024;
					if(gB>=1024){
						tB = gB / 1024;
					}
				}
			}
		}
		/*if(tB>0)
			boyut = tB +" TB";
		else if(gB>0)
			boyut = gB + "GB";
		else if(mB>0)
			boyut = mB + "MB";*/
		if(kB>0)
			boyut = kB + " KB";
		else if(bytes>0)
			boyut = bytes + " B";
		return boyut;
	}
	
	public static String tarihAyraciniVer(String date) {
		for (char c : date.toCharArray()) {
			if (objToLong(c) != null)
				continue;
			else
				return objToString(c);
		}
		return null;
	}


	public static Date getArizaBaslangicTarihi(Date tarih, Date saat)
			throws ParseException {
		if (tarih == null)
			tarih = new Date(0, 0, 0);
		if (saat == null)
			saat = new Date(0, 0, 0, 0, 0, 0);
		Date date = new Date(tarih.getYear(), tarih.getMonth(),
				tarih.getDate(), saat.getHours(), saat.getMinutes());
		return date;
	}
	
	public static String nullTemizle(String girdi) {

		if (girdi == null)
			return "";
		else
			return girdi;
	}

	public static Long objToLong(Object obj) {
		try {
			return Long.valueOf(obj.toString());
		} catch (Exception e) {
			return null;
		}
	}

	public static Integer objToInt(Object obj) {
		try {
			return Integer.valueOf(obj.toString());
		} catch (Exception e) {
			return null;
		}
	}

	public static Long objToLong(Object obj, long def) {
		try {
			return Long.valueOf(obj.toString());
		} catch (Exception e) {
			return def;
		}
	}

	public static Integer objToInt(Object obj, int def) {
		try {
			return Integer.valueOf(obj.toString());
		} catch (Exception e) {
			return def;
		}
	}

	public static String objToString(Object obj) {
		try {
			return String.valueOf(obj.toString());
		} catch (Exception e) {
			return null;
		}
	}

	public static String objToString(Object obj, String defaultValue) {
		try {
			if (obj != null)
				return String.valueOf(obj);
			else
				return defaultValue;
		} catch (Exception e) {
			return defaultValue;
		}
	}

	public static boolean isNullOrEmpty(String str) {
		if (str == null)
			return true;
		if (str.isEmpty())
			return true;
		if (str.trim().isEmpty())
			return true;
		return false;
	}

	public static String aciklamaYaz(String str, int maxKarakterSayisi) {
		String aciklama = null;
		if (!Utility.isNullOrEmpty(str))
			if (str.length() > maxKarakterSayisi)
				aciklama = str.substring(0, maxKarakterSayisi);
			else
				aciklama = str;

		return aciklama;
	}

	public static List getAllSetFieldNames(Class c) {
		List lst = new ArrayList<String>();

		for (Field f : c.getDeclaredFields()) {
			if (f.getType().getName() == "java.util.Set")
				lst.add(f.getName());
		}
		if (c.getSuperclass() != null)
			lst.addAll(getAllSetFieldNames(c.getSuperclass()));

		return lst;
	}

	public static List<Field> getAllField(Class c) {
		List<Field> lstField = new ArrayList<Field>();
		if (c == null)
			return lstField;
		for (Field f : c.getDeclaredFields()) {
			lstField.add(f);
		}
		lstField.addAll(getAllField(c.getSuperclass()));
		return lstField;
	}

	public static List<Field> getAllField(Class c, String packageName) {
		List<Field> lstField = new ArrayList<Field>();
		if (c == null)
			return lstField;
		for (Field f : c.getDeclaredFields()) {
			if (f.getType().getName().indexOf(packageName) > -1)
				lstField.add(f);
		}
		lstField.addAll(getAllField(c.getSuperclass(), packageName));
		return lstField;
	}

	public static List<String> getAllFieldNames(Class c, String packageName) {
		List<String> lstField = new ArrayList<String>();
		if (c == null)
			return lstField;
		for (Field f : c.getDeclaredFields()) {
			if (f.getType().getName().indexOf(packageName) > -1)
				lstField.add(f.getName());
		}
		lstField.addAll(getAllFieldNames(c.getSuperclass(), packageName));
		return lstField;
	}

	public static List<Field> getAllFieldExceptSets(Class c) {
		List<Field> lstField = new ArrayList<Field>();
		if (c == null)
			return lstField;
		for (Field f : c.getDeclaredFields()) {
			if (f.getType().getName() != "java.util.Set")
				lstField.add(f);
		}
		lstField.addAll(getAllFieldExceptSets(c.getSuperclass()));
		return lstField;
	}

	public static String firstCharToUpperCaseCumle(String str, boolean birlestir) {
		if (Utility.isNullOrEmpty(str)) {
			return str;
		}

		String rt = "";

		for (String s : str.split(" ")) {
			if (birlestir)
				rt += firstCharToUpperCase(s);
			else
				rt += " " + firstCharToUpperCase(s);
		}
		if (!rt.equals("")) {
			if (birlestir)
				return rt;
			else
				return rt.substring(1);
		} else
			return rt;
	}

	public static String firstCharToUpperCase(String s) {
		if (s.length() > 1)
			return s.substring(0, 1).toUpperCase().equals("İ") ? "I"
					+ s.substring(1) : s.substring(0, 1).toUpperCase()
					+ s.substring(1);
		else if (s.length() == 1)
			return s.toUpperCase();
		else
			return s;
	}

	public static String tarihFormati(Date date, boolean saatOlsunmu) {
		String tarih = "";
		SimpleDateFormat format;
		if (saatOlsunmu)
			format = new SimpleDateFormat("dd.MM.yyyy HH:mm:ss");
		else
			format = new SimpleDateFormat("dd.MM.yyyy");
		tarih = format.format(date);
		return tarih;
	}

	public static String padRight(String str, int size, char padChar) {

		while (str.length() < size) {
			str += padChar;
		}
		return str;
	}

	public static String padLeft(String str, int size, char padChar) {
		while (str.length() < size) {
			str = padChar + str;
		}
		return str;
	}

	public static int getGuncelYil() {
		return  Calendar.getInstance().get(Calendar.YEAR);
	}
	
	public static String getGuncelAy() {
		int ay = Calendar.getInstance().get(Calendar.MONTH)+1;
		if(ay < 10)
			return "0"+ay;
		return ""+ay;
	}
	
	public static String getGuncelGun() {
		int gun = Calendar.getInstance().get(Calendar.DAY_OF_MONTH);
		if(gun < 10)
			return "0"+gun;
		return ""+gun;
	}
	
	
	public static String getGuncelZaman() {
		String gun = getGuncelGun();
		String ay =getGuncelAy();
		String yil =""+getGuncelYil();
		
		String saat= ""+ Calendar.getInstance().get(Calendar.HOUR);
		String dakika= ""+Calendar.getInstance().get(Calendar.MINUTE);
		String saniye= ""+Calendar.getInstance().get(Calendar.SECOND);
		String milisaniye= ""+Calendar.getInstance().get(Calendar.MILLISECOND);
		
		return ""+gun+"."+ay+"."+yil+" "+saat+":"+dakika+":"+saniye+":"+milisaniye;
	}

	public static byte[] getFileBytes(String fileYolu) {
		try {
			List<Byte> rtList = new ArrayList<Byte>();

			InputStream inputStream = new FileInputStream(new File(fileYolu));

			byte[] buffer = new byte[1024];
			int len;
			while ((len = inputStream.read(buffer)) > 0) {
				for (int i = 0; i < len; i++) {
					rtList.add(buffer[i]);
				}
			}
			inputStream.close();

			byte[] rtArray = new byte[rtList.size()];

			for (int i = 0; i < rtList.size(); i++) {
				rtArray[i] = rtList.get(i).byteValue();
			}
			return rtArray;

		} catch (Exception e) {
			return new byte[0];
		}
	}

	public static void copyInputStream(InputStream in, OutputStream out)
			throws IOException {
		byte[] buffer = new byte[1024];
		int len;
		while ((len = in.read(buffer)) > 0) {
			out.write(buffer, 0, len);
		}
		in.close();
		out.close();
	}
	
	public static boolean isUppercaseCharacter(String text){
		boolean upperFound = false;
		
		for(char c: text.toCharArray()){
			if(Character.isUpperCase(c)){
				upperFound = true;
				break;
			}
		}
		return upperFound;
	}

	private static void dosyaninKlasorleriniOlustur(String dosyaYolu) {
		String s = File.separator;
		String klasorYolu = dosyaYolu.substring(0, dosyaYolu.lastIndexOf(s));
		File f = new File(klasorYolu);
		if (!f.exists()) {
			try {
				f.mkdir();
			} catch (Exception e) {
				f.mkdirs();
			}
		}
	}


	public static String convertXMLFileToString(String fileName) {
		try {
			DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory
					.newInstance();
			InputStream inputStream = new FileInputStream(new File(fileName));
			Document doc = documentBuilderFactory.newDocumentBuilder().parse(
					inputStream);
			StringWriter stw = new StringWriter();
			Transformer serializer = TransformerFactory.newInstance()
					.newTransformer();
			serializer.transform(new DOMSource(doc), new StreamResult(stw));
			return stw.toString();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public static <T> boolean listedeObjVarmi(List<T> lst, T obj) {
		if (lst == null)
			return false;
		if (obj.getClass().isEnum()) {
			for (T t : lst) {
				if (t == obj)
					return true;
			}
		} else if (obj.getClass() == String.class) {
			for (T t : lst) {
				if (obj.equals(t))
					return true;
			}
		} else {
			try {
				Method mtd = obj.getClass().getMethod("getOBJID");
				if (mtd != null) {
					for (T t : lst) {
						if (objToLong(mtd.invoke(t)).equals(
								objToLong(mtd.invoke(obj)))
								&& !objToLong(mtd.invoke(t)).equals(0l))
							return true;
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return false;
	}

	public static <T> boolean setteObjVarmi(Set<T> lst, T obj) {
		if (lst == null)
			return false;
		if (obj.getClass().isEnum()) {
			for (T t : lst) {
				if (t == obj)
					return true;
			}
		} else {
			try {
				Method mtd = obj.getClass().getMethod("getOBJID");
				for (T t : lst) {
					if (objToLong(mtd.invoke(t)).equals(
							objToLong(mtd.invoke(obj)))
							&& !objToLong(mtd.invoke(t)).equals(0l))
						return true;
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return false;
	}

	public static <T> List<T> convertSetToList(Set<T> st) {
		List<T> lst = new ArrayList<T>();
		Iterator<T> i = st.iterator();
		while (i.hasNext()) {
			lst.add(i.next());
		}
		return lst;
	}

	public static <T> Set<T> convertListToSet(List<T> lst) {
		Set<T> st = new HashSet<T>();
		for (T t : lst) {
			st.add(t);
		}
		return st;
	}

	private static Field getField(Class c, String FieldName) {
		Field fld;
		try {
			fld = c.getDeclaredField(FieldName);
		} catch (Exception e) {
			fld = null;
		}
		if (fld != null)
			return fld;
		else
			return getField(c.getSuperclass(), FieldName);
	}

	private static Method getMethod(Class c, String MethodName) {
		Method mtd;
		try {
			mtd = c.getDeclaredMethod(MethodName);
		} catch (Exception e) {
			mtd = null;
		}
		if (mtd != null)
			return mtd;
		else
			return getMethod(c.getSuperclass(), MethodName);
	}

	public static String getOBJIDFromSet(Set set) {
		String rt = "";
		for (Object o : set) {
			try {
				rt += "_"
						+ objToString(getMethod(o.getClass(), "getOBJID")
								.invoke(o), "");
			} catch (Exception e) {
			}
		}
		if (rt.length() > 0)
			return rt.substring(1);
		else
			return rt;
	}

	public static String getOzellikFromSet(Set set, String ozellikAdi) {
		String rt = "";
		for (Object o : set) {
			try {
				rt += "_"
						+ objToString(
								getMethod(
										o.getClass(),
										"get"
												+ firstCharToUpperCase(ozellikAdi))
										.invoke(o), "");
			} catch (Exception e) {
			}
		}
		if (rt.length() > 0)
			return rt.substring(1);
		else
			return rt;
	}

	public static <T> List<T> sortList(List<T> lst, String propertyName,
			boolean desc) {
		try {
			Method mtd;

			int size = lst.size();
			for (int i = 0; i < size; i++) {
				if (size - 1 == i) {
					i = -1;
					size = size - 1;
				} else {
					mtd = getMethod(lst.get(i).getClass(), "get"
							+ firstCharToUpperCase(propertyName));

					Object o1 = mtd.invoke(lst.get(i));
					Object o2 = mtd.invoke(lst.get(i + 1));

					long l1 = 0;
					long l2 = 0;

					String s1 = null;
					String s2 = null;

					if (Integer.class.isInstance(o1)) {
						l1 = objToLong(o1).longValue();
						l2 = objToLong(o2).longValue();
					} else if (String.class.isInstance(o1)) {
						s1 = objToString(o1);
						s2 = objToString(o2);
					}

					if (Integer.class.isInstance(o1)) {
						if (l1 > l2 && !desc) {
							lst.add(i, lst.get(i + 1));
							lst.remove(i + 2);
						} else if (l1 < l2 && desc) {
							lst.add(i, lst.get(i + 1));
							lst.remove(i + 2);
						}
					} else if (String.class.isInstance(o1)) {
						if (s1.compareTo(s2) > 0 && !desc) {
							lst.add(i, lst.get(i + 1));
							lst.remove(i + 2);
						} else if (s1.compareTo(s2) < 0 && desc) {
							lst.add(i, lst.get(i + 1));
							lst.remove(i + 2);
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return lst;
	}

	
	public static void saveToFile(InputStream stream, String uploadedFileLocation) {
		OutputStream out = null;
		int read = 0;
		byte[] bytes= new byte[1024];
		try {
			File f = new File(uploadedFileLocation);
			if(!f.exists()){
				f.createNewFile();
			}
			out = new FileOutputStream(f);
			while((read = stream.read(bytes)) != -1){
				out.write(bytes,0,read);	
			}
			out.flush();
			out.close();
		} catch (FileNotFoundException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
	}
	public static boolean imeiDogrula(String imei) {
		if (imei.length() == 15 || imei.length() == 14)
			if (karakterDiziniSayiMi(imei))
				return true;
			else
				return false;
		else {
			return false;
		}
	}

	public static boolean gsmDogrula(String gsm) {
		if (gsm.length() != 12 || !gsm.substring(0, 2).equals("90"))
			return false;
		else {
			if (karakterDiziniSayiMi(gsm))
				return true;
			else
				return false;
		}
	}
	
	public static boolean htsGsmDogrula(String gsm) {
		if (gsm.length() != 10 || gsm.substring(0, 2).equals("90"))
			return false;
		else {
			if (karakterDiziniSayiMi(gsm))
				return true;
			else
				return false;
		}
	}
	private static boolean karakterDiziniSayiMi(String str) {
		char[] cArray = str.toCharArray();
		for (int i = 0; i < cArray.length; i++) {
			if (!Character.isDigit(str.charAt(i))) {
				return false;
			}
		}
		return true;
	}

	public static <T> List<T> createArrayList(Object... pars) {

		List<T> lst = new ArrayList<T>();

		for (Object o : pars) {
			if (o != null)
				lst.add((T) o);
		}

		return lst;

	}

	public static String xmlToString(Document doc)
			throws TransformerFactoryConfigurationError, TransformerException {
		DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory
				.newInstance();
		StringWriter stw = new StringWriter();
		Transformer serializer = TransformerFactory.newInstance()
				.newTransformer();
		serializer.transform(new DOMSource(doc), new StreamResult(stw));
		return stw.toString();
	}

	public static <t> void detachObjFromSession(Class<t> c, long objId) {

		// ApplicationContext.getCurrentEntityManager().detach(ApplicationContext.getCurrentEntityManager().find(c,
		// objId));
	}

	public static String toUpper(String str) {
		String s = "";
		for (char c : str.toCharArray()) {
			if (c == 'i') {

				c = 'I';

				s += c;
			} else {
				s += objToString(c).toUpperCase();
			}

		}
		return s;
	}

	public static String hashOlustur(String plainText) throws Exception {

		String str = strToSHA1(plainText);
		String desKey = "b3e8a4e6312f3fc1214f8e9c";
		String algorithm = "DESede";
		String transformation = "DESede/ECB/NoPadding";
		byte[] keyValue = desKey.getBytes("UTF-8");
		DESedeKeySpec keySpec = new DESedeKeySpec(keyValue);
		SecretKey key = SecretKeyFactory.getInstance(algorithm).generateSecret(
				keySpec);
		Cipher encrypter = Cipher.getInstance(transformation);
		encrypter.init(Cipher.ENCRYPT_MODE, key);
		byte[] input = getPaddedBytes(str);
		byte[] encrypted = encrypter.doFinal(input);
		return strToSHA1(toHexString(encrypted));
	}

	public static String strToSHA1(String str)
			throws UnsupportedEncodingException {

		// Console console = System.console();
		MessageDigest md = null;
		String javaenc = "Cp1254";
		try {
			md = MessageDigest.getInstance("SHA1");

		} catch (NoSuchAlgorithmException e1) {
		}
		byte[] strByte = md.digest(str.getBytes(javaenc));

		String encodedStrInString = toHexString(strByte);

		return encodedStrInString;
	}

	public static String fileToSHA1(String yol) throws Exception {
		MessageDigest md = null;
		String javaenc = "Cp1254";
		try {
			md = MessageDigest.getInstance("SHA1");
			BufferedInputStream in = new BufferedInputStream(
					new FileInputStream(yol));

			int theByte = 0;
			while ((theByte = in.read()) != -1) {
				md.update((byte) theByte);
			}

			in.close();
		} catch (NoSuchAlgorithmException e1) {
		}
		byte[] strByte = md.digest();

		String encodedStrInString = Utility.toHexString(strByte);

		return encodedStrInString;
	}

	private static String toHexString(byte[] buf) {
		char[] hexChar = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
				'a', 'b', 'c', 'd', 'e', 'f' };

		StringBuffer strBuf = new StringBuffer(buf.length * 2);
		for (int i = 0; i < buf.length; i++) {
			strBuf.append(hexChar[(buf[i] & 0xf0) >>> 4]);
			strBuf.append(hexChar[buf[i] & 0x0f]);
		}
		return strBuf.toString();
	}

	public static byte[] getPaddedBytes(String s)
			throws UnsupportedEncodingException {
		int n = s.length();
		n = n + (8 - (n % 8));
		byte[] src = s.getBytes("UTF-8");
		byte[] dst = Arrays.copyOf(src, n);
		return src;
	}

	public static String toEnglishChar(String in) {
		StringBuilder sb = new StringBuilder();

		for (char c : in.toCharArray()) {
			switch (c) {
			case 'İ':
				sb.append('I');
				break;
			case 'ı':
				sb.append('i');
				break;
			case 'Ş':
				sb.append('S');
				break;
			case 'ş':
				sb.append('s');
				break;
			case 'Ğ':
				sb.append('G');
				break;
			case 'ğ':
				sb.append('g');
				break;
			case 'ü':
				sb.append('u');
				break;
			case 'Ü':
				sb.append('U');
				break;
			case 'ö':
				sb.append('o');
				break;
			case 'Ö':
				sb.append('O');
				break;
			case 'Ç':
				sb.append('C');
				break;
			case 'ç':
				sb.append('c');
				break;
			default:
				sb.append(c);
			}
		}
		return sb.toString();
	}

	public static String getNewEvrakDosyalariKlasoru(long evrakObjId) {
		String s = File.separator;
		SimpleDateFormat format = new SimpleDateFormat("yyyy" + s + "MM" + s
				+ "dd");

		// return "IYM" + s + "YAZI" + s + format.format(new Date()) + s +
		// evrak.getOBJID();
		return format.format(new Date()) + s + evrakObjId;
	}

	public static boolean createDirs(String dosyaYolu) throws UygulamaHatasi {

		
		System.out.println("createDirs : 1");
		System.out.println("createDirs : 1-->" + dosyaYolu);
		
		dosyaYolu = dosyaYolu.replace("\\", File.separator);

		if (dosyaYolu.indexOf(".") >= 0) {
			try{
				dosyaYolu = dosyaYolu.substring(0,
						dosyaYolu.lastIndexOf(File.separator));
			}catch(Exception e){
				dosyaYolu = dosyaYolu.substring(0,
						dosyaYolu.lastIndexOf("/"));
			}
		}

		new File(dosyaYolu).mkdirs();

		return true;

	}

	public static void removeDirs(String klasorYolu, String korunacakKlasorYolu) {
		klasorYolu = trimRight(klasorYolu.replace("\\", File.separator),
				File.separator);
		korunacakKlasorYolu = trimRight(
				korunacakKlasorYolu.replace("\\", File.separator),
				File.separator);

		String tmpKlasorYolu = klasorYolu.replace(File.separator, "/");
		String tmpKorunacakKlasorYolu = korunacakKlasorYolu.replace(
				File.separator, "/");

		if (klasorYolu.indexOf(".") >= 0) {
			klasorYolu = klasorYolu.substring(0,
					klasorYolu.lastIndexOf(File.separator));
			tmpKlasorYolu = tmpKlasorYolu.substring(0,
					tmpKlasorYolu.lastIndexOf("/"));
		}
		String[] klasorDizisi = tmpKlasorYolu.split("/");
		for (int i = klasorDizisi.length - 1; i >= 0; i--) {
			if (!tmpKlasorYolu.equals(tmpKorunacakKlasorYolu)) {
				new File(klasorYolu).delete();
				klasorYolu = trimRight(
						trimRight(trimRight(klasorYolu, File.separator),
								klasorDizisi[i]), File.separator);
				tmpKlasorYolu = klasorYolu.replace(File.separator, "/");
			} else
				break;
		}
	}

	
	public static String trim(String str, String s) {
		if (isNullOrEmpty(str) || str.length() < s.length())
			return str;
		if (str.indexOf(s) == 0)
			str = str.substring(s.length());
		if (isNullOrEmpty(str) || str.length() < s.length())
			return str;
		if (str.lastIndexOf(s) == str.length() - s.length())
			str = str.substring(0, str.length() - s.length());
		if (str.indexOf(s) != 0
				&& str.lastIndexOf(s) != str.length() - s.length())
			return str;
		else
			return trim(str, s);
	}

	public static String trimRight(String str, String s) {
		if (isNullOrEmpty(str) || str.length() < s.length())
			return str;
		if (str.lastIndexOf(s) == str.length() - s.length())
			str = str.substring(0, str.length() - s.length());
		if (str.indexOf(s) != 0
				&& str.lastIndexOf(s) != str.length() - s.length())
			return str;
		else
			return trimRight(str, s);
	}



	public static long calendarDiff(Calendar bas, Calendar bit) {
		return bit.getTimeInMillis() - bas.getTimeInMillis();
	}

	public static long milliSecondToOthers(int tarihTipi, long milliSecond) {
		switch (tarihTipi) {
		case Calendar.SECOND:
			return milliSecond / 1000;
		case Calendar.MINUTE:
			return milliSecond / (1000 * 60);
		case Calendar.HOUR:
			return milliSecond / (1000 * 60 * 60);
		case Calendar.DATE:
			return milliSecond / (1000 * 60 * 60 * 24);
		default:
			return 0;
		}

	}

	public static boolean rakamMi(String str) {
		for (int i = 0; i < str.length(); i++) {
			if (!Character.isDigit(str.charAt(i)))
				return false;
		}
		return true;
	}

	public static Element getRootElement(File xmlFile) throws UygulamaHatasi {

		DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
		DocumentBuilder builder = null;
		Document document = null;
		try {
			builder = factory.newDocumentBuilder();
		} catch (ParserConfigurationException e) {
			// hatalar.append("DOMPARSERFACTORY CONF : " +
			// e.getLocalizedMessage());
			throw new UygulamaHatasi("DOMPARSERFACTORY CONF : "
					+ e.getLocalizedMessage());
		}
		try {
			document = builder.parse(xmlFile);
		} catch (IOException e) {
			// hatalar.append("DOMPARSER IOE  : " + e.getLocalizedMessage());
			// return null;
			throw new UygulamaHatasi("DOMPARSER IOE  : "
					+ e.getLocalizedMessage());
		} catch (SAXException e) {
			// hatalar.append("DOMPARSER SAXE : " + e.getLocalizedMessage());
			// return null;
			throw new UygulamaHatasi("DOMPARSER SAXE : "
					+ e.getLocalizedMessage());
		}
		Element root = document.getDocumentElement();

		return root;
	}

	public static String getStringValue(Element containerElement,
			String elementAdi) throws UygulamaHatasi {
		Element el = getElement(containerElement, elementAdi);

		if (el != null)
			return el.getFirstChild().getNodeValue();
		else
			return "";
	}

	public static List<String> getStringValues(Element containerElement,
			String elementAdi) throws UygulamaHatasi {
		List<Element> els = getElements(containerElement, elementAdi);
		List<String> rt = null;
		if (els != null) {
			for (Element el : els) {
				if (rt == null)
					rt = new ArrayList<String>();

				rt.add(el.getFirstChild().getNodeValue());
			}
		}

		return rt;
	}

	public static Element getElement(Element containerElement, String elementAdi)
			throws UygulamaHatasi {

		List<Element> rtElementList = getElements(containerElement, elementAdi);

		if (rtElementList == null)
			return null;
		else if (rtElementList.size() > 1)
			throw new UygulamaHatasi("Aranan element biren fazla bulundu");

		return rtElementList.get(0);
	}

	public static List<Element> getElements(Element containerElement,
			String elementAdi) {
		NodeList nList = containerElement.getElementsByTagName(elementAdi);
		List<Element> rtElementList = null;
		for (int i = 0; i < nList.getLength(); i++) {
			if (nList.item(i).getNodeName().equals(elementAdi)) {
				if (rtElementList == null)
					rtElementList = new ArrayList<Element>();
				rtElementList.add((Element) nList.item(i));
			}
		}
		return rtElementList;
	}

	public static String clearTextFromHtmlTag(String htmlText) {
		return htmlText.replaceAll("<(.*?)*>", "");
	}

	public static boolean emailDogrula(String hedefNo) {
		boolean sonuc = true;
		if (!rfc2822Mail.matcher(hedefNo).matches())
			sonuc = false;

		return sonuc;

	}

	public static boolean ipDogrula(String hedefNo) {
		final String IPADDRESS_PATTERN = "^([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\."
				+ "([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\."
				+ "([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\."
				+ "([01]?\\d\\d?|2[0-4]\\d|25[0-5])$";

		Pattern pattern = Pattern.compile(IPADDRESS_PATTERN);
		Matcher matcher = pattern.matcher(hedefNo);
		return matcher.matches();

	}
	
	public static boolean macDogrula(String hedefNo) {
		final String IPADDRESS_PATTERN = "[0-9A-F]{4}.[0-9A-F]{4}.[0-9A-F]{4}";
		Pattern pattern = Pattern.compile(IPADDRESS_PATTERN);
		Matcher matcher = pattern.matcher(hedefNo);
		return matcher.matches();

	}

	public static boolean yurtdisiDogrula(String hedefNo) {
		boolean sonuc = true;
		/*Yurt disi hedef nolar 15 den buyuk olmalı-12-11-2013*/
		if(hedefNo.length()>15)
			sonuc = false;
		
		if (hedefNo.startsWith("0") || hedefNo.startsWith("90")
				|| hedefNo.startsWith("90850")) {
			sonuc = false;
		}
		return sonuc;
	}

	public static boolean userDogrula(String hedefNo) {
		boolean sonuc = true;
		if (!rfc2822User.matcher(hedefNo).matches())
			sonuc = false;

		return sonuc;

	}

	public static boolean tarihDogrula(String dateToValidate) {
		boolean sonuc1 = tarihDogrula(dateToValidate, "dd/mm/yyyy");
		boolean sonuc2 = tarihDogrula(dateToValidate, "dd.mm.yyyy");
		boolean sonuc3 = tarihDogrula(dateToValidate, "dd-mm-yyyy");
		if (sonuc1 || sonuc2 || sonuc3)
			return true;
		return false;
	}
	
	
	public static boolean htsTarihDogrula(String dateToValidate) {
		boolean sonuc1 = tarihDogrula(dateToValidate, "dd/MM/yyyy HH:mm");
		boolean sonuc2 = tarihDogrula(dateToValidate, "dd.MM.yyyy HH:mm");
		boolean sonuc3 = tarihDogrula(dateToValidate, "dd-MM-yyyy HH:mm");
		boolean sonuc4 = tarihDogrula(dateToValidate+" 00:00", "dd/MM/yyyy HH:mm");
		boolean sonuc5 = tarihDogrula(dateToValidate+" 00:00", "dd.MM.yyyy HH:mm");
		boolean sonuc6 = tarihDogrula(dateToValidate+" 00:00", "dd-MM-yyyy HH:mm");
		if (sonuc1 || sonuc2 || sonuc3)
			return true;
		else if(sonuc4 || sonuc5 || sonuc6)
			return true;
		return false;
	}

	private static boolean tarihDogrula(String dateToValidate, String dateFromat) {

		if (dateToValidate == null) {
			return false;
		}

		SimpleDateFormat sdf = new SimpleDateFormat(dateFromat);
		sdf.setLenient(false);

		try {
			Date date = sdf.parse(dateToValidate);

		} catch (ParseException e) {
			return false;
		}
		return true;
	}
	
	public static String xmlDosyasininYolunuVer(String anaKlasorYolu) {
		System.out.println("xmlDosyasininYolunuVer" + anaKlasorYolu);
		File fAna = new File(anaKlasorYolu);
		if(fAna.exists()){
			for (File f : fAna.listFiles()) {
				if (f.isDirectory()) {
					return xmlDosyasininYolunuVer(f.getPath());
				}
				if (f.getPath().substring(f.getPath().lastIndexOf('.') + 1)
						.toUpperCase().equals("XML"))
					return f.getPath();
			}
		}
		return "";
	}

	
	private static void addtoZipFile(String fileName, ZipOutputStream zipfile) {		
		try {
			File file = new File(fileName);
			FileInputStream fis = new FileInputStream(file);
			ZipEntry zipEntry = new ZipEntry(fileName);
			zipfile.putNextEntry(zipEntry);
			
			byte[] bytes = new byte[1024];
			int length=0;
			while(((length = fis.read(bytes)))>=0){
				zipfile.write(bytes);
				
			}
			zipfile.closeEntry();
			fis.close();
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		
		
	}

	public static String zipAc(String zipFileYolu, String acilacakYol) {
		String tempKalasorUID = UUID.randomUUID().toString().toUpperCase();
		String acilanYol = null;
		Enumeration entries;
		ZipFile zipFile;
		String s = File.separator;
		try {
			zipFile = new ZipFile(zipFileYolu);

			entries = zipFile.entries();
			File f;
			FileOutputStream fos;
			String entryName = "";
			while (entries.hasMoreElements()) {
				ZipEntry entry = (ZipEntry) entries.nextElement();
				entryName = entry.getName().replace("/", s);
				if (entry.isDirectory()) {
					f = new File(acilacakYol + s + entryName);
					if (!f.exists()) {
						if (entry.getName().replace(s, "#").split("#").length > 1)
							f.mkdirs();
						else
							f.mkdir();
					}
					continue;
				} else {
					acilanYol = acilacakYol + s + tempKalasorUID + s ;
					dosyaninKlasorleriniOlustur(acilanYol + entryName);
					fos = new FileOutputStream(acilanYol + entryName,true);
					copyInputStream(zipFile.getInputStream(entry), fos);
				}
			}
			zipFile.close();
		} catch (IOException ioe) {
			ioe.printStackTrace();
			return null;
		}
		return acilanYol;
	}


	public static String createTempFolder(Desktop desktop) {
		
		return createTempFolder();

		/*
		ClassLoader loader = Thread.currentThread().getContextClassLoader();
		String tempKalasorUID = UUID.randomUUID().toString().toUpperCase();
		
		String sessionFolder = null;
		try {
			HttpSession ses = (HttpSession) desktop.getSession()
					.getNativeSession();
			
			//sessionFolder = loader.getResource("tempFolder").getFile().toString()+ Utility.seperator + ses.getId()+"_"+tempKalasorUID;			

			sessionFolder = ses.getServletContext().getRealPath("/")+ "tempFolder"+ Utility.seperator + ses.getId()+"_"+tempKalasorUID;			

			File folder = new File(sessionFolder);
			if (!folder.exists()) {
				folder.mkdirs();
			}
		} catch (Exception e1) {	
			e1.printStackTrace();
			return null;
		}
		return sessionFolder;
		*/
	}
	
	
	public static String createTempFolder() {

		String tempKalasorUID = UUID.randomUUID().toString().toUpperCase();
		Calendar cal = Calendar.getInstance();
		
		String sessionFolder = null;
		try {			
			int monthIndex = cal.get(Calendar.MONTH) + 1;
			sessionFolder = Utility.tempFolderPath + Utility.seperator + cal.get(Calendar.YEAR) + Utility.seperator + monthIndex + Utility.seperator + cal.get(Calendar.DAY_OF_MONTH) + Utility.seperator +tempKalasorUID;			
			
			System.out.println("createTempFolder : " + sessionFolder);
			File folder = new File(sessionFolder);
			if (!folder.exists()) {
				folder.mkdirs();
			}
		} catch (Exception e1) {	
			e1.printStackTrace();
			return null;
		}
		return sessionFolder;
	}
	
	
	
	public static ArrayList<IymFilePojo> dosyaIndir(Desktop desktop,Media media) {

		String sessionFolder = Utility.createTempFolder(desktop);
		
		ArrayList<IymFilePojo> files = new ArrayList<IymFilePojo>();
		try {
			if (media != null) {
				IymFilePojo file = new IymFilePojo();
					try {
					byte[] uploadedFile;
					if (media.isBinary()) {
						uploadedFile = IOUtils.toByteArray(media.getStreamData());
					} else {
						uploadedFile = IOUtils.toByteArray(media.getStringData());
					}

					OutputStream out = new FileOutputStream(sessionFolder
							+ "/" + media.getName());
					out.write(uploadedFile);
					out.close();
					file.setFileName(media.getName());
					file.setPath(sessionFolder);
					file.setType(media.getContentType());

					System.out.println("dosyaIndır:" + media.getName()
							+ "  " + sessionFolder + " "
							+ media.getContentType());
					files.add(file);
				} catch (Exception e) {
					e.printStackTrace();
				}
				}

		} catch (Exception e) {
			e.printStackTrace();
		}
		return files;
	}
	
	public static boolean evrakDosyasiUzantiKontrol(String uzanti){
		if(uzanti.equalsIgnoreCase("jpg")||uzanti.equalsIgnoreCase("jpeg")||
				uzanti.equalsIgnoreCase("tIff")||uzanti.equalsIgnoreCase("tIf")||
				uzanti.equalsIgnoreCase("tif")||uzanti.equalsIgnoreCase("tiff"))
			return true;
		return false;
	}
	public static boolean dosyalarTamamMiKontrol(String klasorYolu) {
		boolean xmlVarMi=false;
		boolean evrakVarMi=false;
		
		File f = new File(klasorYolu);
		if(f.isDirectory()){
			String icerik[] = f.list();
			if(icerik != null || icerik.length != 0){
				for(String dosya:icerik){
					File file = new File(dosya);
					int dopPos = file.getName().lastIndexOf(".");
					String uzanti = file.getName().substring(dopPos+1);
					if(uzanti.equalsIgnoreCase("xml")){
						xmlVarMi=true;
					}else if(evrakDosyasiUzantiKontrol(uzanti)){
						evrakVarMi=true;
					}
				}
			}
		}
		return xmlVarMi&&evrakVarMi;
	}
	
	public static boolean klasorIcerikCopy(String kaynak, String hedef)  {
		File f = new File(kaynak);
		if(f.isDirectory()){
			try {
				createDirs(hedef);
				String icerik[] = f.list();
				if(icerik != null || icerik.length != 0){
					for(String dosya:icerik){
						File inputFile = new File(dosya);
						File outputFile = new File(hedef);
						InputStream is = new FileInputStream(kaynak+seperator+inputFile);
						OutputStream os = new FileOutputStream(outputFile+seperator+inputFile);
						Utility.copyInputStream(is, os);
					}
				}
			} catch (Exception e) {
				return false;
			}
		}
		return true;
	}
	
	public static boolean fileMove(String kaynak, String hedef)
			throws Exception {
		File inputFile = new File(kaynak);
		createDirs(hedef);
		File outputFile = new File(hedef);

		InputStream is = new FileInputStream(inputFile);
		OutputStream os = new FileOutputStream(outputFile);

		Utility.copyInputStream(is, os);

		try {
			inputFile.delete();
		} catch (Exception e) {
			throw new Exception("HATA  Kod : [HATA-6-01] Lütfen BTK ile irtibata geçiniz!");
		}
		return true;
	}

	public static boolean fileCopy(String kaynak, String hedef)
			throws Exception {
		File inputFile = new File(kaynak);
		createDirs(hedef);
		File outputFile = new File(hedef);
		//System.out.println("kaynak : " + kaynak + " - hedef : " + hedef);
		InputStream is = new FileInputStream(inputFile);
		OutputStream os = new FileOutputStream(outputFile);

		Utility.copyInputStream(is, os);

		return true;
	}

	public static boolean xmlEvrakiTempKlasoreTasi(Connection con,String kaynakKlasor,String anaKlasor,Long personelId,Long evrakId,Long mahkemeKararTalepId,String clientIp,Long islemId) throws Exception {
		String kaynak=Utility.DOGRU_XML_LOG_PATH;
		File f = new File(kaynakKlasor);
		int sspos = kaynakKlasor.lastIndexOf(Utility.seperator);
		String folder = kaynakKlasor.substring(sspos+1);
		if(f.isDirectory()){
			String icerik[] = f.list();
			if(icerik != null || icerik.length != 0){
				for(String dosya:icerik){
					int spos = dosya.lastIndexOf(".");
					String uzanti = dosya.substring(spos+1);
					if(spos == -1){
						File d = new File(kaynakKlasor+Utility.seperator+dosya);
						if(d.isDirectory()){
							if(!klasorIcerikCopy(kaynakKlasor+Utility.seperator+dosya, kaynak+Utility.seperator+getGuncelYil()+Utility.seperator+getGuncelAy()+Utility.seperator+folder+Utility.seperator+anaKlasor+Utility.seperator)){
								throw new Exception("HATA  Kod : [HATA-7-01] Lütfen BTK ile irtibata geçiniz!");
							}else{
								klasorFtple(kaynak+Utility.seperator+getGuncelYil()+Utility.seperator+getGuncelAy()+Utility.seperator+folder+Utility.seperator+anaKlasor+Utility.seperator,"tmp");
							}	
						}
					}else{
						if(!fileCopy(kaynakKlasor+Utility.seperator+dosya,kaynak+Utility.seperator+getGuncelYil()+Utility.seperator+getGuncelAy()+Utility.seperator+folder+Utility.seperator+dosya)){
							throw new Exception("HATA  Kod : [HATA-7-02] Lütfen BTK ile irtibata geçiniz!");
						}else{
							ftpKlasorKontrol(kaynak+Utility.seperator+getGuncelYil()+Utility.seperator+getGuncelAy()+Utility.seperator+folder+Utility.seperator+dosya,"tmp");
							ftpGonder(kaynak+Utility.seperator+getGuncelYil()+Utility.seperator+getGuncelAy()+Utility.seperator+folder+Utility.seperator+dosya, kaynak+Utility.seperator+getGuncelYil()+Utility.seperator+getGuncelAy()+Utility.seperator+folder+Utility.seperator+dosya);
						}
					}

				}
			}
		}
		IymService srv = getIymService();
		boolean sonuc = srv.dogruXmlLogKaydet(con,kaynak+Utility.seperator+getGuncelYil()+Utility.seperator+getGuncelAy()+Utility.seperator+folder+Utility.seperator+anaKlasor,personelId,evrakId,mahkemeKararTalepId,clientIp,islemId);
		return sonuc;
	}

	public static boolean gelenEvrakZipFilesTasi(Connection con, String kaynakKlasor,String anaKlasor, String evrakSiraNo) throws Exception {
		String kaynak= Utility.GELEN_EVRAK_ZIP_FILES_PATH+Utility.seperator+getGuncelYil()+Utility.seperator+getGuncelAy()+Utility.seperator;
		File f = new File(kaynakKlasor);
		
		System.out.println("gelenEvrakZipFilesTasi: 1");
		
		if(f.isDirectory()){
			String icerik[] = f.list();
			if(icerik != null || icerik.length != 0){
				
				System.out.println("gelenEvrakZipFilesTasi: 2");
				
				for(String dosya:icerik){
					int spos = dosya.lastIndexOf(".");
					String uzanti = dosya.substring(spos+1);
					if(uzanti.equalsIgnoreCase("zip")){
						
						System.out.println("gelenEvrakZipFilesTasi: 3");
						
						if(!fileCopy(kaynakKlasor+Utility.seperator+dosya, kaynak+evrakSiraNo+"."+uzanti)){
							throw new Exception("HATA  Kod : [HATA-6-01] Lütfen BTK ile irtibata geçiniz!");
						}else{
							
							System.out.println("gelenEvrakZipFilesTasi: 4");
							
							ftpKlasorKontrol(kaynak+evrakSiraNo+"."+uzanti,"gelenevrakzipfiles");
							ftpGonder(kaynak+evrakSiraNo+"."+uzanti, kaynak+evrakSiraNo+"."+uzanti);
							
							System.out.println("gelenEvrakZipFilesTasi: 1-->" + kaynak);
							System.out.println("gelenEvrakZipFilesTasi: 1-->" + evrakSiraNo);
							System.out.println("gelenEvrakZipFilesTasi: 1-->" + uzanti);
							return true;
						}
					}
				}
			}
		}
		return true;
	}

	
	private static boolean evrakFtpdenAl(String ekPath, long kullaniciId) {
		try {
			 createDirs(ekPath);
			 evrakFtpAl(ekPath,ekPath);
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	public static boolean evrakFtpdenAl(String ekPath,String hedefPath) {
		try {
			createDirs(hedefPath);
			evrakFtpAl(ekPath,hedefPath);
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}

	
	public static boolean tempXmlFtpAl(String hedefPath) {
		try {
			createDirs(hedefPath);
			String xmlDosya="";
			String[] icerikListesi =ftpKlasorIcerikListesi(hedefPath,"tmp");
			for(String icerik : icerikListesi){
				int dPos = icerik.lastIndexOf(".");
				String uzanti = icerik.substring(dPos+1);
				System.out.println("UZANTI : "+uzanti);
				if(uzanti.equalsIgnoreCase("xml")){
					xmlDosya = icerik;
				}
			}
			System.out.println("TEMP XML YOLU : "+hedefPath+seperator+xmlDosya);
			evrakFtpAl(hedefPath+seperator+xmlDosya,hedefPath+seperator+xmlDosya);
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	public static boolean evrakFtple(Connection con , String kaynakKlasor,String anaKlasor,String evrakSiraNo,Long evrakId,Long personelId,String clientIp,Long islemId) throws Exception  {
		IymService srv = getIymService();
		String guncelAyYil = getGuncelYil()+Utility.seperator+getGuncelAy()+Utility.seperator;
		String kaynak=Utility.EVRAK_FILES_PATH+Utility.seperator+guncelAyYil;
		int i=0;
		File f = new File(kaynakKlasor+Utility.seperator+anaKlasor);
		
		System.out.println("evrakFtple: 1");
		
		if(f.exists() && f.isDirectory()){
			String[] icerik = f.list();
			if(icerik != null || icerik.length != 0){
				for(String dosya:icerik){
					int spos = dosya.lastIndexOf(".");
					String uzanti = dosya.substring(spos+1);
					
					System.out.println("evrakFtple: 2");
					
					if(evrakDosyasiUzantiKontrol(uzanti)){
						i++;
						try {
							
							System.out.println("evrakFtple: 3");
							
							if(!fileMove(kaynakKlasor+Utility.seperator+anaKlasor+Utility.seperator+dosya, kaynak+evrakSiraNo+"_"+i+"."+uzanti)){
								throw new Exception("HATA  Kod : [HATA-5-01] Lütfen BTK ile irtibata geçiniz!");
							}
						} catch (Exception e) {
							e.printStackTrace();
							return false;
						}
						try {
							
							System.out.println("evrakFtple: 4");
							
							evrakFtpGonder(evrakSiraNo,guncelAyYil, kaynak, i, uzanti);
						} catch (Exception e) {
							e.printStackTrace();
							return false;
						}
						
						boolean sonuc = srv.evrakFilesKaydet(con,guncelAyYil+evrakSiraNo+"_"+i+"."+uzanti,evrakId,Long.parseLong(new Integer(i).toString()),personelId,clientIp,islemId);
						if(!sonuc){
							
							System.out.println("evrakFtple: 5");
							
							throw new Exception("HATA  Kod : [HATA-5-04] Lütfen BTK ile irtibata geçiniz!");
						}
					}
				}
			}else
				throw new Exception("HATA  Kod : [HATA-5-05] Lütfen BTK ile irtibata geçiniz!");
		}else
			 throw new Exception("HATA  Kod : [HATA-5-06] Lütfen BTK ile irtibata geçiniz!");
		return true;
	}

	private static void evrakFtpGonder(String evrakSiraNo,String guncelAyYil,String kaynak, int i, String uzanti) throws Exception {
		String hedefYol = kaynak+Utility.seperator+evrakSiraNo+"_"+i+"."+uzanti;
		ftpKlasorKontrol(hedefYol, "files");
		ProFTPClient ftp =null;
		try {
			
			System.out.println("FTP_SERVER" + Utility.FTP_SERVER);
			
			ftp = new ProFTPClient();
			ftp.setRemoteHost(Utility.FTP_SERVER);
			ftp.connect();
			if(ftp.connected()){
				ftp.login(Utility.FTP_SERVER_KULLANICI_ADI, Utility.FTP_SERVER_SIFRE);
				ftp.setType(FTPTransferType.BINARY);
				ftp.setConnectMode(FTPConnectMode.PASV);
				System.out.println("KAYNAK : "+kaynak+evrakSiraNo+"_"+i+"."+uzanti);
				System.out.println("HEDEF : "+ hedefYol);
				ftp.put(kaynak+evrakSiraNo+"_"+i+"."+uzanti,hedefYol);
				ftp.quit();
			}else{
				throw new Exception("HATA  Kod : [HATA-5-02] Lütfen BTK ile irtibata geçiniz!");
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception("HATA  Kod : [HATA-5-03] Lütfen BTK ile irtibata geçiniz!");
		}finally{
		}
	}
	
	
	public static void klasorFtple(String hedefKlasorYol,String klasorAdi) {
		ftpKlasorKontrol(hedefKlasorYol,klasorAdi);
		System.out.println("HEDEF1 KLASOR:  "+hedefKlasorYol+ " " + klasorAdi );
		try {
			File f = new File(hedefKlasorYol);
			if(f.exists()){
				String[] klasorIcerik = f.list();
				for(String dosya : klasorIcerik){
					System.out.println("HEDEF1 DOSYA:  :  "+dosya );
					File k = new File(hedefKlasorYol+seperator+dosya);
					if(k.isDirectory()){
						klasorFtple(hedefKlasorYol+seperator+dosya, klasorAdi);
					}else{
						ftpGonder(hedefKlasorYol+seperator+dosya,hedefKlasorYol+seperator+dosya);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	
	private static boolean evrakFtpAl(String kaynak,String hedef) throws Exception {
		ProFTPClient ftp =null;
		byte[] file = null;
		try {
			ftp = new ProFTPClient();
			ftp.setRemoteHost(Utility.FTP_SERVER);
			ftp.connect();
			if(ftp.connected()){
				ftp.login(Utility.FTP_SERVER_KULLANICI_ADI, Utility.FTP_SERVER_SIFRE);
				ftp.setType(FTPTransferType.BINARY);
				ftp.setConnectMode(FTPConnectMode.PASV);
				System.out.println("KAYNAK : "+kaynak);
				System.out.println("HEDEF : "+hedef);
				ftp.get(kaynak,hedef);
				ftp.quit();
				return true;
			}else{
				
			}
		} catch (Exception e) {
			e.printStackTrace();
			
		}finally{
		}
		
		return false;
	}
	
	
	private static String[] ftpKlasorIcerikListesi(String hedef,String klasorAdi) throws Exception {
		ProFTPClient ftp =null;
		String[] list = null;
		try {
			ftp = new ProFTPClient();
			ftp.setRemoteHost(Utility.FTP_SERVER);
			ftp.connect();
			if(ftp.connected()){
				ftp.login(Utility.FTP_SERVER_KULLANICI_ADI, Utility.FTP_SERVER_SIFRE);
				ftp.setType(FTPTransferType.BINARY);
				ftp.setConnectMode(FTPConnectMode.PASV);
				System.out.println("HEDEF : "+hedef);
				ftp.cdup();
				String hedefYol = hedef.substring(hedef.lastIndexOf(klasorAdi)+klasorAdi.length()+1);
				String[] yolList = hedefYol.split(seperator);
				ftp.chdir(klasorAdi);
				for(int i=0;i<yolList.length;i++){
					if(!yolList[i].equalsIgnoreCase(seperator) && yolList[i] != null && !yolList[i].trim().equalsIgnoreCase("")){
						System.out.print(i+". KLASOR : "+yolList[i]);
						ftp.chdir(yolList[i]);
						System.out.println(" SIMDIKI KLASOR  : "+ftp.pwd());
					}
				}
				list = ftp.dir();
				ftp.quit();
			}else{
				
			}
		} catch (Exception e) {
			e.printStackTrace();
			
		}finally{
		}
		
		return list;
	}
	
	private static void evrakFtpSil(String evrakSiraNo,String guncelAyYil,int i, String uzanti) throws Exception {

		ProFTPClient ftp =null;
		try {
			ftp = new ProFTPClient();
			ftp.setRemoteHost(Utility.FTP_SERVER);
			ftp.connect();
			if(ftp.connected()){
				ftp.login(Utility.FTP_SERVER_KULLANICI_ADI, Utility.FTP_SERVER_SIFRE);
				ftp.setType(FTPTransferType.BINARY);
				ftp.setConnectMode(FTPConnectMode.PASV);
				ftp.delete("."+Utility.seperator+guncelAyYil+evrakSiraNo+"_"+i+"."+uzanti);
				//ftp.delete("."+Utility.seperator+guncelAyYil+Utility.seperator+evrakSiraNo+"_"+i+"."+uzanti);
				ftp.quit();
			}else{
				throw new Exception("HATA  Kod : [HATA-9-02] Lütfen BTK ile irtibata geçiniz!");
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception("HATA  Kod : [HATA-9-03] Lütfen BTK ile irtibata geçiniz!");
		}finally{
		}
	}
	
	
	private static void hariciEvrakFtpSil(String evrakYol) throws Exception {

		ProFTPClient ftp =null;
		try {
			ftp = new ProFTPClient();
			ftp.setRemoteHost(Utility.FTP_SERVER);
			ftp.connect();
			if(ftp.connected()){
				ftp.login(Utility.FTP_SERVER_KULLANICI_ADI, Utility.FTP_SERVER_SIFRE);
				ftp.setType(FTPTransferType.BINARY);
				ftp.setConnectMode(FTPConnectMode.PASV);
				ftp.delete(evrakYol);
				ftp.quit();
			}else{
				throw new Exception("HATA  Kod : [HATA-9-02] Lütfen BTK ile irtibata geçiniz!");
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception("HATA  Kod : [HATA-9-03] Lütfen BTK ile irtibata geçiniz!");
		}finally{
		}
	}
	
	public static void ftpGonder(String kaynak,String hedef) throws Exception {
		ProFTPClient ftp =null;
		try {
			ftp = new ProFTPClient();
			ftp.setRemoteHost(Utility.FTP_SERVER);
			ftp.connect();
			if(ftp.connected()){
				ftp.login(Utility.FTP_SERVER_KULLANICI_ADI, Utility.FTP_SERVER_SIFRE);
				ftp.setType(FTPTransferType.BINARY);
				ftp.setConnectMode(FTPConnectMode.PASV);
				System.out.println("KAYNAK : "+kaynak);
				System.out.println("HEDEF : "+hedef);
				ftp.put(kaynak,hedef);
				ftp.quit();
			}else{
				
			}
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
		}
	}
	
	public static void ftpKlasorKontrol(String hedef,String klasor) {
		if(hedef.lastIndexOf(klasor)>=0){
			String hedefYol = hedef.substring(hedef.lastIndexOf(klasor)+klasor.length()+1);
			System.out.print("Utility:ftpKlasorKontrol : HEDEF YOL  : "+hedefYol + " KLASOR : "+klasor);
			String[] yolList = hedefYol.split(seperator);			
			ProFTPClient ftp =null;
			try {
				ftp = new ProFTPClient();
				ftp.setRemoteHost(Utility.FTP_SERVER);
				ftp.connect();
				if(ftp.connected()){
					ftp.login(Utility.FTP_SERVER_KULLANICI_ADI, Utility.FTP_SERVER_SIFRE);
					ftp.cdup();
					logger.info(" SIMDIKI KLASOR  : "+ftp.pwd() + " chdir yapilacak:" + klasor);
					ftp.chdir(klasor);
					logger.info(" SIMDIKI KLASOR  : "+ftp.pwd());
					for(int i=0;i<yolList.length;i++){
						if(!yolList[i].equalsIgnoreCase(seperator) && yolList[i] != null && !yolList[i].trim().equalsIgnoreCase("")){
							if(yolList[i].indexOf(".")<0){	// bakilan klasor olmasi lazim diger durumlarda evraka bakmayacak
								System.out.print(i+". KLASOR : "+yolList[i]);
								try{
									ftp.mkdir(yolList[i]);
								}catch(Exception e){
									System.out.println("KLASOR OLUSTURULAMADI YA DA KLASOR MEVCUT");
									//e.printStackTrace();
								}
								ftp.chdir(yolList[i]);
								System.out.println(" SIMDIKI KLASOR  : "+ftp.pwd());
							}
						}
					}
				}
				ftp.quit();
			} catch (Exception e) {
				logger.info("Utility:ftpKlasorKontrol : ", e);
				//e.printStackTrace();
			}finally{
			}
		}
	}

	public static void dosyalariSil(String anaKlasorYolu, String anaKlasor, Long evrakId, String evrakSiraNo,Long kullaniciId) throws Exception {
		String guncelAyYil = getGuncelYil()+seperator+getGuncelAy()+seperator;
		
		File f = new File(Utility.ARSIV_XML_PATH+seperator+guncelAyYil+seperator+getGuncelGun()+seperator+kullaniciId+seperator+anaKlasor);
		if(f.exists() && f.isDirectory()){
			String icerik[] = f.list();
			if(icerik != null || icerik.length != 0){
				int i=0;
				for(String dosya:icerik){
					int spos = dosya.lastIndexOf(".");
					String uzanti = dosya.substring(spos+1);
					if(evrakDosyasiUzantiKontrol(uzanti)){
						i++;
						File  silinecekDosya = new File(Utility.EVRAK_FILES_PATH+seperator+guncelAyYil+seperator+evrakSiraNo+"_"+i+"."+uzanti);
						if(silinecekDosya.exists()){
							if(!silinecekDosya.delete()){
								throw new Exception("HATA  Kod : [HATA-9-01] Lütfen BTK ile irtibata geçiniz!");
							}
							evrakFtpSil(evrakSiraNo, guncelAyYil, i, uzanti);
						}
					}
					
				}
			}
		}
		File gelenEvrakZipFiles = new File(Utility.GELEN_EVRAK_ZIP_FILES_PATH+seperator+guncelAyYil+seperator+evrakSiraNo+".zip");
		if(gelenEvrakZipFiles.exists()){
			if(!gelenEvrakZipFiles.delete()){
				throw new Exception("HATA  Kod : [HATA-9-04] Lütfen BTK ile irtibata geçiniz!");
			}
		}
		
		
		
	}
	
	public static String anaKlasorYoluBul(String xmlYolu) {
		int spos = xmlYolu.lastIndexOf(seperator);
		String xmlKlasorYolu = xmlYolu.substring(0,spos);
		int sspos = xmlKlasorYolu.lastIndexOf(seperator);
		String anaKlasor =  xmlKlasorYolu.substring(0,sspos);
		return anaKlasor;
	}
	
	public static String anaKlasorBul(String xmlYolu) {
		int spos = xmlYolu.lastIndexOf(seperator);
		String xmlKlasorYolu = xmlYolu.substring(0,spos);
		int sspos = xmlKlasorYolu.lastIndexOf(seperator);
		String anaKlasor = xmlKlasorYolu.substring(sspos+1);
		return anaKlasor;
	}
	



	
	public static boolean arsiveKopyala(String xmlYolu,Long kullaniciId, String clientIp,Long islemId) {
		int spos = xmlYolu.lastIndexOf(Utility.seperator);
		String xmlKlasorYolu = xmlYolu.substring(0,spos);
		int sspos = xmlKlasorYolu.lastIndexOf(Utility.seperator);
		String anaKlasor = xmlKlasorYolu.substring(sspos+1);
		String hedefKlasorYolu =Utility.ARSIV_XML_PATH+Utility.seperator+getGuncelYil()+
								Utility.seperator+getGuncelAy()+Utility.seperator+getGuncelGun()+
								Utility.seperator+kullaniciId+Utility.seperator+anaKlasor;
		try {
		    Utility.xmlIslemLog(kullaniciId, clientIp, "Arşive Dosya Kopyala", null, null,islemId);
			File f = new File(hedefKlasorYolu);
			if(!f.exists()){
				createDirs(hedefKlasorYolu);
			}
		    klasorIcerikCopy(xmlKlasorYolu, hedefKlasorYolu);
		    klasorFtple(hedefKlasorYolu,"arsivzipfiles");
		    IymService srv = getIymService();
		    srv.arsivXmlDosyaKaydet(hedefKlasorYolu,kullaniciId);
		    Utility.xmlIslemLog(kullaniciId, clientIp, "Arşive Insert", "ARSIV_XML_LOG", null,islemId);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
		
	}
	
	public static String evrakBirimGetir(LoginInfo personel,String kurumKod) {
		IymService srv = getIymService();
		String birimKod = srv.birimGetir(kurumKod);
		return birimKod;
	}

	public static List<String> evrakKurumGetir(LoginInfo personel) {
		IymService srv = getIymService();
		List<String> kurumKod = srv.kullaniciKurumGetir(personel.getIymId());
		return kurumKod;
	}
	
	public static List<String> evrakKurumGetir(Long personelId) {
		IymService srv = getIymService();
		List<String> kurumKod = srv.kullaniciKurumGetir(personelId);
		return kurumKod;
	}

	public static String evrakKurumAdiGetir(String kurumKod) {
		IymService srv = getIymService();
		String kurum = srv.kurumAdiGetir(kurumKod);
		return kurum;
	}
	
	public static boolean adliMahkemeKararTuruKontrolu(String evrakKurum) {

		boolean adli = true;
		if (evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMIDB.getKurumKodu())) {
			adli = false;
		} else if (evrakKurum.equals(EVRAK_GELEN_KURUMLAR.MIT.getKurumKodu())) {
			adli = false;
		} 

		return adli;
	}

	public static boolean istihbariMahkemeKararTuruKontrolu(String evrakKurum) {

		boolean istihbari = false;

		if (evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMIDB.getKurumKodu())) {
			istihbari = true;
		} else if (evrakKurum.equals(EVRAK_GELEN_KURUMLAR.MIT.getKurumKodu())) {
			istihbari = true;
		} else if (evrakKurum.equals(EVRAK_GELEN_KURUMLAR.JANDARMA.getKurumKodu())) {
			istihbari = true;
		} else if (evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMBSM.getKurumKodu())) {
			istihbari = true;
		}

		return istihbari;
	}
	
	public static void main(String[] args) {
		mahkemeKararNoDogrumu("2014/1");
	}
	
	public static boolean mahkemeKararNoDogrumu(String mahkemeKararNo){
		try{
			if(mahkemeKararNo.length()<5)
				return false;
			if(mahkemeKararNo.indexOf("/")<0)
				return false;
			/*MAHKEEME KARAR NO YILDAN BAGIMSIZ OLDU BU YUZDEN -30 KONTROLU YAPILDI 11.05.2013*/
		/*	if(Utility.objToLong(mahkemeKararNo.split("/")[0])<Utility.getGuncelYil()-30 || Utility.objToLong(mahkemeKararNo.split("/")[0])>Utility.getGuncelYil())
				return false;*/
			String[] mkNo = mahkemeKararNo.split(" ");
			if (mkNo.length > 1) {
				return false;
			}
			return true;
		}catch(Exception e){
			return false;
		}
	}
	
	public static boolean sucTipiKontrolu(String sucTipiKodu) {
		IymService srv = getIymService();
		Long sayi = srv.sucTipiKontrol(sucTipiKodu);
		if(sayi>0)
			return true;
		return false;
	}
	
	public static boolean mahkemeVarMi(String mahkemeKodu){
		IymService srv = getIymService();
		Long sayi = srv.mahkemeKontrol(mahkemeKodu);
		if(sayi>0)
			return true;
		return false;
	}
	
	public static boolean aidiyatSistemdeVarMi(String aidiyatKod){
		IymService srv = getIymService();
		Long sayi = srv.aidiyatKontrol(aidiyatKod);
		if(sayi>0)
			return true;
		return false;
	}

	public static boolean yurtdisiHedefHitapKuralKontrolu(String hedefNo,String hedefTipi) {
		Long sayi = hitapKuralKontrolu(hedefNo,hedefTipi,"EVET");	//kibris kontrol
		if(sayi>0)
			return true;
		else{
			sayi = hitapKuralKontrolu(hedefNo,hedefTipi,"HAYIR");	//diğer kontrol
			if(sayi == 0)
				return true;
		} 	
		return false;
	}

	public static boolean turksatHedefHitapKuralKontrolu(String hedefNo,String hedefTipi) {
		IymService srv = getIymService();
		Long sayi = srv.turksatMacHedefTipiHitapKuralKontrol(hedefNo,hedefTipi,"EVET");
		if(sayi>0)
			return true;
		return false;
	}
	public static boolean emailTipiHedefHitapKuralKontrolu(String hedefNo,String hedefTipi) {
		IymService srv = getIymService();
		Long sayi = srv.emailTipiHitapKuralKontrol(hedefNo,hedefTipi,"EVET");
		if(sayi>0)
			return true;
		return false;
	}
	
	public static boolean xdslUsernameTipiHedefHitapKuralKontrolu(String hedefNo,String hedefTipi) {
		IymService srv = getIymService();
		int pos = hedefNo.indexOf("@");
		String xdslHedef = hedefNo;
		if(pos != -1){
			xdslHedef = hedefNo.substring(pos);
		}
		Long sayi = srv.xdslUsernameHitapKuralKontrol(xdslHedef,hedefTipi,"EVET");
		if(sayi>0)
			return true;
		return false;
	}

	public static boolean imeiHedefHitapKuralKontrolu(String hedefNo) {
		IymService srv = getIymService();
		Long sayi = srv.hitapKuralKontrol(hedefNo,HEDEF_TIPLERI.IMEI.getHedefKodu(),"EVET");
		if(sayi>0)
			return true;
		return false;
	}
	
	
	
	public static boolean hedefHitapKuralKontrolu(String hedefNo,String hedefTipi) {
		Long sayi = hitapKuralKontrolu(hedefNo,hedefTipi,"EVET");	
		if(sayi>0)
			return true;
		return false;
	}
	
	public static Long hitapKuralKontrolu(String hedefNo,String hedefTipi,String icindeMi) {
		IymService srv = getIymService();
		Long sayi = srv.hitapKuralKontrol(hedefNo,hedefTipi,icindeMi);
		return sayi;
	}
	
	public static List<MahkemeAidiyatTalepPojo> mapMahkemeAidiyat(MAHKEME_AIDIYAT a,Long mahkemeKararId) {
		List<MahkemeAidiyatTalepPojo> aidiyatTalep = new ArrayList<MahkemeAidiyatTalepPojo>();
		for (String aidiyat : a.AIDIYAT_KOD) {
			MahkemeAidiyatTalepPojo mahkemeAidiyat = new MahkemeAidiyatTalepPojo();
			mahkemeAidiyat.setAidiyatKod(aidiyat);
			mahkemeAidiyat.setMahkemeId(mahkemeKararId);
			aidiyatTalep.add(mahkemeAidiyat);
		}
		return aidiyatTalep;
	}
	public static List<String> mapAidiyatKodList(List<MahkemeAidiyatTalepPojo> a) {
		List<String> aidiyatKod = new ArrayList<String>();
		for (MahkemeAidiyatTalepPojo aidiyat : a) {
			aidiyatKod.add(aidiyat.getAidiyatKod());
		}
		return aidiyatKod;
	}
	
	public static List<MahkemeSuclarTalepPojo> mapMahkemeSuclar(MAHKEME_SUC_TIPI s,Long mahkemeKararId) {
		List<MahkemeSuclarTalepPojo> suclarTalep = new ArrayList<MahkemeSuclarTalepPojo>();
		for (String sucTipi : s.SUC_TIP_KOD) {
			MahkemeSuclarTalepPojo sucTipiTalep = new MahkemeSuclarTalepPojo();
			sucTipiTalep.setMahkemeSucTipKod(sucTipi);
			sucTipiTalep.setMahkemeKararId(mahkemeKararId);
			suclarTalep.add(sucTipiTalep);
		}
		return suclarTalep;
	}

	public static MahkemeKararTalepPojo mapMahkemeKarar(MAHKEME_KARAR k, Long evrakId,Long kullaniciId) {
		MahkemeKararTalepPojo mahkemeKarar = new MahkemeKararTalepPojo();
		mahkemeKarar.setEvrakId(evrakId);
		mahkemeKarar.setKullaniciId(kullaniciId);
		mahkemeKarar.setAciklama(k.ACIKLAMA);
		mahkemeKarar.setKararTip(k.KARAR_TIP);
		mahkemeKarar.setMahkemeKararBaslamaTarihi(k.MAH_KARAR_BAS_TAR);
		mahkemeKarar.setMahkemeKararBitisTarihi(k.MAH_KARAR_BITIS_TAR);
		mahkemeKarar.setMahkemeIli(k.MAHKEME_ILI);
		mahkemeKarar.setMahkemeKararNo(k.MAHKEME_KARAR_NO);
		mahkemeKarar.setMahkemeKodu(k.MAHKEME_KODU);
		mahkemeKarar.setSorusturmaNo(k.SORUSTURMA_NO);
		return mahkemeKarar;
	}

	
	public static HtsMahkemeKararTalepPojo mapMahkemeKararHts(MAHKEME_KARAR k,Long evrakId, Long kullaniciId) {
		HtsMahkemeKararTalepPojo mahkemeKarar = new HtsMahkemeKararTalepPojo();
		mahkemeKarar.setEvrakId(evrakId);
		mahkemeKarar.setKullaniciId(kullaniciId);
		mahkemeKarar.setAciklama(k.ACIKLAMA);
		mahkemeKarar.setKararTip(k.KARAR_TIP);
		mahkemeKarar.setMahkemeIli(k.MAHKEME_ILI);
		mahkemeKarar.setMahkemeKararNo(k.MAHKEME_KARAR_NO);
		mahkemeKarar.setMahkemeKodu(k.MAHKEME_KODU);
		mahkemeKarar.setSorusturmaNo(k.SORUSTURMA_NO);
		mahkemeKarar.setDurum("");
		return mahkemeKarar;
	}
	
	public static XmlEvrakPojo mapEvrak(EVRAK_KAYIT e,Long kullaniciId,String evrakBirim,String evrakTipi,String evrakGeldigiKurum) {
		XmlEvrakPojo evrak = new XmlEvrakPojo();
		evrak.setAciklama(e.ACIKLAMA);
		evrak.setAcilMi(e.ACILMI);
		evrak.setEvrakGeldigiKurum(evrakGeldigiKurum);
		evrak.setEvrakKonusu(e.EVRAK_KONUSU);
		evrak.setEvrakNo(e.EVRAK_NO);
		evrak.setEvrakTarihi(e.EVRAK_TARIHI);
		evrak.setGelenIl(e.GEL_IL);
		evrak.setHavaleBirim(evrakBirim);
		evrak.setKayitEdenKullanici(kullaniciId);
		evrak.setEvrakTipi(evrakTipi);
		evrak.setUniqCol(e.mukerrerNo.toString());
		return evrak;
	}

	
	public static XmlEvrakPojo mapEvrakHts(EVRAK_KAYIT xmlEvrak,Long personelIymId, String evrakBirim, String evrakTipi,String evrakKurum) {
		XmlEvrakPojo evrak = mapEvrak(xmlEvrak, personelIymId, evrakBirim, evrakTipi, evrakKurum);
		if(xmlEvrak.MAHKEME_KARAR.size() == 1){
			evrak.setMahkemeKararNo(xmlEvrak.MAHKEME_KARAR.get(0).MAHKEME_KARAR_NO);
			evrak.setSorusturmaNo(xmlEvrak.MAHKEME_KARAR.get(0).SORUSTURMA_NO);
			//evrak.setGelenIl(xmlEvrak.MAHKEME_KARAR.get(0).MAHKEME_ILI);
			return evrak;
		}else
			return null;
	}
	public static MahkemeKararDetayTalepPojo mapMahkemeKararDetay(MAHKEME_KARAR_DETAY d, Long evrakId,Long iliskiliMahkemeKararId, Long mahkemeKararId,Long kullaniciId) {
		MahkemeKararDetayTalepPojo detayTalep = new MahkemeKararDetayTalepPojo();
		detayTalep.setEvrakId(evrakId);
		detayTalep.setKullaniciId(kullaniciId);
		detayTalep.setMahkemeKararId(mahkemeKararId);
		detayTalep.setMahkemeIliDetay(d.MAHKEME_ILI_DETAY);			
		detayTalep.setMahkemeKoduDetay(d.MAHKEME_KODU_DETAY);
		detayTalep.setMahkemeKararNoDetay(d.MAHKEME_KARAR_NO_DETAY);
		detayTalep.setSorusturmaNoDetay(d.SORUSTURMA_NO_DETAY);
		return detayTalep;
	}
	
	public static List<MahkemeAidiyatDetayTalepPojo> mapMahkemeAidiyatDetay(MAHKEME_AIDIYAT_DETAY ad,Long iliskiliMahkemeKararId,Long mahkemeKararId,Long mahkemeKararDetayId) {
		List<MahkemeAidiyatDetayTalepPojo> aidiyatDetay = new ArrayList<MahkemeAidiyatDetayTalepPojo>();
		for (String aidiyatEkle : ad.MAHKEME_AIDIYAT_KODU_EKLE) {
			MahkemeAidiyatDetayTalepPojo detayTalep = new MahkemeAidiyatDetayTalepPojo();
			detayTalep.setIliskiliMahkemeKararId(iliskiliMahkemeKararId);
			detayTalep.setMahkemeAidiyatKoduEkle(aidiyatEkle);
			detayTalep.setMahkemeKararDetayId(mahkemeKararDetayId);
			detayTalep.setMahkemeKararId(mahkemeKararId);
			aidiyatDetay.add(detayTalep);
		}
		for (String aidiyatCikar : ad.MAHKEME_AIDIYAT_KODU_CIKAR) {
			MahkemeAidiyatDetayTalepPojo detayTalep = new MahkemeAidiyatDetayTalepPojo();
			detayTalep.setIliskiliMahkemeKararId(iliskiliMahkemeKararId);
			detayTalep.setMahkemeAidiyatKoduCikar(aidiyatCikar);
			detayTalep.setMahkemeKararDetayId(mahkemeKararDetayId);
			detayTalep.setMahkemeKararId(mahkemeKararId);
			aidiyatDetay.add(detayTalep);
		}
		return aidiyatDetay;
	}
	
	public static HedeflerTalepPojo mapHedefler(HEDEFLER h,Long mahkemeKararId,Long kullaniciId) {
		HedeflerTalepPojo hedefTalep = new HedeflerTalepPojo();
		hedefTalep.setBaslamaTarihi(h.BASLAMA_TARIHI);
		hedefTalep.setHedefAdi(h.HEDEF_ADI);
		hedefTalep.setHedefNo(h.HEDEF_NO);
		hedefTalep.setHedefSoyadi(h.HEDEF_SOYADI);
		hedefTalep.setHedefTipi(h.HEDEF_TIPI);
		hedefTalep.setMahkemeKararId(mahkemeKararId);
		hedefTalep.setKullaniciId(kullaniciId);
		hedefTalep.setSuresi(h.SURESI);
		hedefTalep.setSureTipi(h.SURE_TIPI);
		hedefTalep.setUzatmaSayisi(h.UZATMA_SAYISI);
		//KT.2024-008804 Geregi
		hedefTalep.setCanakNo(h.CANAK_NO);
		return hedefTalep;
	}
	
	public static HtsHedeflerTalepPojo mapHtsHedefler(ITK_HEDEFLER h,Long mahkemeKararId,Long kullaniciId) {
		HtsHedeflerTalepPojo hedefTalep = new HtsHedeflerTalepPojo();
		hedefTalep.setHedefNo(h.HEDEF_NO);
		hedefTalep.setKarsiHedefNo(h.KARSI_HEDEF_NO);
		hedefTalep.setSorguTipi(h.SORGU_TIPI);
		hedefTalep.setTespitTuru(h.TESPIT_TURU);
		hedefTalep.setBaslangicTarihi(h.BASLANGIC_TARIHI);
		hedefTalep.setBitisTarihi(h.BITIS_TARIHI);
		hedefTalep.setMahkemeKararId(mahkemeKararId);
		hedefTalep.setKullaniciId(kullaniciId);
		return hedefTalep;
	}
	
	public static List<HedeflerAidiyatTalepPojo> mapBimAidiyat(List<String> b,Long hedefId,Long kullaniciId) {
		List<HedeflerAidiyatTalepPojo> aidiyatList = new ArrayList<HedeflerAidiyatTalepPojo>();
		for (String aidiyatKod : b) {
			HedeflerAidiyatTalepPojo aidiyat = new HedeflerAidiyatTalepPojo();
			aidiyat.setAidiyatKod(aidiyatKod);
			aidiyat.setHedefId(hedefId);
			aidiyat.setKullaniciId(kullaniciId);
			aidiyatList.add(aidiyat);
		}
		return aidiyatList;
	}
	
	
	public static List<MahkemeHedeflerAidiyatTalepPojo> mapMahkemeHedeflerAidiyat(List<String> b,Long hedefId,Long kullaniciId,Long mahkemeKararId) {
		List<MahkemeHedeflerAidiyatTalepPojo> aidiyatList = new ArrayList<MahkemeHedeflerAidiyatTalepPojo>();
		for (String aidiyatKod : b) {
			MahkemeHedeflerAidiyatTalepPojo aidiyat = new MahkemeHedeflerAidiyatTalepPojo();
			aidiyat.setAidiyatKod(aidiyatKod);
			aidiyat.setHedefId(hedefId);
			aidiyat.setKullaniciId(kullaniciId);
			aidiyat.setMahkemeKararId(mahkemeKararId);
			aidiyatList.add(aidiyat);
		}
		return aidiyatList;
	}

	public static HedeflerDetayTalepPojo mapHedeflerDetay(HEDEFLER_DETAY hd,Long iliskiliHedefId, Long mahkemeKararId, Long mahkemeKararDetayId) {
		HedeflerDetayTalepPojo detayTalep = new  HedeflerDetayTalepPojo();
		detayTalep.setHedefAdi(hd.HEDEF_ADI);
		detayTalep.setHedefSoyadi(hd.HEDEF_SOYADI);
		detayTalep.setHedefNo(hd.HEDEF_NO);
		detayTalep.setHedefTipi(hd.HEDEF_TIPI);
		detayTalep.setCanakNo(hd.CANAK_NO);
		detayTalep.setIliskiliHedefId(iliskiliHedefId);
		detayTalep.setMahkemeKararDetayId(mahkemeKararDetayId);
		detayTalep.setMahkemeKararId(mahkemeKararId);	
		return detayTalep;
	}

	public static boolean evrakDahaOnceGelmisMiKontrol(String evrakNo,String evrakKurum, String gelenIl, String evrakTarihi) {
		IymService srv = getIymService();
		Long sayi = srv.evrakDahaOnceGelmisMiKontrol(evrakNo,evrakKurum,gelenIl,evrakTarihi);
		if(sayi>0)
			return true;
		return false;
		
	}
	
	public static boolean ilKontrol(String ilKodu) {
		IymService srv = getIymService();
		Long sayi = srv.ilKontrol(ilKodu);
		if(sayi>0)
			return true;
		return false;
		
	}
	
	public static boolean mukerrerEvrakKontrol(String evrakNo,String gelenKurum ) {
		IymService srv = getIymService();
		Long sayi = srv.mukerrerEvrakKontrol(evrakNo,gelenKurum);
		if(sayi>0)
			return true;
		return false;
		
	}
	

	
	public static Long sonGonderilenEvrakSayiGetir(String evrakNo,String evrakKurum, String gelenIl, String evrakTarihi) {
		IymService srv = getIymService();
		Long sayi = srv.sonGonderilenEvrakSayiGetir(evrakNo,evrakKurum,gelenIl,evrakTarihi);
		return sayi;
		
	}
	
	public static void xmlIslemLog(Long kullaniciId,String ip, String islem, String islemTablo,String islemTabloRefId,Long islemId) {
		IymService srv = getIymService();
		srv.xmlIslemLog(kullaniciId,ip,islem,islemTablo,islemTabloRefId,islemId);
	}
		

//	public static EVRAK_KAYIT eskiXmlKayit(EVRAK_KAYIT xmlEvrak, String evrakKurum,String evrakBirim, String evrakTipi,Long personelIymId, String xmlYolu, String clientIp) {
//		IymService srv = getIymService();
//		return srv.eskiXmlKayit(xmlEvrak, evrakKurum, evrakBirim, evrakTipi,personelIymId,xmlYolu,clientIp);
//	}
	public static EVRAK_KAYIT yeniXmlKayit(EVRAK_KAYIT xmlEvrak, String evrakKurum,String evrakBirim, String evrakTipi,Long personelIymId, String xmlYolu,String clientIp) {
		IymService srv = getIymService();
		return srv.yeniXmlKayit(xmlEvrak, evrakKurum, evrakBirim, evrakTipi,personelIymId,xmlYolu,clientIp);
	}
	
	public static EVRAK_KAYIT yeniHtsXmlKayit(EVRAK_KAYIT xmlEvrak, String evrakKurum,String evrakBirim, String evrakTipi,Long personelIymId, String xmlYolu, String clientIp) {
		IymService srv = getIymService();
		return srv.yeniHtsXmlKayit(xmlEvrak, evrakKurum, evrakBirim, evrakTipi,personelIymId,xmlYolu,clientIp);
	}
	public static EVRAK_KAYIT yeniGenelEvrakXmlKayit(EVRAK_KAYIT xmlEvrak, String evrakKurum,String evrakBirim, String evrakTipi,Long personelIymId, String xmlYolu, String clientIp) {
		IymService srv = getIymService();
		return srv.yeniGenelEvrakXmlKayit(xmlEvrak, evrakKurum, evrakBirim, evrakTipi,personelIymId,xmlYolu,clientIp);
	}
	public static ArrayList<XMLHATA> evrakOnayla(EVRAK_KAYIT xmlEvrak,Long evrakId,Long personelIymId, String clientIp) {
		IymService srv = getIymService();
		return srv.xmlOnayla(xmlEvrak,evrakId,personelIymId,clientIp);
	}

	public static ArrayList<XMLHATA> htsEvrakOnayla(EVRAK_KAYIT xmlEvrak, Long evrakId, long personelIymId, String clientIp, String gorev) {
		IymService srv = getIymService();
		return srv.htsEvrakOnayla(xmlEvrak,evrakId,personelIymId,clientIp,gorev);
	}
	
	public static ArrayList<XMLHATA> genelEvrakOnayla(EVRAK_KAYIT xmlEvrak, Long evrakId, long personelIymId, String clientIp, String gorev) {
		IymService srv = getIymService();
		return srv.genelEvrakOnayla(xmlEvrak,evrakId,personelIymId,clientIp,gorev);
	}
	
	public static ArrayList<XmlEvrakPojo> evrakListesiGetir(String kurum,Long kullaniciId) {
		IymService srv = getIymService();
		return (ArrayList<XmlEvrakPojo>) srv.getEvrakByKurum(kurum,kullaniciId);
	}
	
	public static boolean hedefMahkemeKararKontrol(String iliskiliMahkemeId,String hedefNo,String evrakGelenKurum,String gercekHedefTipi,String sorusturmaNo) {
		IymService srv = getIymService();
		Long sayi = srv.hedefMahkemeKararKontrol(iliskiliMahkemeId,hedefNo,evrakGelenKurum,gercekHedefTipi,sorusturmaNo);
		if(sayi>0)
			return true;
		return false;
	}

	public static boolean hedefSonMahkemeKarariIleUzatilmisMiKontrol(String iliskiliMahkemeId, HedeflerTalepPojo hedefTalep,String evrakKurum, MahkemeKararDetayTalepPojo mahkemeKararDetay,Long kullaniciId) {
		IymService srv = getIymService();
		Long sayi = srv.hedefSonMahkemeKarariIleUzatilmisMiKontrol(iliskiliMahkemeId,hedefTalep,evrakKurum,mahkemeKararDetay,kullaniciId);
		if(sayi>0)
			return true;
		return false;
	}

	public static XmlEvrakPojo getEvrakById(Long evrakId) {
		IymService srv = getIymService();
		XmlEvrakPojo evrak = srv.getEvrakByID(evrakId);
		return evrak;
	}
	
	public static List<MahkemeKararTalepPojo> getMahkemeKararTalepListByEvrakId(Long evrakId,Long kullaniciId) {
		IymService srv = getIymService();
		List<MahkemeKararTalepPojo> talepList = srv.mahkemeKararTalepGetir(evrakId, kullaniciId);
		return talepList;
	}
	
	
	public static List<HedeflerTalepPojo> hedeflerTalepGetirByMahkemeKararId(Long mahkemeKararId) {
		IymService srv = getIymService();
		List<HedeflerTalepPojo> talepList = srv.hedeflerTalepGetirByMahkemeKararId(mahkemeKararId);
		return talepList;
	}
	

	public static String getXmlKonumByEvrakID(Long evrakId) {
		IymService srv = getIymService();
		String konum = srv.getXmlKonumByEvrakID(evrakId);
		return konum;
	}

	public static boolean evrakSil(Long evrakId, long personelIymId, String clientIp) {
		IymService srv = getIymService();
		return srv.evrakSil(evrakId,personelIymId,clientIp);
		
	}
	
	public static boolean evrakMahkemeKararIslemSil(Long evrakId, long personelIymId, String clientIp) {
		IymService srv = getIymService();
		return srv.evrakMahkemeKararIslemSil(evrakId,personelIymId,clientIp);
		
	}
	
	public static List<GelenEvrakSonuc> tespitEvrakiDurumGetirByKullaniciId(
			long kullaniciId) {
		
		List<GelenEvrakSonuc> sonuc = new ArrayList<GelenEvrakSonuc>();
		IymService srv = getIymService();
		List<XmlEvrakPojo> evrakList = srv.tespitEvrakiDurumGetirByKullaniciId(kullaniciId);
		if(evrakList.size()>0){
			for(XmlEvrakPojo e : evrakList){
				GelenEvrakSonuc s = new GelenEvrakSonuc();
				s.setDurum(e.getDurumu());
				s.setEvrakNo(e.getEvrakNo());
				s.setTibEvrakNo(e.getEvrakSiraNo());
				s.setDosyaBoyutu(dosyaBoyutuBul(kullaniciId,s));
				sonuc.add(s);
			}
		}
		return sonuc;
	}
	
	public static String evrakDurumGetirByKullaniciIdEvrakNo(String evrakNo,Long kullaniciId,String kullaniciAdi) {
		IymService srv = getIymService();
		List<XmlEvrakPojo> evrakList = srv.evrakDurumGetirByKullaniciIdEvrakNo(evrakNo,kullaniciId,kullaniciAdi);
		if(evrakList.size()>0)
			return evrakList.get(0).getDurumu();
		return "EVRAK BULUNAMADI";
	}
	
	public static boolean hariciEvrakArsivle(Long hYaziId) {
		IymService srv = getIymService();
		return srv.hariciEvrakArsivle(hYaziId);
	}
	
	public static GelenEvrakSonuc tespitEvrakiDurumGetirByKullaniciIdEvrakNo(String evrakNo, long kullaniciId) {
		GelenEvrakSonuc sonuc = new GelenEvrakSonuc();
		IymService srv = getIymService();
		List<XmlEvrakPojo> evrakList = srv.tespitEvrakiDurumGetirByKullaniciIdEvrakNo(evrakNo,kullaniciId);
		if(evrakList.size()>0){
			sonuc.setDurum(evrakList.get(0).getDurumu());
			sonuc.setEvrakNo(evrakList.get(0).getEvrakNo());
			sonuc.setTibEvrakNo(evrakList.get(0).getEvrakSiraNo());
			sonuc.setDosyaBoyutu(dosyaBoyutuBul(kullaniciId, sonuc));
		}else
			sonuc.setDurum("EVRAK BULUNAMADI");
		return sonuc;
	}


	private static String dosyaBoyutuBul(long kullaniciId, GelenEvrakSonuc sonuc) {
		IymService srv = getIymService();
		List<HariciYazilarPojo> yaziList = srv.hariciYaziBul(sonuc.getTibEvrakNo(),kullaniciId);
		if(yaziList!= null && yaziList.size()>0){
			File f = new File(yaziList.get(0).getEkPath());
			if(!f.exists()){
				if(!Utility.evrakFtpdenAl(yaziList.get(0).getEkPath(), kullaniciId))
					return null;
			}
			f = new File(yaziList.get(0).getEkPath());
			if(f.exists())
				return getDosyaBoyutu(f.length());
		}
		return "";
	}

	public static XmlEvrakPojo tespitEvrakiDurumGetirByKullaniciIdTibEvrakNo(String tibEvrakNo, long kullaniciId) {
		IymService srv = getIymService();
		List<XmlEvrakPojo> evrakList = srv.tespitEvrakiDurumGetirByKullaniciIdTibEvrakNo(tibEvrakNo,kullaniciId);
		if(evrakList.size()>0){
			return evrakList.get(0);
		}else
			return null;
	}
	
	public static XmlEvrakPojo evrakGetirByKullaniciIdTibEvrakNo(String tibEvrakNo, long kullaniciId) {
		IymService srv = getIymService();
		List<XmlEvrakPojo> evrakList = srv.evrakDurumGetirByKullaniciIdTibEvrakNo(tibEvrakNo,kullaniciId);
		if(evrakList.size()>0){
			return evrakList.get(0);
		}else
			return null;
	}
	
	
	public static FileDownloader evrakGonder(String tibEvrakNo, long kullaniciId) {
		FileDownloader file = null;
		IymService srv = getIymService();
		List<XmlEvrakPojo> evrakList = srv.tespitEvrakiDurumGetirByKullaniciIdTibEvrakNo(tibEvrakNo,kullaniciId);
		System.out.println("HTS : EVRAK LIST SIZE : "+evrakList.size());
		if(evrakList.size()>0){
			if(evrakList.get(0).getDurumu().equalsIgnoreCase("ONAYLANDI") || evrakList.get(0).getDurumu().equalsIgnoreCase("AKTARILDI")){
				List<HariciYazilarPojo> yaziList = srv.hariciYaziBul(tibEvrakNo,kullaniciId);
				System.out.println("HTS : YAZI LIST SIZE : "+yaziList.size());
				if(yaziList.size()>0){
					File f = new File(yaziList.get(0).getEkPath());
					System.out.println("HTS : YAZI YOL  : "+yaziList.get(0).getEkPath());
					if(!f.exists()){
						if(!Utility.evrakFtpdenAl(yaziList.get(0).getEkPath(), kullaniciId))
							return null;
						else
						   f = new File(yaziList.get(0).getEkPath());
					}
					System.out.println("HTS : FILE DOWNLOADER   : "+yaziList.get(0).getEkSha());
					file =new FileDownloader();
					DataSource source = new FileDataSource(f);
					file.setFile(new DataHandler(source));
					file.setFileType("zip");
					file.setName(yaziList.get(0).getSayisi());
					file.setSha(yaziList.get(0).getEkSha());
					if(!srv.hariciEvrakAktarildi(yaziList.get(0).getId(),kullaniciId)){
						try {
							hariciEvrakFtpSil(yaziList.get(0).getEkPath());
						} catch (Exception e) {
							e.printStackTrace();
							return null;
						}
					}
				}
			}
		}
		return file;
	}
	
	public static List mahkemeKodlariGetir(Long kullaniciId) {
		IymService srv = getIymService();
		return srv.mahkemeleriGetir(kullaniciId,null);
	}
	
	public static List<MahkemeKodlariPojo> mahkemeAdlariKodlariGetir(Long kullaniciId) {
		IymService srv = getIymService();
		return srv.mahkemeleriGetir(kullaniciId);
	}
	
	
	public static List mahkemeKodlariIlIlceyeGoreGetir(Long kullaniciId,String ilKod) {
		IymService srv = getIymService();
		return srv.mahkemeleriGetir(kullaniciId,ilKod);
	}
	
	public static List mahkemeKararTipleriGetir(Long kullaniciId) {
		IymService srv = getIymService();
		return srv.mahkemeKararTipleriGetir(kullaniciId);
	}
	
	public static List hedefTipleriGetir(Long kullaniciId) {
		IymService srv = getIymService();
		return srv.hedefTipleriGetir(kullaniciId);
	}
	
	public static List mahkemeSucTipleriGetir(Long kullaniciId) {
		IymService srv = getIymService();
		return srv.mahkemeSucTipleriGetir(kullaniciId);
	}

	public static Collection sorguTipleriGetir(Long kullaniciId) {
		IymService srv = getIymService();
		return srv.sorguTipleriGetir(kullaniciId);
	}

	public static Collection tespitTurleriGetir(Long kullaniciId) {
		IymService srv = getIymService();
		return srv.tespitTurleriGetir(kullaniciId);
	}
	
	public static Long xmlLogIlslemIdGetir() {
		IymService srv = getIymService();
		return srv.xmlLogIlslemIdGetir();
	}

	public static List<IllerPojo> mahkemeIlListGetir() {
		IymService srv = getIymService();
		return srv.mahkemeIlListGetir();
	}

	public static ArrayList<ThreadPojo> threadListesiGetir() {
		IymService srv = getIymService();
		return srv.threadListesiGetir();
	}

	public static List<HariciYazilarPojo> hariciIslenecekYaziBul() {
		IymService srv = getIymService();
		return srv.hariciIslenecekYaziBul();
	}

	public static IymYaziPojo imzalanmisYaziGetir(String sayisi) {
		IymService srv = getIymService();
		return srv.imzalanmisYaziGetir(sayisi);
	}

	

	public  static boolean  zipHazirla(Long yaziId,HariciYazilarPojo hYazi){
		IymService srv = getIymService();
		try{
			
			List<IymYaziDagitimPojo> yaziDagitimList =  srv.dagitimBilgiListGetir(yaziId);
			if(yaziDagitimList==null || yaziDagitimList.size()==0)
				throw new Exception("HATA:[YAZI_VT:"+yaziId+"] Yazı veri tabanında  bulunamadı.");

			for(IymYaziDagitimPojo d:yaziDagitimList){
				File f = new File(d.getDosyaYol());
				if(f.exists()){
				}else{
					if(d.getDosyaYol()==null || d.getDosyaYol().equalsIgnoreCase(""))
						throw new Exception("HATA:[YAZI : "+yaziId+"] Yazı İmzalama Sırasında Oluşturulamamış");
					try{
						if(!evrakFtpdenAl(d.getDosyaYol(),d.getDosyaYol()))
							throw new Exception();
					}catch(Exception e){
						e.printStackTrace();
						throw new Exception("HATA:[YAZI_FTP : "+yaziId+"] Yazı için ftp getirme hatası.");
					}
				}
			}
			
			List<IymYaziEkDosyaPojo> yaziEkList =  srv.talepFormEkListesiGetir(yaziId);
			if(yaziEkList.size() != 0){
				/*Evrak Eklerini Bul Getir*/
				for(IymYaziEkDosyaPojo yaziEk : yaziEkList){
					File f = new File(yaziEk.getDizinAdi()+yaziEk.getDosyaAdi());
					if(f.exists()){
					}else{
						try{
							if(!evrakFtpdenAl(yaziEk.getDizinAdi()+yaziEk.getDosyaAdi(),yaziEk.getDizinAdi()+yaziEk.getDosyaAdi()))
								throw new Exception();
						}catch(Exception e){
							e.printStackTrace();
							throw new Exception("HATA:[YAZI_EK_FTP:"+yaziId+" EK :"+yaziEk.getEkId() +"] Yazı eki ftp getirme hatası.");
						}
					}
				}
				
				
			}
			System.out.println(" HYAZI "+hYazi.getSayisi()+" ZIP OLUSTUR");
			ZipDosya zip = new ZipDosya();
			hYazi = zip.zipOlustur(hYazi, yaziEkList,yaziDagitimList);
			if(hYazi==null || hYazi.getEkPath()==null || hYazi.getEkPath().equalsIgnoreCase("") || hYazi.getEkSha()==null || hYazi.getEkSha().equalsIgnoreCase("") ){
				return false;
			}
			if(srv.hariciYaziOnaylandiGuncelle(hYazi)){
				ftpKlasorKontrol(hYazi.getEkPath(),"haricizipfiles");
				ftpGonder(hYazi.getEkPath(),hYazi.getEkPath());
				return true;
			}
			/*else{
				throw new Exception("HATA:[YAZI_EK_VT:"+yaziId+"] Yazı ekleri veri tabanında  bulunamadı.");
			}*/
		}catch(Exception e){
			e.printStackTrace();
		}
		return false;
	}

	public static boolean hariciYaziBasarisizGuncelle(HariciYazilarPojo hYazi) {
		IymService srv = getIymService();
		return srv.hariciYaziBasarisizGuncelle(hYazi);
		
	}

	public static boolean iymThreadLogSonCalismaZamaniBul(String adi,int sure) {
		IymService srv = getIymService();
		return srv.iymThreadSonCalismaBul(adi,sure);
	}
	
	public static boolean iymThreadLogEkle(String adi, Date baslamaTarihi,Date bitisTarihi, String aciklama) {
		IymService srv = getIymService();
		ThreadPojo t = new ThreadPojo();
		t.setAciklama(aciklama);
		t.setBaslamaTarihi(baslamaTarihi);
		t.setBitisTarihi(bitisTarihi);
		t.setDurum("");
		t.setThreadName(adi);
		return srv.iymThreadLogEkle(t);
	}

	public static void hataMesaj(Component comp, String mesaj,
			String position) {

		Clients.showNotification(mesaj, Clients.NOTIFICATION_TYPE_ERROR, comp, position,3000,true);
	}

	public static byte[] dosyaBindEt(String dosyaYol) {

		byte[] out = null;
		FileInputStream fis = null;
		BufferedInputStream rdr = null;

		try {

			fis = new FileInputStream(dosyaYol);
			rdr = new BufferedInputStream(fis);
			int fileSize = fis.available();
			out = new byte[fileSize];
			rdr.read(out);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				rdr.close();
			} catch (Exception e) {

				e.printStackTrace();
			}
			try {
				fis.close();

			} catch (Exception e) {

				e.printStackTrace();
			}
		}
		return out;
	}
	
	public static String resimToPdfDonustur(Long evrakId) throws IOException {
		
		Rectangle pageSize;
		RandomAccessFileOrArray myFile;
		String fileName ="";
		System.out.println("RESIM TO PDF GIRIS : "+evrakId);
		IymService srv = getIymService(); 
		List<IymFilePojo> fList = srv.evrakDosyaGetir(evrakId);
		if(fList != null && fList.size()>0){
			com.lowagie.text.Document TifftoPDF = new com.lowagie.text.Document();
			com.lowagie.text.Document jpgToPdf =new com.lowagie.text.Document();
			com.lowagie.text.Document jpgToPdfTest =new com.lowagie.text.Document();
			/*EVRAKIN KAYDEDİLECEK ADINI OLUŞTURUR ********_1.***.pdf şeklinde*/
			/*int sPos = fList.get(0).getFileName().lastIndexOf("_");
			String bPos = fList.get(0).getFileName().substring(0,sPos);
			String aPos = fList.get(0).getFileName().substring(sPos);
			aPos = aPos.substring(2);
		    fileName = Utility.EVRAK_FILES_PATH+Utility.seperator+bPos+"_1"+aPos;//.toLowerCase();*/
			fileName = Utility.EVRAK_FILES_PATH+Utility.seperator+fList.get(0).getFileName();
		    //File f = new File(fileName + ".pdf");
			//if (!f.exists()) {
				int spos = fileName.lastIndexOf(".");
				String uzanti = fileName.substring(spos+1);
				if(uzanti.equalsIgnoreCase("tiff" ) || uzanti.equalsIgnoreCase("tif" )){
					try {
						System.out.println("TIFF  : "+fileName);		
						for(IymFilePojo fi : fList){
							String ekName = Utility.EVRAK_FILES_PATH+Utility.seperator+fi.getFileName();
							System.out.println("EK KONTROL  : "+ekName);
							File ek = new File(ekName);
							if (!ek.exists()) {
								Utility.evrakFtpdenAl(ekName, ekName);
								ek = new File(ekName);
								if (!ek.exists()) {
									System.out.println("EK YOK  : "+ekName);
									return fileName;
								}
							}
							myFile = new RandomAccessFileOrArray(ekName);
							
							int numberOfPages = TiffImage.getNumberOfPages(myFile);
							if (numberOfPages == 0) {
								return fileName;
							}
			
							PdfWriter.getInstance(TifftoPDF, new FileOutputStream(fileName
									+ ".pdf"));
							/* Birinci sayfanin boyutu */
							Image image1 = TiffImage.getTiffImage(myFile, 1);
							pageSize = new Rectangle(image1.getPlainWidth(),
									image1.getPlainHeight());
							TifftoPDF.setPageSize(pageSize);
							myFile.close();
							TifftoPDF.open();
							for (int i = 1; i <= numberOfPages; i++) {
								myFile = new RandomAccessFileOrArray(ekName);
								Image image = (TiffImage.getTiffImage(myFile, i));
								/* Diger Sayfalarin boyutu */
								pageSize = new Rectangle(image.getPlainWidth(),
										image.getPlainHeight());
								TifftoPDF.setPageSize(pageSize);
			
								TifftoPDF.add(image);
								myFile.close();
							}
						}
						System.out.println("TIFF  BITTI: "+fileName);
					} catch (Exception i1) {
						TifftoPDF.close();
						i1.printStackTrace();
						return fileName;
					}
				}else if(uzanti.equalsIgnoreCase("jpeg" ) || uzanti.equalsIgnoreCase("jpg" )){
					try {
						System.out.println("JPEG  : "+fileName);
						jpgToPdf.setMargins(5, 5, 5, 5);
						PdfWriter.getInstance(jpgToPdf, new FileOutputStream(fileName
								+ ".pdf"));
						jpgToPdf.open();
						for(IymFilePojo fi : fList){
							String ekName = Utility.EVRAK_FILES_PATH+Utility.seperator+fi.getFileName();
							System.out.println("EK KONTROL  : "+ekName);
							File ek = new File(ekName);
							if (!ek.exists()) {
								Utility.evrakFtpdenAl(ekName, ekName);
							    ek = new File(ekName);
								if (!ek.exists()) {
									System.out.println("EK YOK  : "+ekName);
									return fileName;
								}
							}
							Image jpeg = Image.getInstance(ekName);
							jpeg.scaleToFit(585, 825);
							System.out.println(jpeg.getPlainWidth()+"  -  "+jpeg.getPlainHeight());
							pageSize = new Rectangle(jpeg.getPlainWidth(),jpeg.getPlainHeight());;
						    jpgToPdf.setPageSize(pageSize);
							jpgToPdf.add(jpeg);
						}
						
						/**********TEST*********************/
					/*	System.out.println("JPEG  : "+fileName);
						jpgToPdfTest.setMargins(5, 5, 5, 5);
						PdfWriter.getInstance(jpgToPdfTest, new FileOutputStream(fileName
								+ "test.pdf"));
						jpgToPdfTest.open();
						for(IymFilePojo fi : fList){
							String ekName = Utility.EVRAK_FILES_PATH+Utility.seperator+fi.getFileName();
							System.out.println("EK KONTROL  : "+ekName);
							File ek = new File(ekName);
							if (!ek.exists()) {
								Utility.evrakFtpdenAl(ekName, ekName);
							    ek = new File(ekName);
								if (!ek.exists()) {
									System.out.println("EK YOK  : "+ekName);
									return fileName;
								}
							}
							Image jpeg = Image.getInstance(ekName);
							jpeg.scaleToFit(585, 825);
							jpeg.setDpi(300, 300);
							System.out.println(jpeg.getPlainWidth()+"  -  "+jpeg.getPlainHeight());
							pageSize = new Rectangle(jpeg.getPlainWidth(),jpeg.getPlainHeight());;
							jpgToPdfTest.setPageSize(pageSize);
							jpgToPdfTest.add(jpeg);
						}*/
						/*****************************************/
						
						
						System.out.println("JPEG BITTI: "+fileName);
						
					} catch (DocumentException e) {	
						jpgToPdf.close();
						jpgToPdfTest.close();
						e.printStackTrace();
						
					}
				}
			TifftoPDF.close();
			jpgToPdf.close();
			jpgToPdfTest.close();
			System.out.println("RESIM TO PDF BITTI: "+evrakId);
		}
		if(fileName.length()>0)
			return fileName+".pdf";
		return fileName;
	}
	
	public static void resimToPdfDonustur(String fileName) throws IOException {
		com.lowagie.text.Document TifftoPDF = null;
		com.lowagie.text.Document jpgToPdf = null;
		Rectangle pageSize;
		RandomAccessFileOrArray myFile;

		File f = new File(fileName);
		if (!f.exists()) {
			return;
		}

		f = new File(fileName + ".pdf");
		//if (!f.exists()) {
			int spos = fileName.lastIndexOf(".");
			String uzanti = fileName.substring(spos+1);
			if(uzanti.equalsIgnoreCase("tiff" ) || uzanti.equalsIgnoreCase("tif" )){
				try {
					
					myFile = new RandomAccessFileOrArray(fileName);
	
					int numberOfPages = TiffImage.getNumberOfPages(myFile);
					if (numberOfPages == 0) {
						return;
					}
	
					TifftoPDF = new com.lowagie.text.Document();
					PdfWriter.getInstance(TifftoPDF, new FileOutputStream(fileName
							+ ".pdf"));
					/* Birinci sayfanin boyutu */
					Image image1 = TiffImage.getTiffImage(myFile, 1);
					pageSize = new Rectangle(image1.getPlainWidth(),
							image1.getPlainHeight());
					TifftoPDF.setPageSize(pageSize);
					myFile.close();
					TifftoPDF.open();
					for (int i = 1; i <= numberOfPages; i++) {
						myFile = new RandomAccessFileOrArray(fileName);
						Image image = (TiffImage.getTiffImage(myFile, i));
						/* Diger Sayfalarin boyutu */
						pageSize = new Rectangle(image.getPlainWidth(),
								image.getPlainHeight());
						TifftoPDF.setPageSize(pageSize);
	
						TifftoPDF.add(image);
						myFile.close();
					}
	
					TifftoPDF.close();
				} catch (Exception i1) {
					TifftoPDF.close();
					i1.printStackTrace();
					return;
				}
			}else if(uzanti.equalsIgnoreCase("jpeg" ) || uzanti.equalsIgnoreCase("jpg" )){
				jpgToPdf =new com.lowagie.text.Document();
				try {
					jpgToPdf.setMargins(5, 5, 5, 5);
					PdfWriter.getInstance(jpgToPdf, new FileOutputStream(fileName
							+ ".pdf"));
					jpgToPdf.open();
					Image jpeg = Image.getInstance(fileName);
					jpeg.scaleToFit(585, 825);
					System.out.println(jpeg.getPlainWidth()+"  -  "+jpeg.getPlainHeight());
					pageSize = new Rectangle(jpeg.getPlainWidth(),jpeg.getPlainHeight());;
				    jpgToPdf.setPageSize(pageSize);
					
					jpgToPdf.add(jpeg);
					//jpgToPdf.add(p);
					
					jpgToPdf.close();
				} catch (DocumentException e) {
					jpgToPdf.close();
					e.printStackTrace();
				}
			}
	}
	
	public static String getGercekHedefTipi(String hedefTipi) {
		if(hedefTipi.equals(HEDEF_TIPLERI.GSM_YER_TESPITI_SONLANDIRMA.getHedefKodu())||
				hedefTipi.equals(HEDEF_TIPLERI.GSM_YER_TESPITI.getHedefKodu())){
			return HEDEF_TIPLERI.GSM_YER_TESPITI.getHedefKodu();
		}
		if(hedefTipi.equals(HEDEF_TIPLERI.YURTDISI_YER_TESPITI_SONLANDIRMA.getHedefKodu())|| 
				hedefTipi.equals(HEDEF_TIPLERI.YURTDISI_YER_TESPITI.getHedefKodu()))
		{
			return HEDEF_TIPLERI.YURTDISI_YER_TESPITI.getHedefKodu();
		}
		else if(hedefTipi.length() == 3){
			return hedefTipi.substring(1, 3);
		}
		return hedefTipi;
	}

	public static boolean hedeflerTalepGuncelle(Long id, String ad,String soyad, String adres) {
		IymService srv = getIymService();
		return srv.hedeflerTalepGuncelle(id,ad,soyad,adres);
	}
	
	public static boolean hedefler118CanliAlanGuncelle(Long id, String ad,String soyad, String adres) {
		IymService srv = getIymService();
		return srv.hedefler118CanliAlanGuncelle(id,ad,soyad,adres);
	}
	
	
	public static boolean canli118LogEkle(Canli118LogPojo canli118) {
		IymService srv = getIymService();
		return srv.canli118LogEkle(canli118);
	}
	
	
	
	public static String veritabaniZamaniGetir () {
		IymService srv = getIymService();
		return srv.veritabaniZamaniGetir();
	}
	
	
	public static List<EvrakDurumSorguPojo> evrakDetayByKullaniciIdEvrakNo(String evrakNo,Long kullaniciId,String kullaniciAdi) {
		IymService srv = getIymService();
		List<EvrakDurumSorguPojo> evrakList = srv.evrakDetayByKullaniciIdEvrakNo(evrakNo,kullaniciId,kullaniciAdi);

		if(evrakList == null){
			evrakList = new ArrayList<>();
		}
		return evrakList;
	}

	public static List<HedefDurumPojo> hedefDurumSorgulama(String evrakNo, String hedefNo, Long kullaniciId,String kullaniciAdi) {
		List<HedefDurumPojo> result = new ArrayList<>();
		
		IymService srv = getIymService();
		List<HedefDurumPojo> hedefDurumListesi = srv.hedefDurumSorguBy(evrakNo, kullaniciId, kullaniciAdi);
		
		if(hedefDurumListesi != null){
			for(HedefDurumPojo hedef : hedefDurumListesi){
				if(hedef.getHedefNo().equals(hedefNo) ){
					result.add(hedef);
				}
			}
		}
		
		return result;
	}

	
	public static boolean webServicekKullaniciYetkiKontrol(String kullaniciAdi, String servisAdi){
		
		
		return ServiceManager.getIymservice().webServicekKullaniciYetkiKontrol(kullaniciAdi, servisAdi);
	}


	public static boolean webServicekKullaniciYetkiKontrol(String kullaniciAdi, String servisAdi, String clientIp){
		
		Log4jService.getIymv3AuditLog().info(TemelIslemler.TurkZaman(new java.util.Date())+"|"+ kullaniciAdi + clientIp + " servis :" + servisAdi + " OTURUM GİRİŞİMİ", "canliAbone118", null, Utility.xmlLogIlslemIdGetir());
		
		return ServiceManager.getIymservice().webServicekKullaniciYetkiKontrol(kullaniciAdi, servisAdi);
	}
	
	public static boolean kismiIadeEvrakListedenKaldir(Long evrakId, long personelIymId, String clientIp) {
		IymService srv = getIymService();
		return srv.kismiIadeEvrakListedenKaldir(evrakId, personelIymId, clientIp);
		
	}
	
	public static String getFileExtension(String fileName){
		if (fileName.contains(".")){
			int spos = fileName.lastIndexOf(".");
			return fileName.substring(spos+1);
		}
		return "";
	}

	public static boolean createFolder(String path){
		File directory = new File(path);
		if (!directory.exists()){
			return directory.mkdirs();
		}
		return true;
	}
}
