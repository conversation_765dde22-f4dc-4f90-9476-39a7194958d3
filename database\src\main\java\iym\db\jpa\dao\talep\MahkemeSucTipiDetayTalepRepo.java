package iym.db.jpa.dao.talep;

import iym.common.model.entity.iym.talep.MahkemeSucTipiDetayTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for MahkemeSucTipiDetayTalep entity
 */
@Repository
public interface MahkemeSucTipiDetayTalepRepo extends JpaRepository<MahkemeSucTipiDetayTalep, Long> {

    List<MahkemeSucTipiDetayTalep> findByMahkemeKararDetayTalepId(Long mahkemeKararDetayTalepId);
    

}
