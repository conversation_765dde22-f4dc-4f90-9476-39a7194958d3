<div class="p-4">
  <!-- Başlık -->
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-2xl font-bold text-gray-800">
      <i class="pi pi-cog mr-2"></i>
      Sistem Parametre Yönetimi
    </h2>
    <div class="flex gap-2">
      <p-button 
        icon="pi pi-plus" 
        label="Yeni Parametre" 
        severity="success"
        size="small"
        (onClick)="yeniParametreEkle()">
      </p-button>
      <p-button 
        icon="pi pi-file-excel" 
        label="Excel" 
        severity="info"
        size="small"
        (onClick)="excelAktar()">
      </p-button>
    </div>
  </div>

  <!-- Arama ve Filtreleme -->
  <p-card header="Arama ve Filtreleme" class="mb-4">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
      
      <!-- Parametre Adı -->
      <div class="flex flex-col">
        <label class="text-sm font-medium text-gray-700 mb-2">Parametre Adı</label>
        <input 
          pInputText 
          [(ngModel)]="aramaFiltresi.parametreAdi"
          placeholder="Parametre adı..."
          class="w-full">
      </div>

      <!-- Parametre Grubu -->
      <div class="flex flex-col">
        <label class="text-sm font-medium text-gray-700 mb-2">Parametre Grubu</label>
        <p-dropdown 
          [(ngModel)]="aramaFiltresi.parametreGrubu"
          [options]="parametreGruplari"
          optionLabel="label"
          optionValue="value"
          placeholder="Grup seçiniz"
          class="w-full">
        </p-dropdown>
      </div>

      <!-- Aktif Durumu -->
      <div class="flex flex-col">
        <label class="text-sm font-medium text-gray-700 mb-2">Durum</label>
        <p-dropdown 
          [(ngModel)]="aramaFiltresi.aktifMi"
          [options]="aktifDurumSecenekleri"
          optionLabel="label"
          optionValue="value"
          placeholder="Durum seçiniz"
          class="w-full">
        </p-dropdown>
      </div>

      <!-- Arama Butonları -->
      <div class="flex flex-col justify-end">
        <div class="flex gap-2">
          <p-button 
            icon="pi pi-search" 
            label="Ara"
            (onClick)="parametreAra()"
            [loading]="yukleniyor"
            class="flex-1">
          </p-button>
          <p-button 
            icon="pi pi-times" 
            severity="secondary"
            (onClick)="filtreleriTemizle()"
            pTooltip="Filtreleri Temizle"
            tooltipPosition="top">
          </p-button>
        </div>
      </div>
    </div>
  </p-card>

  <!-- Sonuçlar Tablosu -->
  <p-card header="Sistem Parametreleri">
    
    <!-- Tablo Araç Çubuğu -->
    <p-toolbar class="mb-4">
      <div class="p-toolbar-group-start">
        <span class="p-input-icon-left">
          <i class="pi pi-search"></i>
          <input 
            pInputText 
            type="text" 
            [(ngModel)]="globalAramaMetni"
            (input)="globalArama()"
            placeholder="Tabloda ara..." 
            class="w-80">
        </span>
      </div>
      <div class="p-toolbar-group-end">
        <p-button 
          icon="pi pi-trash" 
          label="Seçilenleri Sil"
          severity="danger"
          size="small"
          (onClick)="topluSil()"
          [disabled]="seciliParametreler.length === 0"
          class="mr-2">
        </p-button>
        <p-button 
          icon="pi pi-refresh" 
          severity="secondary"
          size="small"
          (onClick)="parametreleriYenile()"
          pTooltip="Yenile"
          tooltipPosition="top">
        </p-button>
      </div>
    </p-toolbar>

    <!-- Tablo -->
    <p-table 
      [value]="filtrelenmisParametreler" 
      [(selection)]="seciliParametreler"
      [loading]="yukleniyor"
      [paginator]="true" 
      [rows]="20"
      [rowsPerPageOptions]="[10, 20, 50]"
      [showCurrentPageReport]="true"
      currentPageReportTemplate="{first} - {last} / {totalRecords} kayıt"
      [globalFilterFields]="['parametreAdi', 'parametreDegeri', 'parametreGrubu', 'aciklama']"
      responsiveLayout="scroll"
      styleClass="p-datatable-sm">
      
      <!-- Tablo Başlığı -->
      <ng-template pTemplate="caption">
        <div class="flex justify-between items-center">
          <span class="text-lg font-semibold">
            Toplam {{ filtrelenmisParametreler.length }} parametre
          </span>
          <span class="text-sm text-gray-600">
            {{ seciliParametreler.length }} kayıt seçili
          </span>
        </div>
      </ng-template>

      <!-- Tablo Kolonları -->
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 3rem">
            <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
          </th>
          <th pSortableColumn="parametreAdi">
            Parametre Adı
            <p-sortIcon field="parametreAdi"></p-sortIcon>
          </th>
          <th pSortableColumn="parametreDegeri">
            Değer
            <p-sortIcon field="parametreDegeri"></p-sortIcon>
          </th>
          <th pSortableColumn="parametreGrubu">
            Grup
            <p-sortIcon field="parametreGrubu"></p-sortIcon>
          </th>
          <th>Açıklama</th>
          <th pSortableColumn="aktifMi" style="width: 8rem">
            Durum
            <p-sortIcon field="aktifMi"></p-sortIcon>
          </th>
          <th pSortableColumn="olusturmaTarihi" style="width: 10rem">
            Oluşturma
            <p-sortIcon field="olusturmaTarihi"></p-sortIcon>
          </th>
          <th style="width: 12rem">İşlemler</th>
        </tr>
      </ng-template>

      <!-- Tablo Satırları -->
      <ng-template pTemplate="body" let-parametre>
        <tr>
          <td>
            <p-tableCheckbox [value]="parametre"></p-tableCheckbox>
          </td>
          <td>
            <span class="font-semibold text-blue-800">{{ parametre.parametreAdi }}</span>
          </td>
          <td>
            <span class="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
              {{ parametre.parametreDegeri }}
            </span>
          </td>
          <td>
            <p-tag 
              [value]="parametre.parametreGrubu" 
              [severity]="grupRengiGetir(parametre.parametreGrubu)">
            </p-tag>
          </td>
          <td>
            <span class="text-sm text-gray-600" [title]="parametre.aciklama">
              {{ parametre.aciklama ? (parametre.aciklama.length > 50 ? parametre.aciklama.substring(0, 50) + '...' : parametre.aciklama) : '-' }}
            </span>
          </td>
          <td>
            <div class="flex items-center">
              <p-inputSwitch 
                [(ngModel)]="parametre.aktifMi"
                (onChange)="aktifDurumDegistir(parametre)">
              </p-inputSwitch>
              <span class="ml-2 text-sm">
                <p-tag 
                  [value]="durumMetniGetir(parametre.aktifMi)" 
                  [severity]="durumSeviyesiGetir(parametre.aktifMi)">
                </p-tag>
              </span>
            </div>
          </td>
          <td>
            <span class="text-sm text-gray-600">
              {{ tarihFormatiDuzelt(parametre.olusturmaTarihi) }}
            </span>
          </td>
          <td>
            <div class="flex gap-1">
              <p-button 
                icon="pi pi-pencil" 
                severity="info"
                size="small"
                (onClick)="parametreDuzenle(parametre)"
                pTooltip="Düzenle"
                tooltipPosition="top">
              </p-button>
              <p-button 
                icon="pi pi-trash" 
                severity="danger"
                size="small"
                (onClick)="parametreSil(parametre)"
                pTooltip="Sil"
                tooltipPosition="top">
              </p-button>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Boş Durum -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="8" class="text-center py-8">
            <div class="flex flex-col items-center">
              <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
              <p class="text-gray-600">Parametre bulunamadı</p>
              <p class="text-sm text-gray-500">Arama kriterlerinizi değiştirmeyi deneyin</p>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </p-card>

  <!-- Toast Mesajları -->
  <p-toast></p-toast>
  
  <!-- Onay Dialogları -->
  <p-confirmDialog></p-confirmDialog>
</div>

<!-- Parametre Düzenleme Dialog -->
<p-dialog 
  [header]="yeniParametreMi ? 'Yeni Parametre Ekle' : 'Parametre Düzenle'" 
  [(visible)]="parametreDialogGoruntule"
  [modal]="true"
  [style]="{width: '600px'}"
  [closable]="true"
  (onHide)="parametreDialogKapat()">
  
  <div class="space-y-4">
    
    <!-- Parametre Adı -->
    <div class="flex flex-col">
      <label class="text-sm font-medium text-gray-700 mb-2">
        Parametre Adı <span class="text-red-500">*</span>
      </label>
      <input 
        pInputText 
        [(ngModel)]="seciliParametre.parametreAdi"
        placeholder="Parametre adını giriniz"
        class="w-full"
        [class.ng-invalid]="!seciliParametre.parametreAdi.trim()">
    </div>

    <!-- Parametre Değeri -->
    <div class="flex flex-col">
      <label class="text-sm font-medium text-gray-700 mb-2">
        Parametre Değeri <span class="text-red-500">*</span>
      </label>
      <input 
        pInputText 
        [(ngModel)]="seciliParametre.parametreDegeri"
        placeholder="Parametre değerini giriniz"
        class="w-full"
        [class.ng-invalid]="!seciliParametre.parametreDegeri.trim()">
    </div>

    <!-- Parametre Grubu -->
    <div class="flex flex-col">
      <label class="text-sm font-medium text-gray-700 mb-2">
        Parametre Grubu <span class="text-red-500">*</span>
      </label>
      <p-dropdown 
        [(ngModel)]="seciliParametre.parametreGrubu"
        [options]="filtrelenmisParametreGruplari"
        optionLabel="label"
        optionValue="value"
        placeholder="Grup seçiniz"
        class="w-full">
      </p-dropdown>
    </div>

    <!-- Açıklama -->
    <div class="flex flex-col">
      <label class="text-sm font-medium text-gray-700 mb-2">Açıklama</label>
      <textarea 
        pInputTextarea 
        [(ngModel)]="seciliParametre.aciklama"
        placeholder="Parametre açıklaması (isteğe bağlı)"
        rows="3"
        class="w-full">
      </textarea>
    </div>

    <!-- Aktif Durumu -->
    <div class="flex items-center">
      <p-inputSwitch 
        [(ngModel)]="seciliParametre.aktifMi"
        inputId="aktifDurum">
      </p-inputSwitch>
      <label for="aktifDurum" class="ml-3 text-sm font-medium text-gray-700">
        Parametre aktif
      </label>
    </div>

    <!-- Bilgi Mesajı -->
    <div class="bg-blue-50 border border-blue-200 rounded p-3">
      <div class="flex items-start">
        <i class="pi pi-info-circle text-blue-500 mt-0.5 mr-2"></i>
        <div class="text-sm text-blue-800">
          <p class="font-medium mb-1">Dikkat:</p>
          <ul class="list-disc list-inside space-y-1">
            <li>Parametre adı sistem genelinde benzersiz olmalıdır</li>
            <li>Kritik sistem parametrelerini değiştirirken dikkatli olun</li>
            <li>Değişiklikler anında etkili olur</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <ng-template pTemplate="footer">
    <div class="flex justify-end gap-2">
      <p-button 
        label="İptal" 
        icon="pi pi-times" 
        (onClick)="parametreDialogKapat()"
        severity="secondary">
      </p-button>
      <p-button 
        [label]="yeniParametreMi ? 'Ekle' : 'Güncelle'" 
        icon="pi pi-check" 
        (onClick)="parametreKaydet()"
        [loading]="yukleniyor"
        [disabled]="!seciliParametre.parametreAdi.trim() || !seciliParametre.parametreDegeri.trim()">
      </p-button>
    </div>
  </ng-template>
</p-dialog>
