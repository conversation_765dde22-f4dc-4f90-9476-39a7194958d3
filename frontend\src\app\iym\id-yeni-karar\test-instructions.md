# ID Yeni Karar Test Instructions

## How to Test the ID Yeni Karar Page

### Method 1: Using Browser Console

1. **Navigate to the ID Yeni Karar page** in your browser
2. **Open Developer Console** (F12)
3. **Load the test helper** by running:
   ```javascript
   // If the test helper is already loaded, skip this step
   // Otherwise, copy and paste the contents of test-helper.js into console
   ```
4. **Fill test data** by running:
   ```javascript
   fillTestData();
   ```
5. **Add additional targets** if needed:
   ```javascript
   addTestHedef();
   ```
6. **Create test file**:
   ```javascript
   createTestFile();
   ```
7. **Complete setup** (does everything at once):
   ```javascript
   setupCompleteTest();
   ```

### Method 2: Using Complete Test Setup

Run this single command to set up everything:
```javascript
setupCompleteTest();
```

This will:
- Fill all form fields with valid test data
- Add 2 sample targets (hedef)
- Create a test PDF file for upload
- Validate the form state

### Test Data Values Used

**<PERSON><PERSON><PERSON> (Document Details):**
- Evrak No: `2024-TEST-001`
- Evrak Tarihi: `2024-01-15`
- Evrak Kurum Kodu: `12345`
- Evrak Türü: `ILETISIMIN_DENETLENMESI`
- Havale Birimi: `TEST BİRİMİ`
- Geldiği İl İlçe Kodu: `3401`
- Evrak Konusu: `Test konusu - iletişimin denetlenmesi`
- Evrak Açıklama: `Test amaçlı yeni karar talebi`
- Acil: `true`

**Mahkeme Karar Detayları (Court Decision Details):**
- Mahkeme Karar Tipi: `ONLEYICI_HAKIM_KARARI`
- Mahkeme Kodu: `IST-001`
- Mahkeme Karar No: `2024/12345`
- Mahkeme İl İlçe Kodu: `3401`
- Soruşturma No: `2024-SOR-001`
- Mahkeme Açıklama: `Test mahkeme kararı açıklaması`

**Hedefler (Targets):**
1. Hedef No: `1`
   - Tip: GSM (10)
   - Ad: Ahmet, Soyad: Test
   - Başlama Tarihi: 2024-01-15
   - Süre: 30 Gün
   - BIM Kodu: BIM-001
   - Çanak No: CN-001

2. Hedef No: `2`
   - Tip: Sabit (20)
   - Ad: Ayşe, Soyad: Deneme
   - Başlama Tarihi: 2024-01-20
   - Süre: 45 Gün
   - BIM Kodu: BIM-003
   - Çanak No: CN-003

### Validation

After filling test data, you can validate the form:
```javascript
validateForm();
```

This will show:
- Form validity status
- Any validation errors
- Number of targets added
- File upload status

### Manual Testing Steps

1. **Load the page** - Ensure all controls are visible
2. **Run test setup** - Use `setupCompleteTest()`
3. **Check form validation** - Use `validateForm()`
4. **Verify all fields** - Ensure data is populated correctly
5. **Test submission** - Click "ID Yeni Karar Gönder" button
6. **Check response** - Verify success/error messages

### Troubleshooting

**If test functions don't work:**
1. Ensure you're on the ID Yeni Karar page
2. Check if Angular debugging is enabled
3. Verify the component is loaded by checking:
   ```javascript
   document.querySelector('app-id-yeni-karar')
   ```

**If form fields don't populate:**
1. Check browser console for errors
2. Ensure the component is fully loaded before running tests
3. Try running functions one by one instead of complete setup

**File upload test:**
The test creates a small PDF file. For real testing, you may want to manually select a valid PDF file.