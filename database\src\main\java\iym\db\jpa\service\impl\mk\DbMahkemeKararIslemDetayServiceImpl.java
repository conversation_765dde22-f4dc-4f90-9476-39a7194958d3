package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.DetayMahkemeKararIslem;
import iym.common.service.db.mk.DbMahkemeKararIslemDetayService;
import iym.db.jpa.dao.mk.DetayMahkemeKararIslemRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class DbMahkemeKararIslemDetayServiceImpl extends GenericDbServiceImpl<DetayMahkemeKararIslem, Long> implements DbMahkemeKararIslemDetayService {

    private final DetayMahkemeKararIslemRepo detayMahkemeKararIslemRepo;

    @Autowired
    public DbMahkemeKararIslemDetayServiceImpl(DetayMahkemeKararIslemRepo repository) {
        super(repository);
        this.detayMahkemeKararIslemRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<DetayMahkemeKararIslem> findByEvrakId(Long evrakId){
        return detayMahkemeKararIslemRepo.findByEvrakId(evrakId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<DetayMahkemeKararIslem> findByMahkemeKararTalepId(Long mahkemeKararTalepId){
        return detayMahkemeKararIslemRepo.findByMahkemeKararTalepId(mahkemeKararTalepId);
    }


}
