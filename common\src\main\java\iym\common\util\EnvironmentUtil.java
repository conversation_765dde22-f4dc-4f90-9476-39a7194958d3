package iym.common.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;

public class EnvironmentUtil {
    
    @Value("${spring.profiles.active:dev}")
    private String activeProfile;
    
    /**
     * Check if application is running in development environment
     * @return true if dev/development/local/test profile is active
     */
    public boolean isDevelopmentEnvironment() {
        return Arrays.asList("dev", "development", "local", "test")
                     .contains(activeProfile.toLowerCase());
    }
    
    /**
     * Check if application is running in production environment
     * @return true if prod/production profile is active
     */
    public boolean isProductionEnvironment() {
        return Arrays.asList("prod", "production")
                     .contains(activeProfile.toLowerCase());
    }
    
    /**
     * Get current active profile
     * @return active profile name
     */
    public String getActiveProfile() {
        return activeProfile;
    }
    
    /**
     * Check if specific profile is active
     * @param profile profile name to check
     * @return true if profile is active
     */
    public boolean isProfileActive(String profile) {
        return profile.equalsIgnoreCase(activeProfile);
    }
}
