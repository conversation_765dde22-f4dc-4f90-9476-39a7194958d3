package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.MahkemeAidiyatIslem;
import iym.common.model.entity.iym.talep.MahkemeAidiyatTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface MahkemeAidiyatIslemRepo extends JpaRepository<MahkemeAidiyatIslem, Long> {


    List<MahkemeAidiyatTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);


}
