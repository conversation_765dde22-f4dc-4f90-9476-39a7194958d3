package iym.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import org.springframework.http.HttpStatus;

/**
 * Gets or Sets ResponseCode
 */

public enum ResponseCode {

    SUCCESS,
    INVALID_REQUEST,
    FAILED;

    @Json<PERSON>reator
    public static ResponseCode fromName(String name) {
        for (ResponseCode code : ResponseCode.values()) {
            if (code.name().equals(name)) {
                return code;
            }
        }
        throw new IllegalArgumentException("Invalid code: '" + name + "'");
    }

    public HttpStatus toHttpStatus() {
        switch (this) {
            case SUCCESS -> {
                return HttpStatus.OK;
            }
            case FAILED -> {
                return HttpStatus.INTERNAL_SERVER_ERROR;
            }
            case INVALID_REQUEST -> {
                return HttpStatus.BAD_REQUEST;
            }
        }
        return HttpStatus.OK;
    }

}

