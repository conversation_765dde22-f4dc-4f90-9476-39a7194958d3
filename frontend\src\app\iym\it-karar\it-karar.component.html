<div class="p-4">
  <!-- Başlık -->
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-2xl font-bold text-gray-800">
      <i class="pi pi-desktop mr-2"></i>
      IT Karar (İletişimin Tespiti)
    </h2>
    <div class="flex gap-2">
      <p-button 
        icon="pi pi-refresh" 
        label="Sıfırla" 
        severity="secondary"
        size="small"
        (onClick)="onReset()"
        [disabled]="yukleniyor">
      </p-button>
    </div>
  </div>

  <form [formGroup]="talepForm" (ngSubmit)="onSubmit()">
    
    <!-- Evrak <PERSON>ları -->
    <p-card header="Evrak Detayları" class="mb-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        
        <!-- Evrak No -->
        <div class="flex flex-col">
          <label for="evrakNo" class="text-sm font-medium text-gray-700 mb-1">
            Evrak No <span class="text-red-500">*</span>
          </label>
          <input 
            pInputText 
            formControlName="evrakNo"
            placeholder="Evrak numarası giriniz"
            [class]="isFieldInvalid('evrakNo') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          <small *ngIf="isFieldInvalid('evrakNo')" class="text-red-500">
            {{ getFieldError('evrakNo') }}
          </small>
        </div>

        <!-- Evrak Tarihi -->
        <div class="flex flex-col">
          <label for="evrakTarihi" class="text-sm font-medium text-gray-700 mb-1">
            Evrak Tarihi <span class="text-red-500">*</span>
          </label>
          <p-calendar 
            formControlName="evrakTarihi"
            placeholder="Evrak tarihi seçiniz"
            [showIcon]="true"
            dateFormat="dd/mm/yy"
            [class]="isFieldInvalid('evrakTarihi') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          </p-calendar>
          <small *ngIf="isFieldInvalid('evrakTarihi')" class="text-red-500">
            {{ getFieldError('evrakTarihi') }}
          </small>
        </div>

        <!-- Evrak Kurum Kodu -->
        <div class="flex flex-col">
          <label for="evrakKurumKodu" class="text-sm font-medium text-gray-700 mb-1">
            Evrak Kurum Kodu <span class="text-red-500">*</span>
          </label>
          <input 
            pInputText 
            formControlName="evrakKurumKodu"
            placeholder="Kurum kodu giriniz"
            [class]="isFieldInvalid('evrakKurumKodu') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          <small *ngIf="isFieldInvalid('evrakKurumKodu')" class="text-red-500">
            {{ getFieldError('evrakKurumKodu') }}
          </small>
        </div>

        <!-- Evrak Türü -->
        <div class="flex flex-col">
          <label for="evrakTuru" class="text-sm font-medium text-gray-700 mb-1">
            Evrak Türü <span class="text-red-500">*</span>
          </label>
          <p-dropdown 
            formControlName="evrakTuru"
            [options]="evrakTuruOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="Evrak türü seçiniz"
            inputId="evrakTuru"
            [class]="isFieldInvalid('evrakTuru') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          </p-dropdown>
          <small *ngIf="isFieldInvalid('evrakTuru')" class="text-red-500">
            {{ getFieldError('evrakTuru') }}
          </small>
        </div>

        <!-- Havale Birimi -->
        <div class="flex flex-col">
          <label for="havaleBirimi" class="text-sm font-medium text-gray-700 mb-1">
            Havale Birimi
          </label>
          <input 
            pInputText 
            formControlName="havaleBirimi"
            placeholder="Havale birimi giriniz"
            class="w-full">
        </div>

        <!-- Geldiği İl İlçe Kodu -->
        <div class="flex flex-col">
          <label for="geldigiIlIlceKodu" class="text-sm font-medium text-gray-700 mb-1">
            Geldiği İl İlçe Kodu <span class="text-red-500">*</span>
          </label>
          <input 
            pInputText 
            formControlName="geldigiIlIlceKodu"
            placeholder="İl ilçe kodu giriniz"
            [class]="isFieldInvalid('geldigiIlIlceKodu') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          <small *ngIf="isFieldInvalid('geldigiIlIlceKodu')" class="text-red-500">
            {{ getFieldError('geldigiIlIlceKodu') }}
          </small>
        </div>

        <!-- Evrak Konusu -->
        <div class="flex flex-col md:col-span-2">
          <label for="evrakKonusu" class="text-sm font-medium text-gray-700 mb-1">
            Evrak Konusu
          </label>
          <input 
            pInputText 
            formControlName="evrakKonusu"
            placeholder="Evrak konusu giriniz"
            class="w-full">
        </div>

        <!-- Acil Mi -->
        <div class="flex flex-col">
          <label class="text-sm font-medium text-gray-700 mb-1">
            Durum
          </label>
          <div class="flex items-center">
            <p-checkbox 
              formControlName="acilmi"
              binary="true"
              inputId="acilmi">
            </p-checkbox>
            <label for="acilmi" class="ml-2 text-sm text-gray-700">Acil</label>
          </div>
        </div>

      </div>

      <!-- Evrak Açıklama -->
      <div class="mt-4">
        <label for="evrakAciklama" class="text-sm font-medium text-gray-700 mb-1 block">
          Evrak Açıklama
        </label>
        <textarea 
          pInputTextarea 
          formControlName="evrakAciklama"
          placeholder="Evrak hakkında açıklama giriniz"
          rows="3"
          class="w-full">
        </textarea>
      </div>
    </p-card>

    <!-- Mahkeme Karar Detayları -->
    <p-card header="IT Karar Detayları" class="mb-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        
        <!-- Mahkeme Karar Tipi -->
        <div class="flex flex-col">
          <label for="mahkemeKararTipi" class="text-sm font-medium text-gray-700 mb-1">
            IT Karar Tipi <span class="text-red-500">*</span>
          </label>
          <p-dropdown 
            formControlName="mahkemeKararTipi"
            [options]="mahkemeKararTipOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="IT karar tipi seçiniz"
            inputId="mahkemeKararTipi"
            [class]="isFieldInvalid('mahkemeKararTipi') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          </p-dropdown>
          <small *ngIf="isFieldInvalid('mahkemeKararTipi')" class="text-red-500">
            {{ getFieldError('mahkemeKararTipi') }}
          </small>
        </div>

        <!-- Mahkeme Kodu -->
        <div class="flex flex-col">
          <label for="mahkemeKodu" class="text-sm font-medium text-gray-700 mb-1">
            Mahkeme Kodu <span class="text-red-500">*</span>
          </label>
          <input 
            pInputText 
            formControlName="mahkemeKodu"
            placeholder="Mahkeme kodu giriniz"
            [class]="isFieldInvalid('mahkemeKodu') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          <small *ngIf="isFieldInvalid('mahkemeKodu')" class="text-red-500">
            {{ getFieldError('mahkemeKodu') }}
          </small>
        </div>

        <!-- Mahkeme Karar No -->
        <div class="flex flex-col">
          <label for="mahkemeKararNo" class="text-sm font-medium text-gray-700 mb-1">
            IT Karar No <span class="text-red-500">*</span>
          </label>
          <input 
            pInputText 
            formControlName="mahkemeKararNo"
            placeholder="IT karar numarası giriniz"
            [class]="isFieldInvalid('mahkemeKararNo') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          <small *ngIf="isFieldInvalid('mahkemeKararNo')" class="text-red-500">
            {{ getFieldError('mahkemeKararNo') }}
          </small>
        </div>

        <!-- Mahkeme İl İlçe Kodu -->
        <div class="flex flex-col">
          <label for="mahkemeIlIlceKodu" class="text-sm font-medium text-gray-700 mb-1">
            Mahkeme İl İlçe Kodu <span class="text-red-500">*</span>
          </label>
          <input 
            pInputText 
            formControlName="mahkemeIlIlceKodu"
            placeholder="Mahkeme il ilçe kodu giriniz"
            [class]="isFieldInvalid('mahkemeIlIlceKodu') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          <small *ngIf="isFieldInvalid('mahkemeIlIlceKodu')" class="text-red-500">
            {{ getFieldError('mahkemeIlIlceKodu') }}
          </small>
        </div>

        <!-- Soruşturma No -->
        <div class="flex flex-col">
          <label for="sorusturmaNo" class="text-sm font-medium text-gray-700 mb-1">
            Soruşturma No
          </label>
          <input 
            pInputText 
            formControlName="sorusturmaNo"
            placeholder="Soruşturma numarası giriniz"
            class="w-full">
        </div>

      </div>

      <!-- Mahkeme Açıklama -->
      <div class="mt-4">
        <label for="mahkemeAciklama" class="text-sm font-medium text-gray-700 mb-1 block">
          IT Karar Açıklama
        </label>
        <textarea 
          pInputTextarea 
          formControlName="mahkemeAciklama"
          placeholder="IT kararı hakkında açıklama giriniz"
          rows="3"
          class="w-full">
        </textarea>
      </div>
    </p-card>

    <!-- Hedef Detayları -->
    <p-card header="IT Hedef Detayları" class="mb-4">
      <div class="flex justify-between items-center mb-4">
        <span class="text-sm text-gray-600">
          Toplam {{ hedefler.length }} hedef
        </span>
        <p-button 
          icon="pi pi-plus" 
          label="Hedef Ekle" 
          severity="success"
          size="small"
          (onClick)="hedefEkleDialog()">
        </p-button>
      </div>

      <p-table 
        [value]="hedefler" 
        responsiveLayout="scroll"
        styleClass="p-datatable-sm"
        *ngIf="hedefler.length > 0">
        
        <ng-template pTemplate="header">
          <tr>
            <th>Sorgu Tipi</th>
            <th>Hedef No</th>
            <th>Karşı Hedef</th>
            <th>Başlangıç</th>
            <th>Bitiş</th>
            <th>Tespit Türü</th>
            <th>İşlemler</th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-hedef let-i="rowIndex">
          <tr>
            <td>{{ getSorguTipiLabel(hedef.sorguTipi) }}</td>
            <td>{{ hedef.hedefNo }}</td>
            <td>{{ hedef.karsiHedefNo || '-' }}</td>
            <td>{{ formatTarih(hedef.baslangicTarihi) }}</td>
            <td>{{ formatTarih(hedef.bitisTarihi) }}</td>
            <td>{{ getTespitTuruLabel(hedef.tespitTuru) }}</td>
            <td>
              <div class="flex gap-1">
                <p-button 
                  icon="pi pi-pencil" 
                  size="small"
                  severity="info"
                  (onClick)="hedefDuzenleDialog(i)"
                  pTooltip="Düzenle"
                  tooltipPosition="top">
                </p-button>
                <p-button 
                  icon="pi pi-trash" 
                  size="small"
                  severity="danger"
                  (onClick)="hedefSil(i)"
                  pTooltip="Sil"
                  tooltipPosition="top">
                </p-button>
              </div>
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage">
          <tr>
            <td colspan="7" class="text-center py-8">
              <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
              <p class="text-gray-500">Henüz hedef eklenmedi.</p>
            </td>
          </tr>
        </ng-template>
      </p-table>

      <div *ngIf="hedefler.length === 0" class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
        <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
        <p class="text-gray-500 mb-4">Henüz IT hedefi eklenmedi.</p>
        <p-button 
          icon="pi pi-plus" 
          label="İlk Hedefi Ekle" 
          severity="success"
          (onClick)="hedefEkleDialog()">
        </p-button>
      </div>
    </p-card>

    <!-- Dosya Yükleme -->
    <p-card header="IT Karar Dosyası" class="mb-4">
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-6">
        <p-fileUpload 
          name="mahkemeKararDosyasi"
          [multiple]="false"
          accept=".pdf,.doc,.docx"
          [maxFileSize]="10000000"
          [auto]="false"
          [showUploadButton]="false"
          [showCancelButton]="false"
          (onSelect)="onDosyaSecildi($event)"
          chooseLabel="IT Karar Dosyası Seç"
          chooseIcon="pi pi-file"
          class="w-full">
          
          <ng-template pTemplate="content">
            <div class="text-center">
              <i class="pi pi-cloud-upload text-4xl text-gray-400 mb-4"></i>
              <p class="text-gray-600 mb-2">IT karar dosyasını buraya sürükleyin veya seçin</p>
              <p class="text-sm text-gray-500">
                Maksimum dosya boyutu: 10MB
              </p>
              <p class="text-sm text-gray-500">
                Kabul edilen formatlar: .pdf, .doc, .docx
              </p>
            </div>
          </ng-template>
        </p-fileUpload>
      </div>
      
      <div *ngIf="seciliDosya" class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
        <div class="flex items-center">
          <i class="pi pi-file text-blue-500 mr-2"></i>
          <span class="font-medium text-blue-800">{{ seciliDosya.name }}</span>
          <span class="ml-auto text-sm text-blue-600">
            {{ (seciliDosya.size / 1024 / 1024).toFixed(2) }} MB
          </span>
        </div>
      </div>
    </p-card>

    <!-- Form Butonları -->
    <div class="flex justify-end gap-3">
      <p-button 
        type="button"
        icon="pi pi-times" 
        label="İptal" 
        severity="secondary"
        (onClick)="onReset()"
        [disabled]="yukleniyor">
      </p-button>
      
      <p-button 
        type="submit"
        icon="pi pi-desktop" 
        label="IT Karar Gönder" 
        [loading]="yukleniyor"
        [disabled]="talepForm.invalid || hedefler.length === 0 || !seciliDosya">
      </p-button>
    </div>

  </form>

  <!-- Toast Mesajları -->
  <p-toast></p-toast>
</div>

<!-- IT Hedef Ekleme/Düzenleme Dialog -->
<p-dialog
  [header]="editingHedefIndex >= 0 ? 'IT Hedef Düzenle' : 'IT Hedef Ekle'"
  [(visible)]="hedefDialogVisible"
  [modal]="true"
  [style]="{width: '80vw', maxWidth: '900px'}"
  [closable]="true"
  (onHide)="hedefDialogKapat()">

  <form [formGroup]="hedefForm">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">

      <!-- Sorgu Tipi -->
      <div class="flex flex-col">
        <label for="sorguTipi" class="text-sm font-medium text-gray-700 mb-1">
          Sorgu Tipi <span class="text-red-500">*</span>
        </label>
        <p-dropdown
          formControlName="sorguTipi"
          [options]="sorguTipiOptions"
          optionLabel="label"
          optionValue="value"
          placeholder="Sorgu tipi seçiniz"
          inputId="sorguTipi"
          [class]="isHedefFieldInvalid('sorguTipi') ? 'ng-invalid ng-dirty' : ''"
          class="w-full">
        </p-dropdown>
        <small *ngIf="isHedefFieldInvalid('sorguTipi')" class="text-red-500">
          {{ getHedefFieldError('sorguTipi') }}
        </small>
      </div>

      <!-- Hedef No -->
      <div class="flex flex-col">
        <label for="hedefNo" class="text-sm font-medium text-gray-700 mb-1">
          Hedef No <span class="text-red-500">*</span>
        </label>
        <input
          pInputText
          formControlName="hedefNo"
          placeholder="Hedef numarası giriniz"
          [class]="isHedefFieldInvalid('hedefNo') ? 'ng-invalid ng-dirty' : ''"
          class="w-full">
        <small *ngIf="isHedefFieldInvalid('hedefNo')" class="text-red-500">
          {{ getHedefFieldError('hedefNo') }}
        </small>
      </div>

      <!-- Karşı Hedef No -->
      <div class="flex flex-col">
        <label for="karsiHedefNo" class="text-sm font-medium text-gray-700 mb-1">
          Karşı Hedef No
        </label>
        <input
          pInputText
          formControlName="karsiHedefNo"
          placeholder="Karşı hedef numarası giriniz"
          class="w-full">
      </div>

      <!-- Tespit Türü -->
      <div class="flex flex-col">
        <label for="tespitTuru" class="text-sm font-medium text-gray-700 mb-1">
          Tespit Türü <span class="text-red-500">*</span>
        </label>
        <p-dropdown
          formControlName="tespitTuru"
          [options]="tespitTuruOptions"
          optionLabel="label"
          optionValue="value"
          placeholder="Tespit türü seçiniz"
          inputId="tespitTuru"
          [class]="isHedefFieldInvalid('tespitTuru') ? 'ng-invalid ng-dirty' : ''"
          class="w-full">
        </p-dropdown>
        <small *ngIf="isHedefFieldInvalid('tespitTuru')" class="text-red-500">
          {{ getHedefFieldError('tespitTuru') }}
        </small>
      </div>

      <!-- Başlangıç Tarihi -->
      <div class="flex flex-col">
        <label for="baslangicTarihi" class="text-sm font-medium text-gray-700 mb-1">
          Başlangıç Tarihi <span class="text-red-500">*</span>
        </label>
        <p-calendar
          formControlName="baslangicTarihi"
          placeholder="Başlangıç tarihi seçiniz"
          [showIcon]="true"
          dateFormat="dd/mm/yy"
          [showTime]="true"
          [class]="isHedefFieldInvalid('baslangicTarihi') ? 'ng-invalid ng-dirty' : ''"
          class="w-full">
        </p-calendar>
        <small *ngIf="isHedefFieldInvalid('baslangicTarihi')" class="text-red-500">
          {{ getHedefFieldError('baslangicTarihi') }}
        </small>
      </div>

      <!-- Bitiş Tarihi -->
      <div class="flex flex-col">
        <label for="bitisTarihi" class="text-sm font-medium text-gray-700 mb-1">
          Bitiş Tarihi <span class="text-red-500">*</span>
        </label>
        <p-calendar
          formControlName="bitisTarihi"
          placeholder="Bitiş tarihi seçiniz"
          [showIcon]="true"
          dateFormat="dd/mm/yy"
          [showTime]="true"
          [class]="isHedefFieldInvalid('bitisTarihi') ? 'ng-invalid ng-dirty' : ''"
          class="w-full">
        </p-calendar>
        <small *ngIf="isHedefFieldInvalid('bitisTarihi')" class="text-red-500">
          {{ getHedefFieldError('bitisTarihi') }}
        </small>
      </div>

      <!-- Tespit Türü Detay -->
      <div class="flex flex-col md:col-span-2">
        <label for="tespitTuruDetay" class="text-sm font-medium text-gray-700 mb-1">
          Tespit Türü Detay
        </label>
        <input
          pInputText
          formControlName="tespitTuruDetay"
          placeholder="Tespit türü detayı giriniz"
          class="w-full">
      </div>

    </div>

    <!-- Açıklama -->
    <div class="mt-4">
      <label for="aciklama" class="text-sm font-medium text-gray-700 mb-1 block">
        Açıklama
      </label>
      <textarea
        pInputTextarea
        formControlName="aciklama"
        placeholder="IT hedef açıklaması giriniz"
        rows="3"
        class="w-full">
      </textarea>
    </div>
  </form>

  <ng-template pTemplate="footer">
    <div class="flex justify-end gap-2">
      <p-button
        label="İptal"
        icon="pi pi-times"
        (onClick)="hedefDialogKapat()"
        severity="secondary">
      </p-button>
      <p-button
        [label]="editingHedefIndex >= 0 ? 'Güncelle' : 'Ekle'"
        icon="pi pi-check"
        (onClick)="hedefKaydet()"
        [disabled]="hedefForm.invalid">
      </p-button>
    </div>
  </ng-template>
</p-dialog>
