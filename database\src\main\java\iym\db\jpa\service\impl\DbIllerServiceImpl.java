package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.Iller;
import iym.common.service.db.DbIllerService;
import iym.db.jpa.dao.IllerRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for Iller entity
 */
@Service
public class DbIllerServiceImpl extends GenericDbServiceImpl<Iller, String> implements DbIllerService {

    private final IllerRepo illerRepo;

    @Autowired
    public DbIllerServiceImpl(IllerRepo repository) {
        super(repository);
        this.illerRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Iller> getByIlIlceKodu(String ilIlceKodu){
        return illerRepo.findByIlKod(ilIlceKodu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Iller> findByIlAdi(String ilAdi) {
        return illerRepo.findByIlAdi(ilAdi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Iller> findByIlceAdi(String ilceAdi) {
        return illerRepo.findByIlceAdi(ilceAdi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Iller> findByIlAdiAndIlceAdi(String ilAdi, String ilceAdi) {
        return illerRepo.findByIlAdiAndIlceAdi(ilAdi, ilceAdi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Iller> findByIlKodStartingWith(String ilKodPrefix) {
        return illerRepo.findByIlKodStartingWith(ilKodPrefix);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Iller> findByIlAdiContainingIgnoreCase(String ilAdi) {
        return illerRepo.findByIlAdiContainingIgnoreCase(ilAdi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Iller> findByIlceAdiContainingIgnoreCase(String ilceAdi) {
        return illerRepo.findByIlceAdiContainingIgnoreCase(ilceAdi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Iller> findAllByOrderByIlAdiAsc() {
        return illerRepo.findAllByOrderByIlAdiAsc();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Iller> findAllByOrderByIlKodAsc() {
        return illerRepo.findAllByOrderByIlKodAsc();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Iller> findByIlAdiOrderByIlceAdiAsc(String ilAdi) {
        return illerRepo.findByIlAdiOrderByIlceAdiAsc(ilAdi);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByIlAdiAndIlceAdi(String ilAdi, String ilceAdi) {
        return illerRepo.existsByIlAdiAndIlceAdi(ilAdi, ilceAdi);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsById(String id) {
        return illerRepo.existsById(id);
    }
}
