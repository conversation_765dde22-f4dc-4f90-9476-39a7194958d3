package gov.tib.iym.service;

import java.io.StringReader;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.RowMapper;
import org.zkoss.zhtml.Messagebox;

import gov.tib.iym.mahkemekarar.base.model.HedefDurumPojo;
import gov.tib.iym.mahkemekarar.base.model.HedefTipleriPojo;
import gov.tib.iym.mahkemekarar.base.model.MAHKEME_KARAR_ISLEM_TURU;
import gov.tib.iym.mahkemekarar.base.model.MAHKEME_KARAR_TIPLERI;
import gov.tib.iym.mahkemekarar.base.model.MahkemeKararKodlariPojo;
import gov.tib.iym.mahkemekarar.base.model.MahkemeKodlariPojo;
import gov.tib.iym.mahkemekarar.base.model.MahkemeSucTipleriPojo;
import gov.tib.iym.mahkemekarar.model.EvrakDurumSorguPojo;
import gov.tib.iym.mahkemekarar.model.HariciYazilarPojo;
import gov.tib.iym.mahkemekarar.model.HedeflerAidiyatTalepPojo;
import gov.tib.iym.mahkemekarar.model.HedeflerDetayTalepPojo;
import gov.tib.iym.mahkemekarar.model.HedeflerTalepPojo;
import gov.tib.iym.mahkemekarar.model.HtsHedeflerTalepPojo;
import gov.tib.iym.mahkemekarar.model.HtsMahkemeKararTalepPojo;
import gov.tib.iym.mahkemekarar.model.IllerPojo;
import gov.tib.iym.mahkemekarar.model.MahkemeAidiyatDetayTalepPojo;
import gov.tib.iym.mahkemekarar.model.MahkemeAidiyatTalepPojo;
import gov.tib.iym.mahkemekarar.model.MahkemeHedeflerAidiyatTalepPojo;
import gov.tib.iym.mahkemekarar.model.MahkemeKararDetayTalepPojo;
import gov.tib.iym.mahkemekarar.model.MahkemeKararTalepPojo;
import gov.tib.iym.mahkemekarar.model.MahkemeSuclarTalepPojo;
import gov.tib.iym.mahkemekarar.model.SorguTipleriPojo;
import gov.tib.iym.mahkemekarar.model.TespitTurleriPojo;
import gov.tib.iym.mahkemekarar.model.XmlEvrakPojo;
import gov.tib.iym.mahkemekarar.xmlparse.EVRAK_GELEN_KURUMLAR;
import gov.tib.iym.mahkemekarar.xmlparse.EVRAK_KAYIT;
import gov.tib.iym.mahkemekarar.xmlparse.EVRAK_TIPLERI;
import gov.tib.iym.mahkemekarar.xmlparse.HEDEFLER;
import gov.tib.iym.mahkemekarar.xmlparse.HEDEFLER_DETAY;
import gov.tib.iym.mahkemekarar.xmlparse.ITK_HEDEFLER;
import gov.tib.iym.mahkemekarar.xmlparse.MAHKEME_KARAR;
import gov.tib.iym.mahkemekarar.xmlparse.MAHKEME_KARAR_DETAY;
import gov.tib.iym.mahkemekarar.xmlparse.Utility;
import gov.tib.iym.mahkemekarar.xmlparse.XMLHATA;
import gov.tib.iym.model.CanakNumaraPojo;
import gov.tib.iym.model.Canli118LogPojo;
import gov.tib.iym.model.HedefIstatistikPojo;
import gov.tib.iym.model.HitapKurallarPojo;
import gov.tib.iym.model.ISYSTalepFormlariPojo;
import gov.tib.iym.model.IymEvrakAramaDetayPojo;
import gov.tib.iym.model.IymEvrakAramaPojo;
import gov.tib.iym.model.IymEvrakPojo;
import gov.tib.iym.model.IymFilePojo;
import gov.tib.iym.model.IymGelenEvrakPojo;
import gov.tib.iym.model.IymGidenEvrakPojo;
import gov.tib.iym.model.IymKurumPojo;
import gov.tib.iym.model.IymYaziDagitimPojo;
import gov.tib.iym.model.IymYaziEkDosyaPojo;
import gov.tib.iym.model.IymYaziEvrakIliskiPojo;
import gov.tib.iym.model.IymYaziIslemDetayPojo;
import gov.tib.iym.model.IymYaziPojo;
import gov.tib.iym.model.ThreadPojo;
import gov.tib.kim.model.KimLogPojo;
import gov.tib.kim.model.NumaraImeiSorguLogPojo;
import gov.tib.util.TemelIslemler;
import jdbc.TemelDAO;
import jdbc.TemelJDBC;
import service.Log4jService;
import service.ServiceManager;
import service.TemelService;

public class IymService extends TemelService {
	
	private static final Logger logger = LoggerFactory.getLogger(IymService.class);
	
	private static class SpKontrolRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int adet) throws SQLException {
			HitapKurallarPojo ht = new HitapKurallarPojo();
			// try {
			ht.setIl(rs.getString("IL"));
			ht.setHizmetTip(rs.getString("HIZMET_TIPI"));
			ht.setImsAd(rs.getString("IMS_ADI"));
			// } catch (Exception e) {
			// throw ()
			// }
			return ht;
		}
	}

	public List<HitapKurallarPojo> spKontrol(String prefix) {

		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("SELECT il,ims_adi,hizmet_tipi FROM hitap.hitap_kurallar WHERE hedef_tipi ="
				+ " 'MSISDN' AND il IS NOT NULL AND cikma_tarihi IS NULL AND ? BETWEEN hedef_baslangic AND hedef_bitis");
		tjdbc.setData(new Object[] { prefix });
		tjdbc.setType(new int[] { Types.VARCHAR });
		// List l = getDao().bul(tjdbc);
		tjdbc.setRowMapper(new SpKontrolRowMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	public Boolean alanKodCheck(String prefix) {
		Integer adet;

		adet = (Integer) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						"select count(*) from hitap.hitap_kurallar where rownum<2 and hedef_baslangic like ?",
						new Object[] { prefix.substring(0, 5) + "%" },
						new int[] { Types.VARCHAR }, Integer.class);

		if (adet > 0)
			return true;
		else
			return false;
	}

	public Integer hukukdaBekleyenHedefSayisi_24052011(String gorevKodu) {
		Integer adet;

		adet = (Integer) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						"select count(1) from hedefler_talep h where h.DURUMU ='ONAYLANDI' and h.mahkeme_karar_id in (select id from mahkeme_karar_talep where evrak_id in (SELECT id"
								+ "  FROM evrak_kayit e WHERE e.DURUMU='ONAYLANDI' and e.havale_birim IN (SELECT birim_kod FROM gorev_birimler gb WHERE gb.gorev_kod IN (?))"
								+ "   AND e.giris_tarih > SYSDATE - 7) )",
						new Object[] { gorevKodu },
						new int[] { Types.VARCHAR }, Integer.class);
		return adet;
	}

	public Integer hukukdaBekleyenHedefSayisi(Long userId) {
		Integer adet;

		adet = (Integer) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						"SELECT count(*)  FROM hedefler_talep h WHERE h.DURUMU = 'ONAYLANDI'  AND h.mahkeme_karar_id IN   (SELECT id FROM mahkeme_karar_talep "
								+ " WHERE evrak_id IN (SELECT id FROM evrak_kayit e  WHERE e.DURUMU = 'ONAYLANDI'  AND E.EVRAK_GELDIGI_KURUM IN (SELECT kurum_kod "
								+ " FROM kullanici_kurum  WHERE kullanici_id=?) AND e.giris_tarih > SYSDATE - 7))",
						new Object[] { userId }, new int[] { Types.BIGINT },
						Integer.class);

		return adet;
	}

	public Integer gelenEvrakSayisi(Long kullaniciKod) {
		Integer adet;

		adet = (Integer) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						"select count(*) from evrak_havale h,evrak_kayit t where t.id=h.evrak_id  and h.durum is null "
								+ "and h.havale_edilen_id=?",
						new Object[] { kullaniciKod },
						new int[] { Types.BIGINT }, Integer.class);
		return adet;
	}

	public class GidenEvrakListeRowMapper implements RowMapper {

		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			IymGidenEvrakPojo gr = new IymGidenEvrakPojo();
			gr.setId(rs.getLong("yazi_id"));
			gr.setSayi(rs.getString("sayisi"));
			gr.setKonu(rs.getString("konusu"));
			gr.setKurumBilgileri(rs.getString("kurum"));
			gr.setHavaleEden(rs.getString("havale_Eden"));
			gr.setTarih(rs.getString("kayit_tarihi"));
			gr.setHavaleNeden(rs.getString("neden"));
			gr.setAcilmi(rs.getString("acilmi"));

			return gr;
		}

	}

	public List gidenEvrakListele(Long kullaniciID) {

		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("SELECT v.evrak_ID as yazi_id, v.id ,e.sayisi, e.konusu , e.\"Kurum\", v.\"Havale Eden\" || '-' || v.\"Kayıt Tarihi\" AS havale_eden,"
				// +
				// " to_char(nvl(ev.giris_tarih,e.kayit_tarihi),'dd.mm.yyyy') as Kayit_Tarihi,"
				+ " TO_CHAR (NVL (e.imza_tarih, e.tarih), 'dd.mm.yyyy') AS Kayit_Tarihi,"
				+ " (select tur_adi from HAVALE_TURLERI h where to_number(h.TUR_KODU)=v.sebebi) AS neden, NVL (e.acilmi, 'H') AS acilmi, e.yazi_turu FROM yazilar_view e, evrak_havale2_view v, evrak_kayit ev WHERE e.ID = v.evrak_id "
				+ " AND v.durum IS NULL AND e.ust_evrak_sira_no = ev.evrak_sira_no(+) AND v.havale_edilen_id =?   ORDER BY acilmi, e.imza_tarih desc Nulls last");
		tjdbc.setData(new Object[] { kullaniciID });
		tjdbc.setType(new int[] { Types.BIGINT });

		// System.out.println("kullaniciID : " + kullaniciID + " sql :" +
		// tjdbc.getStatement());

		tjdbc.setRowMapper(new GidenEvrakListeRowMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	public class GelenEvrakListeRowMapper implements RowMapper {

		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			IymGelenEvrakPojo gr = new IymGelenEvrakPojo();

			gr.setEvrakId(rs.getLong("evrak_ID"));
			gr.setEvrakHaveleId(rs.getLong("id"));
			gr.setEvrakSiraNo(rs.getString("evrak_Sira_no"));
			gr.setEvrakKonu(rs.getString("konu"));
			gr.setSorusturmaNo(rs.getString("sorusturma_no"));
			gr.setGelenKurumIl(rs.getString("GeldigiKurumil"));
			gr.setHavaleEden(rs.getString("havale_Eden"));
			gr.setTarih(rs.getString("kayit_tarihi"));
			gr.setHavaleNeden(rs.getString("neden"));
			gr.setAcilmi(rs.getString("acilmi"));
			gr.setHavaleEdenId(rs.getLong("havale_eden_id"));
			return gr;
		}

	}

	public List gelenEvrakListele(Long kullaniciID) {

		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("SELECT   e.ID as evrak_id,v.id ,  e.EVRAK_SIRA_NO , e.\"Evrak Konusu\" as konu , e.sorusturma_no , e.\"Geldiği Kurum\" || '-' || e.\"Geldiği il\" AS GeldigiKurumIl,"
				+ " v.\"Havale Eden\" || '-' || v.\"Kayıt Tarihi\" AS havale_eden,e.\"Kayit Tarihi\","
				+ " to_char (e.\"Kayit Tarihi\",'dd.mm.yyyy') AS Kayit_Tarihi,  (select tur_adi from HAVALE_TURLERI h where to_number(h.TUR_KODU)=v.sebebi) AS neden,"
				+ "e.ACILMI,v.havale_eden_id FROM evrak_kayit_view e, evrak_havale_view v WHERE e.ID = v.evrak_id AND v.durum IS NULL AND v.havale_edilen_id = ? ORDER BY NVL (e.acilmi, 'H'), (8) desc");
		tjdbc.setData(new Object[] { kullaniciID });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new GelenEvrakListeRowMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	public Integer gidenEvrakSayisi(Long kullaniciKod) {
		Integer adet;

		adet = (Integer) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						"select count(*) from evrak_havale2 where evrak_id in (select id from yazilar where "
								+ "durum=\'ONAYLANDI\' or durum=\'ARSIV\') and durum is null  and havale_edilen_id=?",
						new Object[] { kullaniciKod },
						new int[] { Types.BIGINT }, Integer.class);
		return adet;
	}

	private static class HedefIstatistikRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int adet) throws SQLException {
			HedefIstatistikPojo ht = new HedefIstatistikPojo();
			// try {
			ht.setDurum(rs.getString("DURUMU"));
			// ht.setKurum(rs.getString("KURUM"));
			ht.setToplam(rs.getString("TOPLAM"));
			ht.setHedefTip(rs.getString("hedef_tipi"));

			return ht;
		}
	}

	public List<HedefIstatistikPojo> hedefIstatistikGetir_24052011(
			String durum, String havaleBirimKod) {
		String whereDurum = "";
		if (durum.equalsIgnoreCase("")) {
			whereDurum = "h.durumu is null";
		} else {
			whereDurum = "h.durumu ='" + durum + "'";
		}

		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("SELECT ht.hedef_tipi,durumu, COUNT (*) AS toplam  FROM hedefler h,hedef_tipleri ht  WHERE h.HEDEF_TIPI=HT.HEDEF_KODU and "
				+ whereDurum
				+ " and mahkeme_karar_id IN ( SELECT ID"
				+ " FROM mahkeme_karar WHERE evrak_id IN ( SELECT ID FROM evrak_kayit e WHERE e.durumu = 'ONAYLANDI' AND e.havale_birim IN (SELECT birim_kod FROM gorev_birimler gb"
				+ " WHERE gb.gorev_kod IN (?)) AND e.giris_tarih > SYSDATE - 7)) GROUP BY ht.hedef_tipi,durumu order by ht.hedef_tipi");
		tjdbc.setData(new Object[] { havaleBirimKod });
		tjdbc.setType(new int[] { Types.VARCHAR });

		/*
		 * System.out.println(
		 * "SELECT ht.hedef_tipi,durumu, COUNT (*) AS toplam  FROM hedefler h,hedef_tipleri ht  WHERE h.HEDEF_TIPI=HT.HEDEF_KODU and "
		 * + whereDurum + " and mahkeme_karar_id IN ( SELECT ID" +
		 * " FROM mahkeme_karar WHERE evrak_id IN ( SELECT ID FROM evrak_kayit e WHERE e.durumu = 'ONAYLANDI' AND e.havale_birim IN (SELECT birim_kod FROM gorev_birimler gb"
		 * +
		 * " WHERE gb.gorev_kod IN (?)) AND e.giris_tarih > SYSDATE - 7)) GROUP BY ht.hedef_tipi,durumu order by ht.hedef_tipi"
		 * );
		 */
		tjdbc.setRowMapper(new HedefIstatistikRowMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	public List<HedefIstatistikPojo> hedefIstatistikGetir(String durum,
			Long kullaniciId) {
		String whereDurum = "";
		if (durum.equalsIgnoreCase("")) {
			whereDurum = "h.durumu is null";
		} else {
			whereDurum = "h.durumu ='" + durum + "'";
		}

		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("SELECT ht.hedef_tipi,durumu, COUNT (*) AS toplam  FROM hedefler h,hedef_tipleri ht  WHERE h.HEDEF_TIPI=HT.HEDEF_KODU and "
				+ whereDurum
				+ " and mahkeme_karar_id IN ( SELECT ID"
				+ " FROM mahkeme_karar WHERE evrak_id IN ( SELECT ID FROM evrak_kayit e WHERE e.durumu = 'ONAYLANDI' AND E.EVRAK_GELDIGI_KURUM IN "
				+ "(SELECT kurum_kod FROM kullanici_kurum     WHERE kullanici_id=?) AND e.giris_tarih > SYSDATE - 7)) GROUP BY ht.hedef_tipi,durumu order by ht.hedef_tipi");
		tjdbc.setData(new Object[] { kullaniciId });
		tjdbc.setType(new int[] { Types.BIGINT });

		System.out
				.println("SELECT ht.hedef_tipi,durumu, COUNT (*) AS toplam  FROM hedefler h,hedef_tipleri ht  WHERE h.HEDEF_TIPI=HT.HEDEF_KODU and "
						+ whereDurum
						+ " and mahkeme_karar_id IN ( SELECT ID"
						+ " FROM mahkeme_karar WHERE evrak_id IN ( SELECT ID FROM evrak_kayit e WHERE e.durumu = 'ONAYLANDI' AND E.EVRAK_GELDIGI_KURUM IN "
						+ "(SELECT kurum_kod FROM kullanici_kurum     WHERE kullanici_id=?) AND e.giris_tarih > SYSDATE - 7)) GROUP BY ht.hedef_tipi,durumu order by ht.hedef_tipi");

		tjdbc.setRowMapper(new HedefIstatistikRowMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	//

	public class IlMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			// IymEvrakPojo il = new IymEvrakPojo();
			// il.setEvrakGelenIl(rs.getString("IL_ADI"));
			// il.setEvrakGelenIlKod(rs.getString("IL_KOD"));
			// // System.out.println(rs.getString("IL_ADI"));
			IllerPojo il = new IllerPojo();
			il.setIlAdi(rs.getString("IL_ADI"));
			il.setIlKod(rs.getString("IL_KOD"));
			return il;
		}
	}

	public List<IllerPojo> ilListGetir() {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("select il_kod,il_adi from iller order by il_adi");
		tjdbc.setData(new Object[] {});
		tjdbc.setType(new int[] {});
		tjdbc.setRowMapper(new IlMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	public class kurumMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			IymKurumPojo kurum = new IymKurumPojo();
			kurum.setKurumAd(rs.getString("kurum_adi"));
			kurum.setKurumKod(rs.getString("kurum_kod"));

			return kurum;
		}
	}

	public List<IymEvrakPojo> kurumListGetir(Long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("	select e.kurum_adi,e.kurum_kod from EVRAK_GELEN_KURUMLAR e, "
				+ "KULLANICI_KURUM k where e.KURUM_KOD=k.KURUM_KOD and  k.kullanici_id=?");
		tjdbc.setData(new Object[] { kullaniciId });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new kurumMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	public class EvrakAramaRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			IymEvrakAramaPojo evr = new IymEvrakAramaPojo();

			evr.setEvrakId(rs.getString("id"));
			evr.setEvrakGirisTarihi(rs.getTimestamp("giris_tarih"));
			evr.setEvrakSiraNo(rs.getString("evrak_sira_no"));
			evr.setEvrakNo(rs.getString("evrak_no"));
			evr.setEvrakDurum(rs.getString("durum"));
			evr.setEvrakAcilMi(rs.getString("acilmi"));
			evr.setEvrakGelenIl(rs.getString("il_adi"));
			evr.setMahkemeAd(rs.getString("mahkeme_adi"));
			evr.setMahkemeKararNo(rs.getString("mahkeme_karar_no"));
			evr.setMahkemeKararTip(rs.getString("karar_tip"));
			evr.setEvrakGelenKurumKod(rs.getString("evrak_geldigi_kurum"));
			evr.setSorusturmaNo(rs.getString("sorusturma_no"));
			evr.setMahkemeIl(rs.getString("mahkeme_ili"));

			return evr;
		}
	}
	

	public String kurumGetir(Long kullaniciId) {
		String sonuc = null;

		try {
			sonuc = (String) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							"select temsil_edilen_kurum from kullanicilar where id = ? and durumu = 'A' ",
							new Object[] { kullaniciId },
							new int[] { Types.BIGINT }, String.class);
		} catch (Exception e) {

		}

		return sonuc;
	}


	
	public List<IymEvrakAramaPojo> evrakAra(IymEvrakAramaPojo evrak,
			Date sorguBaslama, Date sorguBitis, Long kullaniciId,String kullaniciAdi) {
		TemelJDBC tjdbc = new TemelJDBC();

		String kosulStr = "";

		List<Object> data = new ArrayList();
		List<Integer> type = new ArrayList();
		// koşuldaki kullanici id alanı için eklendi
		data.add(kullaniciId);
		type.add(Types.BIGINT);

		if (!TemelIslemler.bosMu(evrak.getEvrakSiraNo())) {
			kosulStr += " ev.evrak_sira_no like ? and ";
			data.add("%" + evrak.getEvrakSiraNo());
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getEvrakNo())) {
			kosulStr += " ev.evrak_no like ? and ";

			data.add(evrak.getEvrakNo() + "%");
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getEvrakKonu())) {
			kosulStr += " ev.evrak_konusu like ? and ";
			data.add("%" + evrak.getEvrakKonu() + "%");
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(sorguBaslama)) {
			kosulStr += " trunc(ev.giris_tarih)>= ? and ";
			data.add(sorguBaslama);
			type.add(Types.VARCHAR);
		}

		String kurum = kurumGetir(kullaniciId);
		// if (1 == 1) {
		// Tib karar girici ve B kurum sadece kendi girdigi evrakı gorebilir...
		if (kurum == null || "B".equals(kurum) || kullaniciId.equals(2779L)) {
			kosulStr += " ev.kay_kullanici= ? and ";
			data.add(kullaniciId);
			type.add(Types.BIGINT);
		}

		if (!TemelIslemler.bosMu(sorguBitis)) {
			kosulStr += " trunc(ev.giris_tarih)<= ? and ";
			data.add(sorguBitis);
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getEvrakGelenKurumKod())) {
			kosulStr += " ev.evrak_geldigi_kurum =? and ";
			data.add(evrak.getEvrakGelenKurumKod());
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getEvrakGelenIl())) {
			kosulStr += " ev.gel_il = (select il_kod from iller where il_adi =?) and ";
			data.add(evrak.getEvrakGelenIl());
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getMahkemeIl())) {
			kosulStr += " mk.mahkeme_ili = (select il_kod from iller where il_adi =?) and ";
			data.add(evrak.getMahkemeIl());
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getEvrakAciklama())) {
			kosulStr += " ev.aciklama like ? and ";
			data.add("%" + evrak.getEvrakAciklama() + "%");
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getMahkemeKararNo())) {
			kosulStr += " mk.mahkeme_karar_no = ? and ";
			data.add(evrak.getMahkemeKararNo());
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getHedefNo())) {
			kosulStr += " mk.ID in (select mahkeme_karar_id from hedefler where  hedef_no = ? ) and ";
			data.add(evrak.getHedefNo());
			type.add(Types.VARCHAR);
		}
		
		if (kosulStr.length() > 0) {			
			kosulStr = " and " + kosulStr.substring(0, kosulStr.length() - 5);
			kosulStr += "  order by ev.id desc) where rownum<101";
		} else {
			kosulStr = " and giris_tarih > sysdate - 10 order by ev.id desc) where  rownum<101";
		}

		tjdbc.setStatement("	 select * from (	select  ev.id,ev.giris_tarih,ev.evrak_sira_no, ev.evrak_no,ev.ACILMI,il.il_adi, ev.evrak_geldigi_kurum, mk.mahkeme_adi, mk.mahkeme_karar_no, "
				+ " mk.sorusturma_no,(select il_adi from iller i where i.il_kod = mk.mahkeme_ili) as mahkeme_ili,"
				+ "(Select mkt.KARAR_TIPI from mah_karar_tipleri mkt where  mkt.KARAR_KODU=mk.KARAR_TIP ) as KARAR_TIP, "
				+ "CASE "
				+ "WHEN  (MK.KARAR_TIP IN(510,520,530) AND (SELECT COUNT(*) FROM MAHKEME_KARAR K WHERE  mk.id=k.id AND k.durum='ONAYLANDI')>0) THEN 'TANIMLANDI' "
				+ "WHEN (SELECT COUNT (*) FROM hedefler h1, mahkeme_karar mr  WHERE h1.mahkeme_karar_id = mr.ID  AND mr.ID = mk.ID  AND (h1.durumu IS NULL OR h1.durumu = 'TANIMLANAMADI')) >0  "
				+ "OR (SELECT COUNT (*)  FROM hedefler h1, mahkeme_karar mr  WHERE h1.mahkeme_karar_id = mr.ID  AND mr.ID = mk.ID  AND h1.durumu = 'TANIMLANDI') = 0  THEN 'İŞLEMDE' "
				+ "WHEN (SELECT COUNT (*)  FROM hedefler h1, mahkeme_karar mr  WHERE h1.mahkeme_karar_id = mr.ID  AND mr.ID = mk.ID  AND (h1.durumu IS NULL OR h1.durumu = 'TANIMLANAMADI')) =0  "
				+ "OR (SELECT COUNT (*)  FROM hedefler h1, mahkeme_karar mr  WHERE h1.mahkeme_karar_id = mr.ID  AND mr.ID = mk.ID  AND h1.durumu = 'TANIMLANDI')>0  then'TANIMLANDI'  "
				+ "END AS durum  "
				+ "FROM evrak_kayit ev, mahkeme_karar mk, iller il  "
				+ " WHERE ev.gel_il = il.il_kod   "
				+ "AND ev.ID = mk.evrak_id(+) and ev.durumu in ('ONAYLANDI','IDKBEKLEME') "
				+ "AND      ev.evrak_geldigi_kurum IN (SELECT ek.kurum_kod FROM evrak_gelen_kurumlar ek, kullanici_kurum kk WHERE ek.kurum_kod = kk.kurum_kod  "
				+ "AND kk.kullanici_id = ?) AND ev.KAY_KULLANICI IN (SELECT id  FROM kullanicilar WHERE TEMSIL_EDILEN_KURUM IS NOT NULL) "
				+ kosulStr);
		

		int types[] = new int[type.size()];
		for (int i = 0; i < type.size(); i++) {
			types[i] = (Integer) type.get(i);
		}

		tjdbc.setData(data.toArray());
		tjdbc.setRowMapper(new EvrakAramaRowMapper());

		System.out.println(tjdbc.getStatement());

		List<IymEvrakAramaPojo> list =  getDao().bulRowMapper(tjdbc);
		
		evrakAramaLogKaydet(evrak, sorguBaslama, sorguBitis, kullaniciId,"IYM",kullaniciAdi);
		
		return list;
		
	}

	public void evrakAramaLogKaydet(IymEvrakAramaPojo evrak, Date sorguBaslama,Date sorguBitis, Long kullaniciId,String kaynak,String kullaniciAd) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("insert into xml_evrak_arama_log (id,kullanici_id,islem_tarihi,sorgu_bas_tarihi," +
						   "sorgu_bit_tarihi,evrak_sira_no,evrak_no,evrak_konusu,evrak_gelen_kurum,evrak_gelen_il" +
						   ",mahkeme_il,aciklama,mahkeme_karar_no,hedef_no,kaynak,kullanici_ad)"
				+ " values (xml_evrak_arama_log_seq.nextval,?,sysdate,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		tjdbc.setData(new Object[] { kullaniciId,sorguBaslama,sorguBitis,evrak.getEvrakSiraNo(),
				evrak.getEvrakNo(),evrak.getEvrakKonu(),evrak.getEvrakGelenKurumKod(),
				evrak.getEvrakGelenIl(),evrak.getMahkemeIl(),evrak.getEvrakAciklama(),
				evrak.getMahkemeKararNo(),evrak.getHedefNo(),kaynak,kullaniciAd });
		tjdbc.setType(new int[] { 
				Types.BIGINT, Types.DATE, Types.DATE,Types.VARCHAR,
				Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,
				Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR
				});

		try {
			getDao().ekle(tjdbc);
			Log4jService.getIymv3EvrakAramaLog().info(TemelIslemler.TurkZaman(new java.util.Date())+"|"+tjdbc.getStatement()+"|"+kullaniciId+"|"+sorguBaslama+"|"+sorguBitis+"|"+evrak.getEvrakSiraNo()+"|"+
					evrak.getEvrakNo()+"|"+evrak.getEvrakKonu()+"|"+evrak.getEvrakGelenKurumKod()+"|"+evrak.getEvrakGelenIl()+"|"+evrak.getMahkemeIl()+"|"+evrak.getEvrakAciklama()+"|"+
					"|"+evrak.getMahkemeKararNo()+"|"+evrak.getHedefNo()+"|"+kaynak+"|"+kullaniciAd);
		}catch(Exception e){
			e.printStackTrace();
		}
	}

	public List<IymEvrakAramaPojo> evrakAraTemsilci(IymEvrakAramaPojo evrak,
			Date sorguBaslama, Date sorguBitis, Long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();

		String kosulStr = "";

		List<Object> data = new ArrayList();
		List<Integer> type = new ArrayList();
		// koşuldaki kullanici id alanı için eklendi
		data.add(kullaniciId);
		type.add(Types.BIGINT);

		if (!TemelIslemler.bosMu(evrak.getEvrakSiraNo())) {
			kosulStr += " ev.evrak_sira_no like ? and ";
			data.add("%" + evrak.getEvrakSiraNo());
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getEvrakNo())) {
			kosulStr += " ev.evrak_no like ? and ";

			data.add(evrak.getEvrakNo() + "%");
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getEvrakKonu())) {
			kosulStr += " ev.evrak_konusu like ? and ";
			data.add("%" + evrak.getEvrakKonu() + "%");
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(sorguBaslama)) {
			kosulStr += " trunc(ev.giris_tarih)>= ? and ";
			data.add(sorguBaslama);
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(sorguBitis)) {
			kosulStr += " trunc(ev.giris_tarih)<= ? and ";
			data.add(sorguBitis);
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getEvrakGelenKurumKod())) {
			kosulStr += " ev.evrak_geldigi_kurum =? and ";
			data.add(evrak.getEvrakGelenKurumKod());
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getEvrakGelenIl())) {
			kosulStr += " ev.gel_il = (select il_kod from iller where il_adi =?) and ";
			data.add(evrak.getEvrakGelenIl());
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getMahkemeIl())) {
			kosulStr += " mk.mahkeme_ili = (select il_kod from iller where il_adi =?) and ";
			data.add(evrak.getMahkemeIl());
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getEvrakAciklama())) {
			kosulStr += " ev.aciklama like ? and ";
			data.add("%" + evrak.getEvrakAciklama() + "%");
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getMahkemeKararNo())) {
			kosulStr += " mk.mahkeme_karar_no = ? and ";
			data.add(evrak.getMahkemeKararNo());
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getHedefNo())) {
			kosulStr += " mk.ID in (select mahkeme_karar_id from hedefler where  hedef_no = ? ) and ";
			data.add(evrak.getHedefNo());
			type.add(Types.VARCHAR);
		}
		if (kosulStr.length() > 0) {
			kosulStr = " and " + kosulStr.substring(0, kosulStr.length() - 5);
			kosulStr += "  order by ev.id desc) where rownum<101";
		} else {
			kosulStr = " and giris_tarih > sysdate - 10  order by ev.id desc) where  rownum<101";
		}
		
		String sql = "	select * from (	select  ev.id,ev.giris_tarih,ev.evrak_sira_no, ev.evrak_no,ev.ACILMI,il.il_adi, ev.evrak_geldigi_kurum, mk.mahkeme_adi, mk.mahkeme_karar_no, "
				+ " mk.sorusturma_no,(select il_adi from iller i where i.il_kod = mk.mahkeme_ili) as mahkeme_ili,"
				+ "(Select mkt.KARAR_TIPI from mah_karar_tipleri mkt where  mkt.KARAR_KODU=mk.KARAR_TIP ) as KARAR_TIP, "
				+ "CASE "
				+ "WHEN (SELECT COUNT (*) FROM hedefler h1, mahkeme_karar mr  WHERE h1.mahkeme_karar_id = mr.ID  AND mr.ID = mk.ID  AND (h1.durumu IS NULL OR h1.durumu = 'TANIMLANAMADI')) >0  "
				+ "OR (SELECT COUNT (*)  FROM hedefler h1, mahkeme_karar mr  WHERE h1.mahkeme_karar_id = mr.ID  AND mr.ID = mk.ID  AND h1.durumu = 'TANIMLANDI') = 0  THEN 'İŞLEMDE' "
				+ "WHEN (SELECT COUNT (*)  FROM hedefler h1, mahkeme_karar mr  WHERE h1.mahkeme_karar_id = mr.ID  AND mr.ID = mk.ID  AND (h1.durumu IS NULL OR h1.durumu = 'TANIMLANAMADI')) =0  "
				+ "OR (SELECT COUNT (*)  FROM hedefler h1, mahkeme_karar mr  WHERE h1.mahkeme_karar_id = mr.ID  AND mr.ID = mk.ID  AND h1.durumu = 'TANIMLANDI')>0  then'TANIMLANDI'  "
				+ "END AS durum  "
				+ "FROM evrak_kayit ev, mahkeme_karar mk, iller il  "
				+ " WHERE ev.gel_il = il.il_kod   "
				+ "AND ev.ID = mk.evrak_id(+) and ev.durumu='ONAYLANDI' "
				+ "AND      ev.evrak_geldigi_kurum IN (SELECT ek.kurum_kod FROM evrak_gelen_kurumlar ek, kullanici_kurum kk WHERE ek.kurum_kod = kk.kurum_kod  "
				+ "AND kk.kullanici_id = ?) AND ev.KAY_KULLANICI IN (SELECT id  FROM kullanicilar WHERE TEMSIL_EDILEN_KURUM IS NOT NULL)"
				+ kosulStr;
		tjdbc.setStatement(sql);

		int types[] = new int[type.size()];
		for (int i = 0; i < type.size(); i++) {
			types[i] = (Integer) type.get(i);
		}

		tjdbc.setData(data.toArray());
		tjdbc.setRowMapper(new EvrakAramaRowMapper());

		// System.out.println(tjdbc.getStatement());
		return getDao().bulRowMapper(tjdbc);
	}

	public class EvrakDetayRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			IymEvrakAramaDetayPojo evr = new IymEvrakAramaDetayPojo();

			evr.setEvrakSiraNo(rs.getString("evrak_sira_no"));
			evr.setEvrakOnayTarih(rs.getString("evrak_onay_tarih"));
			evr.setMahkemeAd(rs.getString("mahkeme_adi"));
			evr.setMahkemeKararDurum(rs.getString("karar_durum"));
			evr.setMahkemeKararTip(rs.getString("karar_tip"));
			evr.setHedefDurum(rs.getString("hedef_durum"));
			evr.setHedefNo(rs.getString("hedef_no"));
			evr.setHedefTip(rs.getString("hedef_tipi"));
			evr.setAidiyatKod(rs.getString("aidiyat_kod"));
			evr.setKararBaslamaTarih(rs.getString("baslama_tarihi"));
			evr.setKararGun(rs.getString("suresi"));
			evr.setKararAy(rs.getString("sure_tipi"));
			evr.setUzatmaSayisi(rs.getString("uzatma"));

			evr.setMahkemeKararId(rs.getLong("mahkeme_karar_id"));
			evr.setKutu(rs.getInt("kutu"));
			evr.setEvrakGeldigiKurum(rs.getString("evrak_geldigi_kurum"));

			return evr;
		}
	}

	public List<IymEvrakPojo> islemDetayListGetir(String evrakId) {
		TemelJDBC tjdbc = new TemelJDBC();

		tjdbc.setStatement("SELECT ek.evrak_sira_no, evrak_geldigi_kurum,"
				+ "   TO_CHAR (ek.onay_tarihi, 'dd.mm.yyyy hh24:mi:ss') AS evrak_onay_tarih,"
				+ "   mk.mahkeme_adi, (SELECT karar_tipi"
				+ "                    FROM mah_karar_tipleri"
				+ "                   WHERE karar_kodu = mk.karar_tip) AS karar_tip,"
				+ " mk.durum AS karar_durum, h.hedef_no,"
				+ " (SELECT hedef_tipi"
				+ "    FROM hedef_tipleri"
				+ "   WHERE hedef_kodu = h.hedef_tipi) AS hedef_tipi,"
		//		+ "       (SELECT mha.aidiyat_kod"
				+ "       (SELECT  listagg(mha.AIDIYAT_KOD,' ; ') WITHIN GROUP (ORDER BY mha.AIDIYAT_KOD) "
				+ "    FROM mkno_hno_aidiyat_2 mha"
				+ "   WHERE mha.ID = h.ID AND ROWNUM <= 1) AS aidiyat_kod, "
				+ " TO_CHAR (h.baslama_tarihi, 'dd.mm.yyyy') AS baslama_tarihi, h.suresi,"
				+ " h.sure_tipi, h.uzatma_sayisi AS Uzatma,"
				+ " NVL (h.durumu, 'İŞLEMDE') AS hedef_durum,"
				+ " h.mahkeme_karar_id, "
				+ "    CASE"
				+ "          WHEN mk.karar_tip IN ('300', '400','410', '730','710','800', '900', '700')"
				+ "          THEN"
				+ "             '1'"
				+ "          WHEN mk.karar_tip IN ('100', '200', '600')"
				+ "          THEN"
				+ "             CASE (SELECT t.evrak_geldigi_kurum"
				+ "                    FROM evrak_kayit t"
				+ "                    WHERE t.ID = mk.evrak_id)"
				+ "               WHEN '01' THEN '3'"
				+ "               WHEN '03' THEN '4'"
				+ "               WHEN '02' THEN '2'"
				+ "            END"
				+ "      END"
				+ "         AS kutu"
				+ " FROM evrak_kayit ek LEFT JOIN mahkeme_karar mk ON ek.ID = mk.evrak_id"
				+ "  LEFT JOIN hedefler h ON mk.ID = h.mahkeme_karar_id WHERE ek.ID = ?");

		tjdbc.setData(new Object[] { Long.parseLong(evrakId) });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new EvrakDetayRowMapper());

		return getDao().bulRowMapper(tjdbc);
	}

	public class CanakEvrakDetayRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			IymEvrakAramaDetayPojo evr = new IymEvrakAramaDetayPojo();

			evr.setEvrakSiraNo(rs.getString("evrak_sira_no"));
			evr.setEvrakGeldigiKurum(rs.getString("evrak_geldigi_kurum"));
			// evr.setEvrakOnayTarih(rs.getString("evrak_onay_tarih"));
			evr.setMahkemeAd(rs.getString("mahkeme_adi"));
			evr.setMahkemeKararDurum(rs.getString("karar_durum"));
			evr.setMahkemeKararTip(rs.getString("karar_tip"));
			evr.setHedefDurum(rs.getString("hedef_durum"));
			evr.setHedefNo(rs.getString("hedef_no"));
			evr.setCanakNo(rs.getString("canak_no"));
			// evr.setCanakBaslangicTarihi(rs
			// .getTimestamp("canak_baslangic_tarihi"));
			// evr.setCanakBitisTarihi(rs.getTimestamp("canak_bitis_tarihi"));
			evr.setCanakTip(rs.getString("canak_aktarim_tip"));
			evr.setHedefTip(rs.getString("Hedef Tipi"));
			evr.setAidiyatKod(rs.getString("aidiyat_kod"));
			evr.setKararBaslamaTarih(rs.getString("baslama_tarihi"));
			evr.setKararGun(rs.getString("Süre Gün"));
			evr.setKararAy(rs.getString("Süre Ay"));
			evr.setUzatmaSayisi(rs.getString("uzatma"));

			evr.setMahkemeKararId(rs.getLong("mahkeme_karar_id"));
			evr.setKutu(rs.getInt("kutu"));

			return evr;
		}
	}

	public List<IymEvrakPojo> islemDetayListGetir(Long kullaniciId,
			String hedefNo, String canakNo) {
		TemelJDBC tjdbc = new TemelJDBC();

		String kosulStr = "";

		List<Object> data = new ArrayList();
		List<Integer> type = new ArrayList();

		data.add(kullaniciId);
		type.add(Types.BIGINT);

		if (canakNo != null && !canakNo.trim().equals("")) {
			data.add(canakNo);
			type.add(Types.VARCHAR);
			kosulStr += " AND c.canak_no = ? ";
		}

		if (hedefNo != null && !hedefNo.trim().equals("")) {
			data.add(hedefNo);
			type.add(Types.VARCHAR);
			kosulStr += " AND h.hedef_no = ?";
		}

		String kurum = kurumGetir(kullaniciId);
		// if (1 == 1) {
		// Tib karar girici ve B kurum sadece kendi girdigi evrakı gorebilir...
		// if (kurum == null || "B".equals(kurum) || kullaniciId.equals(2779L)){
		if (true) {
			kosulStr += " AND e.kay_kullanici= ? ";
			data.add(kullaniciId);
			type.add(Types.BIGINT);
		}

		if (kosulStr == "") {
			return null;
		}

		String sql = "SELECT h.evrak_sira_no, "
				+ "       h.evrak_geldigi_kurum, "
				+ "       h.mahkeme_adi, "
				+ "       (SELECT karar_tipi "
				+ "          FROM mah_karar_tipleri "
				+ "         WHERE karar_kodu = h.karar_tip) "
				+ "          AS karar_tip, "
				+ "       h.durum AS karar_durum, "
				+ "       h.hedef_no, "
				+ "		  h.\"Hedef Tipi\", "
				+ "		  (SELECT mha.aidiyat_kod "
				+ "			  FROM mkno_hno_aidiyat_2 mha "
				+ "			  WHERE mha.ID = h.ID AND ROWNUM <= 1) "
				+ "		  AS aidiyat_kod, "
				+ "       c.canak_no, "
				+ "       c.tip as canak_aktarim_tip, "
				+ "       TO_CHAR (h.baslama_tarihi, 'dd.mm.yyyy') AS baslama_tarihi, "
				+ "       h.\"Süre Gün\", "
				+ "       h.\"Süre Ay\", "
				+ "       h.uzatma_sayisi AS Uzatma, "
				+ "       NVL (h.durumu, 'İŞLEMDE') AS hedef_durum, "
				+ "       h.mahkeme_karar_id, "
				+ "       h.\"Kutu\" "
				+ "    FROM hedefler_view h, canak_atanmis_numaralar c, evrak_kayit e "
				+ "    WHERE  c.hedef_no(+) = h.hedef_no "
				+ "       AND h.evrak_id = e.id "
				+ "       AND c.mahkeme_karar_id(+) = h.mahkeme_karar_id "
				+ "       AND c.kurum_kod(+) = h.evrak_geldigi_kurum "
				+ "       AND c.kutu(+) = h.\"Kutu\" "
				+ "       AND h.karar_tip in (100,200,300,400,410,800)"
				+ "       AND h.evrak_geldigi_kurum IN "
				+ "              (SELECT e.kurum_kod "
				+ "                 FROM evrak_gelen_kurumlar e, kullanici_kurum kk "
				+ "                WHERE e.kurum_kod = kk.kurum_kod AND kk.kullanici_id = ?) ";

		sql = sql + kosulStr;

		// System.out.println("sql : " + sql);

		tjdbc.setStatement(sql);

		int types[] = new int[type.size()];
		for (int i = 0; i < type.size(); i++) {
			types[i] = (Integer) type.get(i);
		}

		tjdbc.setData(data.toArray());
		tjdbc.setRowMapper(new CanakEvrakDetayRowMapper());

		// System.out.println(tjdbc.getStatement());
		return getDao().bulRowMapper(tjdbc);

	}

	public String talepFormSayiAl(IymYaziPojo yazi) {

		boolean hata = false;
		CallableStatement cs = null;
		String procedure = "{call GET_FORM_SIRANO(?,?)}";
		String sonuc = null;

		try {
			cs = getDao().procedureCall(procedure);
			cs.setString(1, yazi.getEvrakTip());
			cs.registerOutParameter(2, Types.VARCHAR);
			cs.execute();
			sonuc = cs.getString(2);

		} catch (Exception e) {
			e.printStackTrace();
			hata = true;
		}

		finally {
			if (cs != null) {
				try {
					cs.close();
				} catch (Exception e) {
					// TODO: handle exception
				}
			}
		}

		sonuc = formKurumGetir(yazi.getPersonelId()) + sonuc.substring(10);

		return sonuc;
	}

	private String formKurumGetir(Long kullaniciId) {
		String kurum = "";

		kurum = (String) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						"select temsil_edilen_kurum||'.KT.' as kurum from kullanicilar where id=?",
						new Object[] { kullaniciId },
						new int[] { Types.BIGINT }, Object.class);

		if (kurum != null || kurum.equalsIgnoreCase(".KT."))
			return kurum;
		else
			return null;
	}

	public Long talepFormIdAl() {
		Long yaziId = null;

		yaziId = (Long) ((TemelDAO) getDao()).getJdbcTemplate().queryForObject(
				"Select  yazilar_seq.nextval from dual ", new Object[] {},
				new int[] {}, Long.class);

		return yaziId;

	}

	private void setStates(PreparedStatement st, int[] tipler, Object[] obj) {

		int index = 0;
		StringReader reader = null;
		for (int i = 0; i < tipler.length; i++) {
			try {
				index++;
				switch (tipler[i]) {
				case Types.VARCHAR:
					st.setString(index, (String) obj[i]);
					break;
				case Types.BIGINT:
					if (obj[i] == null) {
						st.setBigDecimal(index, null);
					} else {
						st.setLong(index, (Long) obj[i]);
					}
					break;
				case Types.INTEGER:
					st.setInt(index, (Integer) obj[i]);
					break;
				case Types.NCLOB:
					reader = new StringReader((String) obj[i]);
					st.setNClob(index, reader);
					reader.close();
					break;
				case Types.DATE:
					System.out.println("Date aktif degil..");
					break;
				default:
					break;
				}
			} catch (Exception e) {
				System.out.println("hata -- index:" + index + "  obj: "
						+ obj[i]);
			}

		}

	}

	public boolean talepFormKaydet(IymYaziPojo yazi) {
		boolean don = false;
		Connection con = null;
		PreparedStatement st = null;
		ResultSet rs = null;

		try {

			// con = (Connection) getDao().getJdbcTemplate().getDataSource()
			// .getConnection();

			con = TIBDB.getConnectionIYM2();

			if (con == null) {
				System.out.println("connection alınamadı...");
				return false;
			}

			String sql = "select yazilar_seq.nextval as sayi from dual";
			st = (PreparedStatement) con.prepareStatement(sql);
			rs = (ResultSet) st.executeQuery();
			while (rs.next()) {
				yazi.setId(rs.getLong("sayi"));
			}

			String sayi = talepFormSayiAl(yazi);
			if (yazi == null || sayi == null || sayi == null) {
				return false;
			}

			sql = "insert into iym.yazi_dagitim (id, yazi_id, kurum, il_ilce_kod, tur, silindi) values "
					+ "(yazi_dagitim_seq.nextval,?,?,?,?,0)";
			try {
				st = (PreparedStatement) con.prepareStatement(sql);
				st.setLong(1, yazi.getId());
				st.setString(2, yazi.getDagitim().replaceAll("\n", ""));
				st.setString(3, "0699");
				st.setString(4, "GEREGI");
				st.executeUpdate();
			} catch (SQLException e) {
				return false;
			}

			yazi.setSayi(sayi);
			yazi.setEvrakTip(sayi.substring(0, 4));

			// sql =
			// "insert into iym.yazilar (id,tarih,sayisi,konusu,baslik,ilgi, yazi_icerik,    ek_id,kurum_bilgileri,kayit_tarihi,kullanici_id,durum,ekler,dagitim,icerik_son,evrak_kurum,evrak_tipi, ust_evrak_sira_no,bilgi,gizlilik_derecesi,ivedilik_derecesi,evrak_il,aciklama,ust_evrak_id,yazi_turu,acilmi,paraf1,imza_id) values  "
			// +
			// "(?,to_date(?,'dd.mm.YYYYHH24MISS'), ?,?,?,?,    ?,     ?,?, sysdate, ?,?,?,?, ?,?,?, ?,?,?, ?,?,?,    ?, ?,     ?,   (Select  id  FROM kullanici_gorev2_view WHERE gorev_kodu=(select substr(max(gorev_kodu),1,2) from kullanici_gorev2_view where  gdurum='A' and durumu='A' ) and gdurum='A' and durumu='A'),"
			// +
			// "(Select gid  FROM kullanici_gorev2_view WHERE gorev_kodu=(select substr(max(gorev_kodu),1,2) from kullanici_gorev2_view where  gdurum='A' and durumu='A' ) and gdurum='A' and durumu='A'))";

			sql = "insert into iym.yazilar (id,tarih,sayisi,konusu,baslik,ilgi, yazi_icerik,    ek_id,kurum_bilgileri,kayit_tarihi,kullanici_id,durum,ekler,dagitim,icerik_son,evrak_kurum,evrak_tipi, ust_evrak_sira_no,bilgi,gizlilik_derecesi,ivedilik_derecesi,evrak_il,aciklama,ust_evrak_id,yazi_turu,acilmi,paraf1,imza_id) values  "
					+ "(?,to_date(?,'dd.mm.YYYYHH24MISS'), ?,?,?,?,    ?,     ?,?, sysdate, ?,?,?,?, ?,?,?, ?,?,?, ?,?,?,    ?, ?,     ?, ?,?  )";

			Object[] obj = { yazi.getId(),
					TemelIslemler.TurkTarih(yazi.getTarih()) + "000000",
					yazi.getSayi(), yazi.getKonu(), yazi.getBaslik(),
					yazi.getIlgi(), yazi.getIcerik(), yazi.getEkId(),
					yazi.getKurumBilgileri(), yazi.getPersonelId(),
					yazi.getDurum(), yazi.getEkler(), yazi.getDagitim(),
					yazi.getIcerikSon(), yazi.getEvrakKurum(),
					yazi.getEvrakTip(), yazi.getUstEvrakSiraNo(),
					yazi.getBilgi(), yazi.getGizlilikDerece(),
					yazi.getIvediDerece(), yazi.getEvrakIl(),
					yazi.getAciklama(), yazi.getUstEvrakId(),
					yazi.getYaziTur(), yazi.getAcil(), yazi.getParaf1(),
					yazi.getImzaId() };
			int[] tipler = { Types.BIGINT, Types.VARCHAR, Types.VARCHAR,
					Types.VARCHAR, Types.VARCHAR, Types.VARCHAR, Types.NCLOB,
					Types.BIGINT, Types.VARCHAR, Types.BIGINT, Types.VARCHAR,
					Types.VARCHAR, Types.VARCHAR, Types.VARCHAR, Types.VARCHAR,
					Types.VARCHAR, Types.VARCHAR, Types.VARCHAR, Types.VARCHAR,
					Types.VARCHAR, Types.VARCHAR, Types.VARCHAR, Types.VARCHAR,
					Types.VARCHAR, Types.VARCHAR, Types.BIGINT, Types.INTEGER };

			st = (PreparedStatement) con.prepareStatement(sql);
			setStates(st, tipler, obj);
			st.executeUpdate();

			sql = "update yazilar y set y.icerik = ? where y.id = ?";
			st = con.prepareStatement(sql);

			StringReader sreader = new StringReader(yazi.getIcerik());
			int fileLength = yazi.getIcerik().length();
			st.setCharacterStream(1, sreader, fileLength);
			sreader.close();

			st.setLong(2, yazi.getId());
			st.executeUpdate();

			con.commit();
			don = true;
		} catch (Exception e) {
			try {
				con.rollback();
			} catch (Exception e1) {
				e1.printStackTrace();
			}
			e.printStackTrace();
		} finally {
			try {
				if (rs != null) {
					rs.close();
				}
				if (st != null) {
					st.close();
				}
				if (con != null) {
					con.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}

		return don;

	}

	public class YaziDetayRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			// ResultSetMetaData rsm=rs.getMetaData();
			// int colnumber=rsm.getColumnCount();
			//
			// for (int i = 0; i < colnumber; i++) {
			// System.out.println("colum adı: "+rsm.getColumnLabel(i));
			// }

			IymYaziPojo yazi = new IymYaziPojo();

			yazi.setId(rs.getLong("ID"));
			yazi.setTarih(rs.getDate("TARIH"));
			yazi.setSayi(rs.getString("SAYISI"));
			yazi.setKonu(rs.getString("KONUSU"));
			yazi.setBaslik(rs.getString("BASLIK"));
			yazi.setIlgi(rs.getString("ILGI"));

			String icerik = rs.getString("YAZI_ICERIK");
			if (icerik == null || icerik.trim().length() < 2) {
				icerik = rs.getString("ICERIK");
			}

			yazi.setIcerik(icerik);

			yazi.setDagitimId(rs.getLong("DAGITIM_ID"));
			yazi.setEkId(rs.getLong("EK_ID"));
			yazi.setKayitTarihi(rs.getTimestamp("KAYIT_TARIHI"));
			yazi.setPersonelId(rs.getLong("KULLANICI_ID"));
			yazi.setDurum(rs.getString("DURUM"));
			yazi.setKurumBilgileri(rs.getString("KURUM_BILGILERI"));
			yazi.setEkler(rs.getString("EKLER"));
			yazi.setDagitim(rs.getString("DAGITIM"));
			yazi.setIcerikSon(rs.getString("ICERIK_SON"));
			yazi.setEvrakKurum(rs.getString("EVRAK_KURUM"));
			yazi.setEvrakTip(rs.getString("EVRAK_TIPI"));
			yazi.setUstEvrakSiraNo(rs.getString("UST_EVRAK_SIRA_NO"));
			yazi.setBilgi(rs.getString("BILGI"));
			yazi.setGizlilikDerece(rs.getString("GIZLILIK_DERECESI"));
			yazi.setIvediDerece(rs.getString("IVEDILIK_DERECESI"));
			yazi.setEvrakIl(rs.getString("EVRAK_IL"));
			yazi.setAciklama(rs.getString("ACIKLAMA"));
			yazi.setUstEvrakId(rs.getLong("UST_EVRAK_ID"));
			yazi.setYaziTur(rs.getString("YAZI_TURU"));
			yazi.setAcil(rs.getString("ACILMI"));

			return yazi;
		}
	}

	public List<IymYaziPojo> talepFormTaslakListGetir(Long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("	SELECT ID,TARIH,SAYISI,KONUSU,BASLIK,ILGI,ICERIK, YAZI_ICERIK, DAGITIM_ID,EK_ID,KAYIT_TARIHI,KULLANICI_ID,"
				+ "DURUM,KURUM_BILGILERI,EKLER,DAGITIM,ICERIK_SON,EVRAK_KURUM,EVRAK_TIPI,UST_EVRAK_SIRA_NO,BILGI,GIZLILIK_DERECESI,"
				+ "IVEDILIK_DERECESI,EVRAK_IL,ACIKLAMA,UST_EVRAK_ID,YAZI_TURU,ACILMI FROM yazilar t WHERE durum IS NULL AND "
				+ "t.kullanici_id = ? ORDER BY ID DESC");
		tjdbc.setData(new Object[] { kullaniciId });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new YaziDetayRowMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	public class TalepFormEkDetayMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			IymYaziEkDosyaPojo ek = new IymYaziEkDosyaPojo();
			ek.setEkId(rs.getLong("id"));
			ek.setYaziId(rs.getLong("yazi_Id"));
			ek.setDosyaAdi(rs.getString("dosya_Adi"));
			ek.setDizinAdi(rs.getString("dizin_Adi"));
			ek.setEklemeTarih(rs.getTimestamp("tarih"));
			ek.setSiraNo(rs.getLong("sira_No"));
			ek.setKullnaniciId(rs.getLong("kullanici_id"));

			return ek;
		}
	}

	public List talepFormEkListesiGetir(Long id) {

		TemelJDBC tjdbc = new TemelJDBC();

		tjdbc.setStatement("select id,dosya_adi,Sira_no,yazi_id,dizin_adi,tarih,kullanici_id  from yazi_ekler t where yazi_id=? order by sira_no desc ");
		tjdbc.setData(new Object[] { id });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new TalepFormEkDetayMapper());

		List<IymYaziEkDosyaPojo> list = null;
		try {
			list = getDao().bulRowMapper(tjdbc);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;

	}

	public boolean talepFormSil(Long id) {

		TemelJDBC tjdbc = new TemelJDBC();
		String statement = " DELETE FROM iym.yazilar WHERE id=?";
		Object[] obj = { id };
		int[] i = { Types.BIGINT };
		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			if (talepFormIlgileriniSil(id)) {
				getDao().sil(tjdbc);
			}
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	private boolean talepFormIlgileriniSil(Long id) {
		TemelJDBC tjdbc = new TemelJDBC();
		String statement = " delete from evrak_yazi_iliski where id_1=?";
		Object[] obj = { id };
		int[] i = { Types.BIGINT };
		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().sil(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	public boolean talepFormDegistir(IymYaziPojo yazi) {

		boolean don = false;
		Connection con = null;
		PreparedStatement st = null;

		try {

			con = (Connection) getDao().getJdbcTemplate().getDataSource()
					.getConnection();

			con = TIBDB.getConnectionIYM2();

			if (con == null) {
				System.out.println("connection alınamadı...");
				return false;
			}

			String sql = "update iym.yazilar set "
					+ "sayisi=?,konusu=?,baslik=?,ilgi=?,yazi_icerik=?,"
					+ "ek_id=?,kurum_bilgileri=?,kayit_tarihi= SYSDATE, kullanici_id=?,"
					+ "durum=?,ekler=?,dagitim=?,icerik_son=?,evrak_kurum=?,evrak_tipi=?,"
					+ "ust_evrak_sira_no=?,bilgi=?,gizlilik_derecesi=?,ivedilik_derecesi=?,evrak_il=?,"
					+ "aciklama=?,ust_evrak_id=?,yazi_turu=?,acilmi=? where id=?";

			Object[] obj = { yazi.getSayi(), yazi.getKonu(), yazi.getBaslik(),
					yazi.getIlgi(), yazi.getIcerik(), yazi.getEkId(),
					yazi.getKurumBilgileri(), yazi.getPersonelId(),
					yazi.getDurum(), yazi.getEkler(), yazi.getDagitim(),
					yazi.getIcerikSon(), yazi.getEvrakKurum(),
					yazi.getEvrakTip(), yazi.getUstEvrakSiraNo(),
					yazi.getBilgi(), yazi.getGizlilikDerece(),
					yazi.getIvediDerece(), yazi.getEvrakIl(),
					yazi.getAciklama(), yazi.getUstEvrakId(),
					yazi.getYaziTur(), yazi.getAcil(), yazi.getId() };
			int[] tipler = { Types.VARCHAR, Types.VARCHAR, Types.VARCHAR,
					Types.VARCHAR, Types.NCLOB, Types.BIGINT, Types.VARCHAR,
					Types.BIGINT, Types.VARCHAR, Types.VARCHAR, Types.VARCHAR,
					Types.VARCHAR, Types.VARCHAR, Types.VARCHAR, Types.VARCHAR,
					Types.VARCHAR, Types.VARCHAR, Types.VARCHAR, Types.VARCHAR,
					Types.VARCHAR, Types.BIGINT, Types.VARCHAR, Types.VARCHAR,
					Types.BIGINT };

			st = con.prepareStatement(sql);
			setStates(st, tipler, obj);
			st.executeUpdate();

			sql = "update yazilar y set y.icerik = ? where y.id = ?";
			st = con.prepareStatement(sql);

			StringReader sreader = new StringReader(yazi.getIcerik());
			int fileLength = yazi.getIcerik().length();
			st.setCharacterStream(1, sreader, fileLength);
			sreader.close();

			st.setLong(2, yazi.getId());
			st.executeUpdate();

			con.commit();
			don = true;
		} catch (Exception e) {
			try {
				con.rollback();
			} catch (Exception e1) {
				e1.printStackTrace();
			}
			e.printStackTrace();
		} finally {
			try {
				if (st != null) {
					st.close();
				}
				if (con != null) {
					con.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}

		return don;

	}

	public class FormIlgiDetayRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {

			IymYaziPojo yazi = new IymYaziPojo();

			yazi.setId(rs.getLong("ID"));
			yazi.setTarih(rs.getDate("TARIH"));
			yazi.setSayi(rs.getString("SAYISI"));
			yazi.setKonu(rs.getString("KONUSU"));
			yazi.setIlgi(rs.getString("ILGI"));

			return yazi;
		}
	}

	public List yaziIlgiListGetir(IymYaziPojo yazi, Date sorguBaslama,
			Date sorguBitis) {

		TemelJDBC tjdbc = new TemelJDBC();

		String kosulStr = "";

		List<Object> data = new ArrayList();
		List<Integer> type = new ArrayList();
		// koşuldaki 2 TANE kullanici id alanı için eklendi
		data.add(yazi.getPersonelId());
		type.add(Types.BIGINT);
		data.add(yazi.getPersonelId());
		type.add(Types.BIGINT);

		if (!TemelIslemler.bosMu(yazi.getSayi())) {
			kosulStr += " y.Sayisi like ? and ";
			data.add("%" + yazi.getSayi());
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(yazi.getKonu())) {
			kosulStr += " upper(y.konusu) like ? and ";
			data.add("%" + yazi.getKonu().toUpperCase(new Locale("tr", "TR"))
					+ "%");
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(sorguBaslama)) {
			kosulStr += " trunc(y.kayit_tarihi)>= ? and ";
			data.add(sorguBaslama);
			type.add(Types.DATE);
		}
		if (!TemelIslemler.bosMu(sorguBitis)) {
			kosulStr += " trunc(y.kayit_tarihi)<= ? and ";
			data.add(sorguBitis);
			type.add(Types.DATE);
		}

		if (kosulStr.length() > 0) {
			kosulStr = " and " + kosulStr.substring(0, kosulStr.length() - 5);
			kosulStr += " order by y.kayit_tarihi desc";
		} else {
			kosulStr = "   order by y.kayit_tarihi desc";
		}

		tjdbc.setStatement("SELECT DISTINCT (eh.evrak_id) as id , y.sayisi, y.konusu, y.ilgi, y.kayit_tarihi as tarih FROM yazilar_view y, evrak_havale2 eh "
				+ "where y.ID = eh.evrak_id and (eh.havale_eden_id = ? OR eh.havale_edilen_id = ?) and "
				+ "  y.durum in ('ONAYLANDI','ARSIV') " + kosulStr);

		int types[] = new int[type.size()];
		for (int i = 0; i < type.size(); i++) {
			types[i] = (Integer) type.get(i);
		}

		tjdbc.setData(data.toArray());
		tjdbc.setRowMapper(new FormIlgiDetayRowMapper());

		return getDao().bulRowMapper(tjdbc);

	}

	public class IlgiRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {

			IymYaziEvrakIliskiPojo yazi = new IymYaziEvrakIliskiPojo();

			yazi.setId(rs.getLong("ID"));
			yazi.setYaziId(rs.getLong("YAZIID"));
			yazi.setYaziSiraNo(rs.getString("YAZISIRANO"));
			yazi.setIlgiliYaziId(rs.getLong("ILGILIYAZIID"));
			yazi.setIlgiliYaziSiraNo(rs.getString("ILGILIYAZISIRANO"));
			yazi.setTarih(rs.getDate("TARIH"));
			yazi.setIlgiTarih(rs.getString("ILGI_TARIH"));
			return yazi;
		}
	}

	public List<String> ilgiListGetir(Long formId) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("select eyi.id,eyi.iliski, eyi.id_1 AS yaziid, eyi.id_2 AS ilgiliyaziid,eyi.id_1_sno AS yazisirano, eyi.id_2_sno AS ilgiliyazisirano, eyi.tarih,"
				+ "  case when eyi.iliski='YAZI-YAZI' Then to_char(y1.kayit_tarihi,'dd.mm.yyyy') else to_char(y2.giris_tarih,'dd.mm.yyyy') end ILGI_TARIH"
				+ " FROM evrak_yazi_iliski eyi,yazilar y1,evrak_kayit y2   WHERE 1=1    and eyi.id_2 = y1.id(+)    and eyi.id_2=y2.id(+)    and id_1 = ? ORDER BY id_2 ASC");
		tjdbc.setData(new Object[] { formId });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new IlgiRowMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	public boolean talepFormIlgiEkle(String iliski, Long yaziID,
			String yaziSiraNo, Long yaziIlgiId, String yaziIlgiSiraNo,
			Long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("insert into evrak_yazi_iliski (id,ILISKI,Id_1,Id_1_sno,ID_2,ID_2_SNO,TARIH,KULLANICI_ID)"
				+ " values (evrak_yazi_iliski_seq.nextval,?,?,?,?,?,sysdate,?)");
		tjdbc.setData(new Object[] { iliski, yaziID, yaziSiraNo, yaziIlgiId,
				yaziIlgiSiraNo, kullaniciId });
		tjdbc.setType(new int[] { Types.VARCHAR, Types.BIGINT, Types.VARCHAR,
				Types.BIGINT, Types.VARCHAR, Types.BIGINT });

		try {
			getDao().ekle(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}

	}

	public boolean talepFormIlgiCikar(Long id) {

		TemelJDBC tjdbc = new TemelJDBC();
		String statement = " DELETE FROM iym.evrak_yazi_iliski WHERE id=?";
		Object[] obj = { id };
		int[] i = { Types.BIGINT };
		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().sil(tjdbc);
			return true;
		} catch (Exception e) {
			return false;
		}

	}

	public boolean talepFormEkDosyaEkle(Long yaziID, String dosyaAdi,
			String path, Long siraNo, Long kullaniciId, String sha1) {
		TemelJDBC tjdbc = new TemelJDBC();
		String statement = " insert into iym.yazi_ekler (id,yazi_id, dosya_adi, dizin_adi, sira_no, tarih, kullanici_id, sha) values(yazi_ekler_seq.nextval,?,?,?,?,sysdate,?,?)";
		Object[] obj = { yaziID, dosyaAdi, path, siraNo, kullaniciId, sha1 };
		int[] i = { Types.BIGINT, Types.VARCHAR, Types.VARCHAR, Types.BIGINT,
				Types.BIGINT, Types.VARCHAR };
		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().ekle(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}

	}

	public Long talepFormEkSiraNoGetir(Long yaziId) {
		Long siraNo = null;

		siraNo = (Long) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						"Select to_number(nvl(Max(sira_no),0)+1) as sirano from yazi_ekler where yazi_id=?",
						new Object[] { yaziId }, new int[] { Types.BIGINT },
						Long.class);

		return siraNo;

	}

	public boolean talepFormEkDosyaSil(IymYaziEkDosyaPojo ekDosya) {
		// Delete yazi_ekler where id=
		TemelJDBC tjdbc = new TemelJDBC();
		String statement = " Delete yazi_ekler where id=? and yazi_id=?";
		Object[] obj = { ekDosya.getEkId(), ekDosya.getYaziId() };
		int[] i = { Types.BIGINT, Types.BIGINT };
		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().sil(tjdbc);
			return true;
		} catch (Exception e) {
			return false;
		}
	}

	public String gorevKoduGetir(Long iymId) {
		String sql = "select gorev_kodu from (select * from iym.kullanici_gorev2_view where kullanici_id=? and (durumu='A' or durumu='IZ') and gdurum='A' order by gid) where rownum <2";
		Long gorevKodu = null;
		try {
			gorevKodu = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
					.queryForLong(sql, new Object[] { iymId },
							new int[] { Types.BIGINT });

		} catch (Exception e) {
			return null;
		}
		return gorevKodu + "";
	}

	public Integer gorevIdGetir(Long iymId) {
		String sql = "select gid from (select * from iym.kullanici_gorev2_view where kullanici_id=? and (durumu='A' or durumu='IZ') and gdurum='A' order by gid) where rownum <2";
		Integer gorevId = null;
		try {
			gorevId = ((TemelDAO) getDao()).getJdbcTemplate().queryForInt(sql,
					new Object[] { iymId }, new int[] { Types.BIGINT });

		} catch (Exception e) {
			return null;
		}
		return gorevId;
	}

	public boolean talepFormOnayla(IymYaziPojo yazi, Integer havaleSebebi) {

		boolean don = false;
		Connection con = null;
		PreparedStatement st = null;
		ResultSet rs = null;

		try {

			con = TIBDB.getConnectionIYM2();

			if (con == null) {
				System.out.println("connection alınamadı...");
				return false;
			}

			Integer gorevId = gorevIdGetir(yazi.getPersonelId());
			TemelJDBC tjdbc = new TemelJDBC();
			String sql = "update iym.YAZILAR  set durum='ONAYLANDI', IMZA_ONAY_ID=? where id=?";
			st = con.prepareStatement(sql);
			st.setInt(1, gorevId);
			st.setLong(2, yazi.getId());
			st.executeUpdate();

			sql = "select evrak_havale2_seq.nextval as sayi from dual";
			st = (PreparedStatement) con.prepareStatement(sql);
			rs = (ResultSet) st.executeQuery();
			Long havaleId = null;
			while (rs.next()) {
				havaleId = rs.getLong("sayi");
			}

			sql = "INSERT INTO EVRAK_HAVALE2 (id,evrak_id,evrak_sira_no,havale_eden_id,havale_edilen_id,sebebi,kullanici_id,aciklama,grubami) "
					+ "VALUES(?,?,?,?,(Select  id  FROM kullanici_gorev2_view WHERE gorev_kodu=(select substr(max(gorev_kodu),1,2) from kullanici_gorev2_view where  gdurum='A' and durumu='A' ) and gdurum='A' and durumu='A'),"
					+ "?,?,?,?)";
			Object[] obj = {havaleId, yazi.getId(), yazi.getSayi(), yazi.getPersonelId(), havaleSebebi, yazi.getPersonelId(),
					yazi.getAciklama(), "H" };
			int[] tipler = {Types.BIGINT, Types.BIGINT, Types.VARCHAR, Types.BIGINT,
					Types.INTEGER, Types.BIGINT, Types.VARCHAR, Types.VARCHAR };
			
			st = con.prepareStatement(sql);
			setStates(st, tipler, obj);
			st.executeUpdate();
			
			sql = "INSERT INTO EVRAK_HAVALE2_DETAY (id, evrak_havale_id, evrak_id, havale_eden_id, havale_eden_gorev, havale_edilen_id, havale_edilen_gorev) "
					+ "VALUES(evrak_havale2_detay_seq.nextval,?,?,?,?,(Select  id  FROM kullanici_gorev2_view WHERE gorev_kodu=(select substr(max(gorev_kodu),1,2) from kullanici_gorev2_view where  gdurum='A' and durumu='A' ) and gdurum='A' and durumu='A'), '11' )";
			st = con.prepareStatement(sql);
			st.setLong(1, havaleId);
			st.setLong(2, yazi.getId());
			st.setLong(3, yazi.getPersonelId());
			st.setString(4, gorevKoduGetir(yazi.getPersonelId()));
			st.executeUpdate();
			
			don = true;

		} catch (Exception e) {
			try {
				con.rollback();
			} catch (Exception e1) {
				e1.printStackTrace();
			}
			e.printStackTrace();
			try {
				Messagebox.show("IYM Yazı Onaylama işleminde Hata");
				don = false;
			} catch (Exception e1) {
				e1.printStackTrace();
			}
		} finally {
			try {
				if (rs != null) {
					rs.close();
				}
				if (st != null) {
					st.close();
				}
				if (con != null) {
					con.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
	
		return don;

	}

	// public boolean talepFormOnayla(IymYaziPojo yazi, Integer havaleSebebi) {
	//
	// // select kullanici_id from kullanici_gorev2 where gorev_id=11 and
	// // durum='A' and rownum<2 order by id asc
	//
	// boolean don = false;
	//
	// // String gorevKodu = gorevKoduGetir(yazi.getPersonelId());
	// Integer gorevId = gorevIdGetir(yazi.getPersonelId());
	// TemelJDBC tjdbc = new TemelJDBC();
	// String statement =
	// "update iym.YAZILAR  set durum='ONAYLANDI', IMZA_ONAY_ID=? where id=?";
	//
	// Object[] obj = { gorevId, yazi.getId() };
	// int[] i = { Types.BIGINT, Types.BIGINT };
	//
	// tjdbc.setStatement(statement);
	// tjdbc.setData(obj);
	// tjdbc.setType(i);
	// try {
	// getDao().degistir(tjdbc);
	//
	// if (evrakHavaleYap(yazi, havaleSebebi)) {
	// don = true;
	// } else {
	// don = false;
	// }
	//
	// } catch (Exception e) {
	//
	// e.printStackTrace();
	//
	// try {
	// Messagebox.show("IYM Yazı Onaylama işleminde Hata");
	// don = false;
	// } catch (InterruptedException e1) {
	//
	// e1.printStackTrace();
	// }
	// }
	// return don;
	//
	// }

	public boolean evrakHavaleYap(IymYaziPojo yazi, Integer havaleSebebi) {
		TemelJDBC tjdbc = new TemelJDBC();
		String statement = "INSERT INTO EVRAK_HAVALE2 (id,evrak_id,evrak_sira_no,"
				+ "havale_eden_id,havale_edilen_id,"
				+ "sebebi,kullanici_id,"
				+ "aciklama,grubami) "
				+ "VALUES(evrak_havale2_seq.nextval,?,?,"
				+ "?,(Select  id  FROM kullanici_gorev2_view WHERE gorev_kodu=(select substr(max(gorev_kodu),1,2) from kullanici_gorev2_view where  gdurum='A' and durumu='A' ) and gdurum='A' and durumu='A'),"
				+ "?,?," + "?,?)";
		Object[] obj = { yazi.getId(), yazi.getSayi(), yazi.getPersonelId(),
				havaleSebebi, yazi.getPersonelId(), yazi.getAciklama(), "H" };
		int[] i = { Types.BIGINT, Types.VARCHAR, Types.BIGINT, Types.VARCHAR,
				Types.BIGINT, Types.VARCHAR, Types.VARCHAR };

		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().ekle(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	public class TalepFormAramaRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {

			IymYaziPojo yazi = new IymYaziPojo();

			yazi.setId(rs.getLong(1));
			yazi.setDurum(rs.getString(2));
			yazi.setTarih(rs.getDate(3));
			yazi.setSayi(rs.getString(4));
			yazi.setKonu(rs.getString(5));

			yazi.setIlgi(rs.getString(6));
			yazi.setEkler(rs.getString(7));
			yazi.setDagitim(rs.getString(8));
			yazi.setKayitTarihi(rs.getTimestamp(9));
			yazi.setAcil(rs.getString(10));
			yazi.setAciklama(rs.getString(11));
			yazi.setCevapEvrakSiraNo(rs.getString("cevapsirano"));
			return yazi;
		}
	}

	public List<IymEvrakAramaPojo> talepFormAra(IymYaziPojo evrak,
			Date sorguBaslama, Date sorguBitis, Long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();

		String kosulStr = "";

		List<Object> data = new ArrayList();
		List<Integer> type = new ArrayList();
		// koşuldaki kullanici id alanı için eklendi
		data.add(kullaniciId);
		type.add(Types.BIGINT);

		if (!TemelIslemler.bosMu(evrak.getSayi())) {
			kosulStr += " v.sayisi like ? and ";
			data.add("%" + evrak.getSayi());
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getKonu())) {
			kosulStr += " v.konusu like ? and ";
			data.add("%" + evrak.getKonu() + "%");
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(sorguBaslama)) {
			kosulStr += " trunc(v.\"Kayıt Tarihi\")>= ? and ";
			data.add(sorguBaslama);
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(sorguBitis)) {
			kosulStr += " trunc(v.\"Kayıt Tarihi\")<= ? and ";
			data.add(sorguBitis);
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getAciklama())) {
			kosulStr += " v.aciklama like ? and ";
			data.add("%" + evrak.getAciklama() + "%");
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getDurum())) {
			kosulStr += " v.durum = ? and ";
			data.add(evrak.getDurum());
			type.add(Types.VARCHAR);
		} else {
			kosulStr += " v.durum = ? and ";
			data.add("ONAYLANDI");
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getYaziTur())) {
			kosulStr += " v.\"İmza Durum\"= ? and ";
			data.add(evrak.getYaziTur());
			type.add(Types.VARCHAR);
		}

		if (kosulStr.length() > 0) {
			kosulStr = " and " + kosulStr.substring(0, kosulStr.length() - 5);
			kosulStr += " ORDER BY (3) DESC";
		} else {
			kosulStr = " and ROWNUM < 200 ORDER BY  (3) DESC";
		}

		tjdbc.setStatement("	SELECT   v.ID, v.\"İmza Durum\", v.tarih, v.sayisi, v.konusu, v.ilgi, v.ekler,"
				+ "  v.dagitim, v.\"Kayıt Tarihi\",nvl(v.ACILMI,'H') as acilmi,"
				+ "  v.aciklama , v.\"K.Adi Soyadi\" ,(select id_1_sno from evrak_yazi_iliski WHERE iliski='YAZI-YAZI' and id_2= v.id and rownum<2) AS cevapSiraNo "
				+ " FROM yazilar_view v "
				+ " WHERE "
				+ " v.kullanici_id = ?"
				+ kosulStr);

		// "	SELECT   v.ID, v.\"İmza Durum\", v.tarih, v.sayisi, v.konusu, v.ilgi, v.ekler,"
		// + "  v.dagitim, v.\"Kayıt Tarihi\",nvl(ev.ACILMI,'H') as acilmi,"
		// + "  v.aciklama , v.\"K.Adi Soyadi\""
		// + " FROM yazilar_view v, evrak_kayit_view ev, yazi_aps ya"
		// + " WHERE v.ust_evrak_sira_no = ev.\"Evrak Sira No\"(+)"
		// + " AND v.ID = ya.yazi_id(+)"
		// + " AND v.kullanici_id = ?" + kosulStr);

		int types[] = new int[type.size()];
		for (int i = 0; i < type.size(); i++) {
			types[i] = (Integer) type.get(i);
		}

		tjdbc.setData(data.toArray());
		tjdbc.setRowMapper(new TalepFormAramaRowMapper());
		// System.out.println(tjdbc.getStatement());
		// System.out.println(data.toArray());
		// return null;
		return getDao().bulRowMapper(tjdbc);
	}

	public List<IymEvrakAramaPojo> talepFormAra2(IymYaziPojo evrak,
			Date sorguBaslama, Date sorguBitis, Long kullaniciId,
			boolean cevapEvrakindeAransinmi) {
		TemelJDBC tjdbc = new TemelJDBC();

		String kosulStr = "";

		List<Object> data = new ArrayList();
		List<Integer> type = new ArrayList();
		// koşuldaki kullanici id alanı için eklendi
		data.add(kullaniciId);
		type.add(Types.BIGINT);

		data.add(kullaniciId);
		type.add(Types.BIGINT);

		if (!TemelIslemler.bosMu(evrak.getSayi())) {
			kosulStr += " v.sayisi like ? and ";
			data.add("%" + evrak.getSayi());
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getKonu())) {
			kosulStr += " v.konusu like ? and ";
			data.add("%" + evrak.getKonu() + "%");
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(sorguBaslama)) {
			kosulStr += " trunc(v.\"Kayıt Tarihi\")>= ? and ";
			data.add(sorguBaslama);
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(sorguBitis)) {
			kosulStr += " trunc(v.\"Kayıt Tarihi\")<= ? and ";
			data.add(sorguBitis);
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getAciklama())) {
			kosulStr += " v.aciklama like ? and ";
			data.add("%" + evrak.getAciklama() + "%");
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getDurum())) {
			kosulStr += " v.durum = ? and ";
			data.add(evrak.getDurum());
			type.add(Types.VARCHAR);
		} else {
			kosulStr += "( v.durum = ? or ";
			data.add("ONAYLANDI");
			type.add(Types.VARCHAR);

			kosulStr += " v.durum = ? ) and ";
			data.add("ARSIV");
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getYaziTur())) {
			kosulStr += " v.\"İmza Durum\"= ? and ";
			data.add(evrak.getYaziTur());
			type.add(Types.VARCHAR);
		}

		if (kosulStr.length() > 0) {
			kosulStr = " and " + kosulStr.substring(0, kosulStr.length() - 5);
			kosulStr += " ORDER BY (3) DESC";
		} else {
			kosulStr = " and ROWNUM < 200 ORDER BY  (3) DESC";
		}

		String sql = "";
		if (cevapEvrakindeAransinmi == true)
			sql = "	SELECT   v.ID, v.\"İmza Durum\", v.tarih, v.sayisi, v.konusu, v.ilgi, v.ekler,"
					+ "  v.dagitim, v.\"Kayıt Tarihi\",nvl(v.ACILMI,'H') as acilmi,"
					+ "  v.aciklama , v.\"K.Adi Soyadi\" ,(select id_1_sno from evrak_yazi_iliski WHERE iliski='YAZI-YAZI' and id_2= v.id and kullanici_id != v.kullanici_id and rownum<2) AS cevapSiraNo "
					+ " FROM yazilar_view v,evrak_havale2 eh "
					+ " WHERE "
					+ "   ((havale_eden_id=?) or (havale_edilen_id=?)) and"
					+ "   v.id=eh.evrak_id ";
		else
			sql = "	SELECT   v.ID, v.\"İmza Durum\", v.tarih, v.sayisi, v.konusu, v.ilgi, v.ekler,"
					+ "  v.dagitim, v.\"Kayıt Tarihi\",nvl(v.ACILMI,'H') as acilmi,"
					+ "  v.aciklama , v.\"K.Adi Soyadi\" ,(select id_1_sno from evrak_yazi_iliski WHERE iliski='YAZI-YAZI' and id_2= v.id and kullanici_id != v.kullanici_id and rownum<2) AS cevapSiraNo "
					+ " FROM yazilar_view v "
					+ " WHERE "
					+ " ((v.kullanici_id = ?) or (v.kullanici_id = ?)) ";

		sql += kosulStr;
		// System.out.println("sql  : " + sql);
		// System.out.println("data : " + data.toString());
		tjdbc.setStatement(sql);

		int types[] = new int[type.size()];
		for (int i = 0; i < type.size(); i++) {
			types[i] = (Integer) type.get(i);
		}

		tjdbc.setData(data.toArray());
		tjdbc.setRowMapper(new TalepFormAramaRowMapper());
		
		return getDao().bulRowMapper(tjdbc);
	}

	public boolean talepFormArsivle(Long yaziId, String aciklama) {
		TemelJDBC tjdbc = new TemelJDBC();
		String statement = "update iym.YAZILAR  set durum='ARSIV',aciklama =?  where id=?";
		Object[] obj = { aciklama, yaziId };
		int[] i = { Types.VARCHAR, Types.BIGINT };
		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);

		try {
			getDao().degistir(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}

	}

	public boolean gidenEvrakArsivle(Long yaziId, Long kullaniciId,
			String aciklama) {
		TemelJDBC tjdbc = new TemelJDBC();
		String statement = "update iym.evrak_HAvale2  set durum='ARSIV' ,aciklama=? where evrak_id=? and havale_edilen_id=? ";
		Object[] obj = { aciklama, yaziId, kullaniciId };
		int[] i = { Types.VARCHAR, Types.BIGINT, Types.BIGINT };
		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().degistir(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}

	}

	public boolean gelenEvrakArsivle(Long evrakId, Long kullaniciId,
			String aciklama) {
		TemelJDBC tjdbc = new TemelJDBC();
		String statement = "update iym.evrak_HAvale  set durum='ARSIV', aciklama=? where evrak_id=? and havale_edilen_id=? ";
		Object[] obj = { aciklama, evrakId, kullaniciId };
		int[] i = { Types.VARCHAR, Types.BIGINT, Types.BIGINT };
		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().degistir(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}

	}

	public void talepFormAciklamaEkle(String aciklama, Long yaziId) {
		TemelJDBC tjdbc = new TemelJDBC();
		String statement = "update iym.YAZILAR  set ACIKLAMA=? where id=?";
		Object[] obj = { aciklama, yaziId };
		int[] i = { Types.VARCHAR, Types.BIGINT };
		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);

		getDao().degistir(tjdbc);

	}

	public class TalepFormIslemDetayRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {

			IymYaziIslemDetayPojo yazi = new IymYaziIslemDetayPojo();

			yazi.setYaziId(rs.getLong("evrak_id"));
			yazi.setTarih(rs.getString("kayit_tarihi"));
			yazi.setHavaleEden(rs.getString("havale_eden"));
			yazi.setHavaleEdilen(rs.getString("havale_edilen"));

			yazi.setHavaleNeden(rs.getString("neden"));
			yazi.setDurum(rs.getString("durum"));

			return yazi;
		}
	}

	public List talepFormIslemDetayGetir(Long formId) {

		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("SELECT v.evrak_id, v.ID, DECODE (v.havale_eden_id,34, 'B Kurum Temsilcisi',75, 'A Kurum Temsilcisi',177, 'C kurum Temsilcisi',"
				+ " (SELECT gorev FROM kullanici_gorev2_view WHERE ID = v.havale_eden_id"
				+ "  AND durumu = 'A'  AND gdurum = 'A'  AND ROWNUM < 2)) AS havale_eden,"
				+ "DECODE (v.havale_edilen_id, 34, 'B Kurum Temsilcisi', 75, 'A Kurum Temsilcisi', 177, 'C kurum Temsilcisi',"
				+ " (SELECT gorev FROM kullanici_gorev2_view WHERE ID = v.havale_edilen_id   AND durumu = 'A'"
				+ " AND gdurum = 'A'  AND ROWNUM < 2)) AS havale_edilen,  (select tur_adi from HAVALE_TURLERI h where to_number(h.TUR_KODU)=v.sebebi) AS neden , v.durum, v.\"Kayıt Tarihi\" AS kayit_tarihi"
				+ " FROM evrak_havale2_view v  WHERE evrak_id = ? ORDER BY (7)");
		tjdbc.setData(new Object[] { formId });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new TalepFormIslemDetayRowMapper());
		return getDao().bulRowMapper(tjdbc);

	}

	public class dosyaMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {

			IymFilePojo dosya = new IymFilePojo();
			dosya.setDosyaId(rs.getLong("id"));
			dosya.setFileName(rs.getString("FILE_NAME"));
			dosya.setPath(rs.getString("directory"));
			dosya.setDosyaId(rs.getLong("evrak_id"));
			dosya.setSiraNo(rs.getInt("sira_no"));
			return dosya;

		}
	}

	public List<IymFilePojo> gelenDosyaDetayGetir(Long evrakId) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("Select id,file_name,evrak_id,sira_no,case when directory is null then '/iym300/iym/files/' else '/iym300/iym/files/'||directory end as directory  from evrak_files t where t.evrak_id=? order by sira_no");
		tjdbc.setData(new Object[] { evrakId });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new dosyaMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	public boolean evrakHavaleYap(IymGelenEvrakPojo evrak, Long havaleEdilen,
			Long havaleEden, Integer havaleSebebi) {

		if (!evrakHavaleOnayla(evrak)) {

			return false;
		}

		TemelJDBC tjdbc = new TemelJDBC();
		String statement = "INSERT INTO EVRAK_HAVALE (id,evrak_id,evrak_sira_no,"
				+ "havale_eden_id,havale_edilen_id,"
				+ "sebebi,kullanici_id,"
				+ "aciklama,grubami) "
				+ "VALUES(evrak_havale_seq.nextval,?,?,"
				+ "?,?," + "?,?," + "?,?)";
		Object[] obj = { evrak.getEvrakId(), evrak.getEvrakSiraNo(),
				havaleEden, havaleEdilen, havaleSebebi, havaleEden,
				evrak.getAciklama(), "H" };
		int[] i = { Types.VARCHAR, Types.VARCHAR, Types.BIGINT, Types.BIGINT,
				Types.VARCHAR, Types.BIGINT, Types.VARCHAR, Types.VARCHAR, };

		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().ekle(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	private boolean evrakHavaleOnayla(IymGelenEvrakPojo evrak) {
		TemelJDBC tjdbc = new TemelJDBC();

		// Kontrol edilecek yanlış yeri update ediyor olabilir...... havale
		// incele..

		String statement = "Update EVRAK_HAVALE set durum='ONAYLANDI',durum_zamani=sysdate  where id=?  ";
		Object[] obj = { evrak.getEvrakHaveleId() };
		int[] i = { Types.VARCHAR };

		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().degistir(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	private boolean yaziHavaleOnayla(IymEvrakPojo evrak) {
		TemelJDBC tjdbc = new TemelJDBC();
		String statement = "Update EVRAK_HAVALE2 set durum='ONAYLANDI',durum_zamani=sysdate where id=? ";
		Object[] obj = { evrak.getEvrakHavaleId() };
		int[] i = { Types.VARCHAR };

		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().degistir(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	public class HtsEvrakAramaRowMapper implements RowMapper {

		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			IymGelenEvrakPojo gr = new IymGelenEvrakPojo();

			gr.setEvrakId(rs.getLong("yazi_ID"));
			gr.setEvrakHaveleId(rs.getLong("id"));
			gr.setEvrakSiraNo(rs.getString("evrak_Sira_no"));
			gr.setSorusturmaNo(rs.getString("sorusturma_no"));
			gr.setMahkemeKararNo(rs.getString("mahkeme_karar_no"));
			gr.setEvrakNo(rs.getString("evrakno"));
			gr.setEvrakTarih(rs.getString("evrak_Tarih"));
			gr.setKayitTarihi(rs.getString("kayit_tarih"));
			gr.setEvrakKonu(rs.getString("konu"));
			gr.setGelenKurumIl(rs.getString("GeldigiKurum"));
			gr.setCevapYaziSayi(rs.getString("sayi"));
			gr.setCevatTarih(rs.getString("tarih"));
			gr.setCevapImzaDurum(rs.getString("imza_durum"));
			gr.setCevapApsNo(rs.getString("APS_NO"));
			return gr;
		}

	}

	public List gelenGidenEvrakAra(IymEvrakPojo evrak, Date sorguBaslama,
			Date sorguBitis) {
		TemelJDBC tjdbc = new TemelJDBC();

		String kosulStr = "";

		List<Object> data = new ArrayList();
		List<Integer> type = new ArrayList();
		// koşuldaki kullanici id alanı için eklendi

		if (!TemelIslemler.bosMu(evrak.getEvrakSayi())) {
			kosulStr += " ek.\"Evrak No\" = ? and ";
			data.add(evrak.getEvrakSayi());
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getEvrakTarih())) {
			kosulStr += " to_char(EK.\"E.Tarihi\",'dd.mm.yyyy') = ? and ";
			data.add(evrak.getEvrakTarih());
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getEvrakKonu())) {
			kosulStr += " ek.\"Evrak Konusu\" like ? and ";
			data.add("%" + evrak.getEvrakKonu() + "%");
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(sorguBaslama)) {
			kosulStr += " trunc(ek.\"Kayit Tarihi\")>= ? and ";
			data.add(sorguBaslama);
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(sorguBitis)) {
			kosulStr += " trunc(ek.\"Kayit Tarihi\")<= ? and ";
			data.add(sorguBitis);
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getEvrakSorusturmaNo())) {
			kosulStr += "  ek.sorusturma_no = ? and ";
			data.add(evrak.getEvrakSorusturmaNo());
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getEvrakMahkemeKararNo())) {
			kosulStr += "  ek.mahkeme_karar_no = ? and";
			data.add(evrak.getEvrakMahkemeKararNo());
			type.add(Types.VARCHAR);
		}

		// if (!TemelIslemler.bosMu(evrak.getEvrakSorusturmaNo())) {
		// kosulStr += "  ek.sorusturma_no like ? and ";
		// data.add("%" + evrak.getEvrakSorusturmaNo() + "%");
		// type.add(Types.VARCHAR);
		// }
		//
		// if (!TemelIslemler.bosMu(evrak.getEvrakMahkemeKararNo())) {
		// kosulStr +=
		// "  (ek.mahkeme_karar_no like ? or EK.SORUSTURMA_NO like ?) and";
		// // kosulStr += "  ek.SORUSTURMA_NO like ?  and ";
		// data.add("%" + evrak.getEvrakMahkemeKararNo() + "%");
		// type.add(Types.VARCHAR);
		// data.add("%" + evrak.getEvrakMahkemeKararNo() + "%");
		// type.add(Types.VARCHAR);
		// }

		if (!TemelIslemler.bosMu(evrak.getEvrakGelenKurum())) {
			kosulStr += " ek.evrak_geldigi_kurum =? and ";
			data.add(evrak.getEvrakGelenKurum());
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getEvrakGelenIl())) {
			kosulStr += " ek.gel_il = (select il_kod from iller where il_adi =?) and ";
			data.add(evrak.getEvrakGelenIl());
			type.add(Types.VARCHAR);
		}

		if (kosulStr.length() > 0) {
			kosulStr = " and " + kosulStr.substring(0, kosulStr.length() - 5);
			kosulStr += " ORDER BY (11) DESC";
		} else {
			kosulStr = " and ek.\"Kayit Tarihi\">sysdate-3 and ROWNUM < 100 ORDER BY (11) DESC";
		}

		tjdbc.setStatement(" SELECT ek.ID, i.ID AS yazi_id, ek.sorusturma_no, ek.mahkeme_karar_no, ek.evrak_sira_no , ek.\"Evrak No\" as evrakno, to_char(ek.\"E.Tarihi\",'dd.mm.yyyy') as evrak_tarih, ek.\"Geldiği Kurum\" GeldigiKurum, "
				+ " ek.\"Geldiği il\", ek.\"Evrak Konusu\" as konu, to_char(ek.\"Kayit Tarihi\",'dd.mm.yyyy') as kayit_tarih, i.sayi, to_char(i.tarih,'dd.mm.yyyy') as tarih,i.imza_durum,i.aps_no FROM evrak_kayit_view ek, (SELECT y.ID,y.\"İmza Durum\" as imza_durum, "
				+ " CASE WHEN eyi.id_2_sno IS NULL THEN y.ust_evrak_sira_no ELSE eyi.id_2_sno END AS sayi,ya.tarih, ya.aps_no FROM yazilar_view y LEFT JOIN evrak_yazi_iliski eyi ON y.ID =eyi.id_1 LEFT JOIN yazi_aps ya ON y.ID = ya.yazi_id) i WHERE ek.evrak_sira_no = i.sayi AND ek.kay_kullanici NOT IN (20, 15, 16, 17, 75, 18, 739, 177, 34, 1) AND ek.evrak_geldigi_kurum NOT IN ('06', '07', '13', '10', '11')"
				+ kosulStr);

		int types[] = new int[type.size()];
		for (int i = 0; i < type.size(); i++) {
			types[i] = (Integer) type.get(i);
		}

		tjdbc.setData(data.toArray());
		tjdbc.setRowMapper(new HtsEvrakAramaRowMapper());
		// System.out.println(tjdbc.getStatement());
		// return null;
		return getDao().bulRowMapper(tjdbc);
	}

	public class yaziPdfDetayMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {

			IymYaziPojo yazi = new IymYaziPojo();
			yazi.setId(rs.getLong("ID"));
			yazi.setTarih(rs.getDate("TARIH"));
			yazi.setSayi(rs.getString("SAYISI"));
			yazi.setKonu(rs.getString("KONUSU"));
			yazi.setBaslik(TemelIslemler.nullTemizle(rs.getString("BASLIK"))
					.replace("(", "\n("));
			yazi.setIlgi(rs.getString("ILGI"));
			// yazi.setIcerik(TemelIslemler.objeNullTemizle(
			// rs.getString("ICERIK")).replace("\n", "\n\n"));

			String icerik = rs.getString("YAZI_ICERIK");
			if (icerik == null || icerik.trim().length() < 2) {
				icerik = rs.getString("ICERIK");
			}

			yazi.setIcerik(icerik);

			yazi.setIcerik(TemelIslemler.objeNullTemizle(icerik));

			yazi.setDagitimId(rs.getLong("DAGITIM_ID"));
			yazi.setEkId(rs.getLong("EK_ID"));
			yazi.setKayitTarihi(rs.getTimestamp("KAYIT_TARIHI"));
			yazi.setPersonelId(rs.getLong("KULLANICI_ID"));
			yazi.setDurum(rs.getString("DURUM"));
			yazi.setKurumBilgileri(rs.getString("KURUM_BILGILERI"));
			yazi.setDagitim(TemelIslemler.nullTemizle(rs.getString("DAGITIM"))
					.replace("(", "\n("));
			yazi.setIcerikSon(rs.getString("ICERIK_SON"));
			yazi.setEvrakKurum(rs.getString("EVRAK_KURUM"));
			yazi.setEvrakTip(rs.getString("EVRAK_TIPI"));
			yazi.setUstEvrakSiraNo(rs.getString("UST_EVRAK_SIRA_NO"));

			yazi.setGizlilikDereceKod(rs.getString("GIZLILIK_DERECESI"));
			yazi.setIvediDereceKod(rs.getString("IVEDILIK_DERECESI"));
			yazi.setEvrakIl(rs.getString("EVRAK_IL"));
			yazi.setAciklama(rs.getString("ACIKLAMA"));
			yazi.setUstEvrakId(rs.getLong("UST_EVRAK_ID"));
			yazi.setYaziTur(rs.getString("YAZI_TURU"));
			yazi.setAcil(rs.getString("ACILMI"));
			yazi.setElektronikImza(rs.getString("IMZASI"));
			yazi.setImzaId(rs.getInt("IMZA_ID"));
			yazi.setLogoBtk(rs.getString("btklogo"));
			// yazi.setLogoTk(rs.getString(0))
			yazi.setKurumAdi(rs.getString("kurumadi"));
			yazi.setResmiKod(rs.getString("resmi_kod"));
			yazi.setGizlilikDerece(rs.getString("gizlilikderece"));
			yazi.setIvediDerece(rs.getString("ivedilikderece"));

			yazi.setImzaGif(rs.getString("imzagif"));
			yazi.setParafGif(rs.getString("parafgif"));
			yazi.setBilgi(rs.getString("BILGI"));
			yazi.setEkler(rs.getString("EKLER"));
			yazi.setImzaOnayId(rs.getLong("IMZA_ONAY_ID"));
			yazi.setImzaTarih(rs.getDate("IMZA_TARIH"));
			yazi.setImzaAd(rs.getString("IMZA_AD"));
			yazi.setImzaGorevAd(rs.getString("IMZA_GOREV_AD"));
			yazi.setParaf2Ad(rs.getString("PARAF2_AD"));
			yazi.setParaf2GorevAd(rs.getString("PARAF2_GOREV_AD"));

			return yazi;
		}
	}

	public IymYaziPojo PdfDetayGetir(Long yaziId) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("select s.*,(select resmi_kod from kullanicilar k where k.ID=s.KULLANICI_ID) as resmi_kod,(select Adi  from kullanici_gorev2_view kg where gid=s.imza_id ) as imza_ad,(select gorev_imza_adi  from kullanici_gorev2_view kg where gid=s.imza_id) as imza_gorev_ad,(select Adi  from kullanici_gorev2_view kg where gdurum='A' and durumu='A' and gorev_imza_adi is not null and id=s.paraf_id and gorev_kodu=11) as paraf2_Ad,(select gorev_imza_adi  from kullanici_gorev2_view kg where gdurum='A' and durumu='A' and gorev_imza_adi is not null and id=s.paraf_id and gorev_kodu=11) as paraf2_gorev_ad,(SELECT km.imza_dosyasi                      FROM kullanici_gorev2_view kg,                          kullanici_imzalar km                    WHERE km.kullanici_id = kg.ID                      AND km.gorev_kodu = kg.gorev_kodu                      AND km.imza_tipi = 'PARAF'          AND KG.DURUMU ='A' AND KG.GDURUM='A'              AND ID =s.paraf_id) as parafgif from (SELECT y.ID, y.tarih, y.sayisi, y.konusu, y.baslik, y.ilgi, y.icerik, y.yazi_icerik, y.dagitim_id, y.ek_id, y.kayit_tarihi, y.kullanici_id, y.durum,       y.kurum_bilgileri, y.dagitim, y.icerik_son, y.evrak_kurum,       y.evrak_tipi, y.ust_evrak_sira_no, y.gizlilik_derecesi,       y.ivedilik_derecesi, y.evrak_il, y.aciklama, y.ust_evrak_id,       y.yazi_turu, y.acilmi, y.imzası, y.imza_id,       CASE          WHEN y.imza_tarih < TO_DATE ('10112008', 'ddmmyyyy')             THEN 'cbsv_files/tk_logo.gif'          ELSE 'cbsv_files/btk_logo.gif'       END AS btklogo,       CASE          WHEN y.imza_tarih < TO_DATE ('10112008', 'ddmmyyyy')             THEN 'TELEKOMÜNİKASYON KURUMU'          ELSE 'BİLGİ TEKNOLOJİLERİ VE İLETİŞİM KURUMU'       END AS kurumadi,      (SELECT derece                       FROM g_i_derece g                      WHERE g.kod = y.gizlilik_derecesi) AS gizlilikderece,       (SELECT derece          FROM g_i_derece g         WHERE g.kod = y.ivedilik_derecesi) AS ivedilikderece,CASE when y.IMZA_ONAY_ID=12345 then case when substr(y.sayisi,1,1) ='A' then 'cbsv_files/A_Kurum.jpg' when substr(y.sayisi,1,1) ='B' then 'cbsv_files/B_Kurum.jpg' when substr(y.sayisi,1,1) ='C' then 'cbsv_files/C_Kurum.jpg' else ''   end else (SELECT km.imza_dosyasi                  FROM kullanici_gorev2_view kg,                       kullanici_imzalar km                 WHERE km.kullanici_id = kg.ID                   AND km.gorev_kodu = kg.gorev_kodu                   AND gid = y.imza_onay_id and rownum<2) end AS imzagif,       CASE          WHEN (y.paraf2 IS NULL OR y.paraf2 = '0')             THEN y.paraf1          WHEN y.paraf2 IS NOT NULL             THEN y.paraf2       END AS paraf_id,       y.bilgi,       CASE          WHEN INSTR (y.ekler, '#_evrak_tarih') > 1             THEN REPLACE (y.ekler,                           '#_evrak_tarih',                           (SELECT TO_CHAR (giris_tarih, 'dd/mm/yyyy')                              FROM evrak_kayit                             WHERE evrak_sira_no = y.ust_evrak_sira_no)                          )          ELSE y.ekler       END AS ekler,y.IMZA_ONAY_ID		,y.IMZA_TARIH         FROM yazilar y) s WHERE  s.ID=?");
		tjdbc.setData(new Object[] { yaziId });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new yaziPdfDetayMapper());
		return (IymYaziPojo) getDao().bulRowMapper(tjdbc).get(0);
	}

	public boolean kurumKullanicisiKontrol(Long kullaniciId) {

		Integer adet = 0;
		try {
			adet = (Integer) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							"select count(*) from kullanicilar where yetki is not null and id=?",
							new Object[] { kullaniciId },
							new int[] { Types.BIGINT }, Integer.class);

			// System.out.println("Kurum kullanıcı sayisi : " + adet);

		} catch (Exception e) {

		}

		if ("2779".equals(kullaniciId.toString())) {
			System.out.println("BTK karar girici izin verildi....");
			adet = 1;
		}

		if (adet > 0)
			return true;
		else
			return false;

	}

	public boolean tokenKontrol(String kurumlarxmlevrakaramatoken,
			String linkverenkullanici, String ip) {

		Integer adet = 0;
		try {
			adet = (Integer) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							"select count(*) from iym_anahtar where anahtar=? and kullanici=? and talep_Ip=? and talep_Tarih>sysdate-30/24/60",
							new Object[] { kurumlarxmlevrakaramatoken,
									linkverenkullanici, ip },
							new int[] { Types.VARCHAR, Types.VARCHAR,
									Types.VARCHAR }, Integer.class);
		} catch (Exception e) {
			// TODO: handle exception
		}

		if (adet > 0)
			return true;
		else
			return false;

	}
	
	public boolean tokenKontrolWithSession(String kurumlarxmlevrakaramatoken,
			String linkverenkullanici, String ip,String sessionId) {

		Integer adet = 0;

		try {
			adet = (Integer) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							"select count(*) from iym_anahtar where anahtar=? and kullanici=? and talep_Ip=? and talep_session = ? and talep_Tarih>sysdate-5/24/60 ",
							new Object[] { kurumlarxmlevrakaramatoken,
									linkverenkullanici, ip, sessionId },
							new int[] { Types.VARCHAR, Types.VARCHAR,
									Types.VARCHAR , Types.VARCHAR}, Integer.class);
		} catch (Exception e) {
			// TODO: handle exception
		}

		if (adet > 0)
			return true;
		else
			return false;

	}

	public Long kullaniciKontrol(String linkverenkullanici) {

		Long id = 0L;
		try {
			id = (Long) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							"select id from kullanicilar where  kullanici_adi=? and durumu='A' and yetki is not null ",
							new Object[] { linkverenkullanici, },
							new int[] { Types.VARCHAR }, Long.class);
		} catch (Exception e) {
			// TODO: handle exception
		}

		if ("tibkarargirme".equals(linkverenkullanici)) {
			System.out.println("BTK karar girici izin verildi....");
			id = 2779L;
		}

		return id;
	}

	// Talep Formaları

	public String talepFormuSayiAl() {

		String tfsayi = "";
		Long sayi = 0L;
		try {
			sayi = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
					.queryForObject("select tf_yazilar_seq.nextval from dual",
							new Object[] {}, new int[] {}, Long.class);
		} catch (Exception e) {
			// TODO: handle exception
		}

		if (sayi < 10000)
			tfsayi = "GECICI_0" + sayi;
		if (sayi < 1000)
			tfsayi = "GECICI_00" + sayi;
		if (sayi < 100)
			tfsayi = "GECICI_000" + sayi;
		if (sayi < 10)
			tfsayi = "GECICI_0000" + sayi;

		return tfsayi;

	}

	public boolean talepFormuKaydet(ISYSTalepFormlariPojo tf) {

		return true;

	}

	private class ISYS_TalepFormRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {

			IymYaziPojo yazi = new IymYaziPojo();

			yazi.setId(rs.getLong(1));
			yazi.setDurum(rs.getString(2));
			yazi.setTarih(rs.getDate(3));
			yazi.setSayi(rs.getString(4));
			yazi.setKonu(rs.getString(5));

			yazi.setIlgi(rs.getString(6));
			yazi.setEkler(rs.getString(7));
			yazi.setDagitim(rs.getString(8));
			yazi.setKayitTarihi(rs.getTimestamp(9));
			yazi.setAcil(rs.getString(10));
			yazi.setAciklama(rs.getString(11));

			return yazi;
		}
	}

	/*
	 * public List<IymEvrakAramaPojo> talepFormAra(IymYaziPojo evrak, Date
	 * sorguBaslama, Date sorguBitis, Long kullaniciId) { TemelJDBC tjdbc = new
	 * TemelJDBC();
	 * 
	 * String kosulStr = "";
	 * 
	 * List<Object> data = new ArrayList(); List<Integer> type = new
	 * ArrayList(); // koşuldaki kullanici id alanı için eklendi
	 * data.add(kullaniciId); type.add(Types.BIGINT);
	 * 
	 * if (!TemelIslemler.bosMu(evrak.getSayi())) { kosulStr +=
	 * " v.sayisi like ? and "; data.add("%" + evrak.getSayi());
	 * type.add(Types.VARCHAR); }
	 * 
	 * if (!TemelIslemler.bosMu(evrak.getKonu())) { kosulStr +=
	 * " v.konusu like ? and "; data.add("%" + evrak.getKonu() + "%");
	 * type.add(Types.VARCHAR); }
	 * 
	 * if (!TemelIslemler.bosMu(sorguBaslama)) { kosulStr +=
	 * " trunc(v.\"Kayıt Tarihi\")>= ? and "; data.add(sorguBaslama);
	 * type.add(Types.VARCHAR); } if (!TemelIslemler.bosMu(sorguBitis)) {
	 * kosulStr += " trunc(v.\"Kayıt Tarihi\")<= ? and "; data.add(sorguBitis);
	 * type.add(Types.VARCHAR); } if (!TemelIslemler.bosMu(evrak.getAciklama()))
	 * { kosulStr += " v.aciklama like ? and "; data.add("%" +
	 * evrak.getAciklama() + "%"); type.add(Types.VARCHAR); }
	 * 
	 * if (!TemelIslemler.bosMu(evrak.getDurum())) { kosulStr +=
	 * " v.durum = ? and "; data.add(evrak.getDurum()); type.add(Types.VARCHAR);
	 * } else { kosulStr += " v.durum = ? and "; data.add("ONAYLANDI");
	 * type.add(Types.VARCHAR); }
	 * 
	 * if (!TemelIslemler.bosMu(evrak.getYaziTur())) { kosulStr +=
	 * " v.\"İmza Durum\"= ? and "; data.add(evrak.getYaziTur());
	 * type.add(Types.VARCHAR); }
	 * 
	 * if (kosulStr.length() > 0) { kosulStr = " and " + kosulStr.substring(0,
	 * kosulStr.length() - 5); kosulStr += " ORDER BY (3) DESC"; } else {
	 * kosulStr = " and ROWNUM < 200 ORDER BY  (3) DESC"; }
	 * 
	 * tjdbc.setStatement(
	 * "	SELECT   v.ID, v.\"İmza Durum\", v.tarih, v.sayisi, v.konusu, v.ilgi, v.ekler,"
	 * + "  v.dagitim, v.\"Kayıt Tarihi\",nvl(v.ACILMI,'H') as acilmi," +
	 * "  v.aciklama , v.\"K.Adi Soyadi\" ,(select id_1_sno from evrak_yazi_iliski WHERE iliski='YAZI-YAZI' and id_2= v.id and rownum<2) AS cevapSiraNo "
	 * + " FROM yazilar_view v " + " WHERE " + " v.kullanici_id = ?" +
	 * kosulStr);
	 * 
	 * //
	 * "	SELECT   v.ID, v.\"İmza Durum\", v.tarih, v.sayisi, v.konusu, v.ilgi, v.ekler,"
	 * // + "  v.dagitim, v.\"Kayıt Tarihi\",nvl(ev.ACILMI,'H') as acilmi," // +
	 * "  v.aciklama , v.\"K.Adi Soyadi\"" // +
	 * " FROM yazilar_view v, evrak_kayit_view ev, yazi_aps ya" // +
	 * " WHERE v.ust_evrak_sira_no = ev.\"Evrak Sira No\"(+)" // +
	 * " AND v.ID = ya.yazi_id(+)" // + " AND v.kullanici_id = ?" + kosulStr);
	 * 
	 * int types[] = new int[type.size()]; for (int i = 0; i < type.size(); i++)
	 * { types[i] = (Integer) type.get(i); }
	 * 
	 * tjdbc.setData(data.toArray()); tjdbc.setRowMapper(new
	 * TalepFormAramaRowMapper()); System.out.println(tjdbc.getStatement());
	 * System.out.println(data.toArray()); // return null; return
	 * getDao().bulRowMapper(tjdbc); }
	 */

	// Mehmet KARAMAN Talep Formu Kurum Temsilcileri

	public boolean tfKaydet(ISYSTalepFormlariPojo yazi) {

		boolean don = false;

		TemelJDBC tjdbc = new TemelJDBC();

		String statement = "insert into iym.tf_yazilar (id,tarih,sayisi,konusu,baslik,ilgi,icerik,ek_id,kurum_bilgileri,kayit_tarihi,kullanici_id,durum,ekler,dagitim,icerik_son,evrak_kurum,evrak_tipi, ust_evrak_sira_no,bilgi,gizlilik_derecesi,ivedilik_derecesi,evrak_il,aciklama,ust_evrak_id,yazi_turu,acilmi,paraf1,imza_id) values  "
				+ "(tf_yazilar_seq.nextval,sysdate, ?,?,?, ?, ?,?,?, ?, sysdate,?,?,?, ?,?,?, ?,?,?, ?,?,?,    ?, ?,     ?,1453,"
				+ "123)";

		Object[] obj = { yazi.getSayi(), yazi.getKonu(), yazi.getBaslik(),
				yazi.getIlgi(), yazi.getIcerik(), yazi.getEkId(),
				yazi.getKurumBilgileri(), yazi.getPersonelId(),
				yazi.getDurum(), yazi.getEkler(), yazi.getDagitim(),
				yazi.getIcerikSon(), yazi.getEvrakKurum(), yazi.getEvrakTip(),
				yazi.getUstEvrakSiraNo(), yazi.getBilgi(),
				yazi.getGizlilikDerece(), yazi.getIvediDerece(),
				yazi.getEvrakIl(), yazi.getAciklama(), yazi.getUstEvrakId(),
				yazi.getYaziTur(), yazi.getAcil() };
		int[] i = { Types.VARCHAR, Types.VARCHAR, Types.VARCHAR, Types.VARCHAR,
				Types.VARCHAR, Types.BIGINT, Types.VARCHAR, Types.BIGINT,
				Types.VARCHAR, Types.VARCHAR, Types.VARCHAR, Types.VARCHAR,
				Types.VARCHAR, Types.VARCHAR, Types.VARCHAR, Types.VARCHAR,
				Types.VARCHAR, Types.VARCHAR, Types.VARCHAR, Types.VARCHAR,
				Types.VARCHAR, Types.VARCHAR, Types.VARCHAR };

		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		// System.out.println(tjdbc.getStatement());
		try {

			getDao().ekle(tjdbc);
			don = true;
		} catch (Exception e) {
			don = false;

			e.printStackTrace();
		}

		return don;

	}

	public Long kimLogKaydet(KimLogPojo log) {
		Long logId = null;
		try {
			logId = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
					.queryForObject(
							"Select iym.imei_sorgu_log_seq.nextval from dual",
							new Object[] {}, new int[] {}, Long.class);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}

		TemelJDBC tjdbc = new TemelJDBC();

		String statement = "insert into iym.imei_sorgu_log(id, aranan, baslangic_tarihi, bitis_tarihi, sorgu_tarihi, kullanici_id, ip, aciklama, file_name) "
				+ " values (?,?,?,?,sysdate, ?,?,?,?)";

		Object[] obj = { logId, log.getAranan(), log.getSorguBaslamaTarih(),
				log.getSorguBitisTarih(), log.getKullaniciId(),
				log.getIpAdres(), log.getAciklama(), log.getFileName() };
		int[] i = { Types.BIGINT, Types.VARCHAR, Types.DATE, Types.DATE,
				Types.BIGINT, Types.VARCHAR, Types.VARCHAR, Types.VARCHAR };

		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().ekle(tjdbc);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
		return logId;
	}

	public boolean kimImeiSonucLogKaydet(Long imeiLogId, String numara) {
		boolean don = false;
		TemelJDBC tjdbc = new TemelJDBC();

		String statement = "insert into iym.imei_sorgu_sonuc_log(log_id, numara) "
				+ " values (?,?)";

		Object[] obj = { imeiLogId, numara };
		int[] i = { Types.BIGINT, Types.VARCHAR };

		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().ekle(tjdbc);
			don = true;
		} catch (Exception e) {
			don = false;
			e.printStackTrace();
		}
		return don;
	}

	public boolean jandarmaKullanicisiKontrol(Long kullaniciId) {

		return false;
	}

	public class stringMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			String s = new String(rs.getString("str"));
			return s;
		}
	}

	public ArrayList<String> kullaniciKurumKodGetir(Long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		String sql = "SELECT e.kurum_kod as str "
				+ "                 FROM evrak_gelen_kurumlar e, kullanici_kurum kk "
				+ "                WHERE e.kurum_kod = kk.kurum_kod AND kk.kullanici_id = ?";
		tjdbc.setStatement(sql);
		tjdbc.setData(new Object[] { kullaniciId });
		tjdbc.setType(new int[] { Types.VARCHAR });
		tjdbc.setRowMapper(new stringMapper());
		return (ArrayList<String>) getDao().bulRowMapper(tjdbc);

	}

	public class canakNumaraMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			CanakNumaraPojo pojo = new CanakNumaraPojo();
			pojo.setId(rs.getLong("id"));
			pojo.setCanakNo(rs.getString("canak_no"));
			pojo.setKurumKod(rs.getString("kurum_kod"));
			pojo.setKutu(rs.getInt("kutu"));
			return pojo;
		}
	}

	public ArrayList<CanakNumaraPojo> canakNumaraListesiGetir(Long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		String sql = "Select * from (Select id, canak_no, kurum_kod, kutu from canak_numaralar where kurum_kod in ("
				+ "                 SELECT e.kurum_kod "
				+ "                 FROM evrak_gelen_kurumlar e, kullanici_kurum kk "
				+ "                WHERE e.kurum_kod = kk.kurum_kod AND kk.kullanici_id = ? ) and rownum < 21) order by canak_no";
		tjdbc.setStatement(sql);
		// System.out.println("kullaniciId : " + kullaniciId + "   sql:" + sql);
		tjdbc.setData(new Object[] { kullaniciId });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new canakNumaraMapper());
		return (ArrayList<CanakNumaraPojo>) getDao().bulRowMapper(tjdbc);

	}

	public boolean canakNumaraAta(IymEvrakAramaDetayPojo p) {

		Long seq = null;
		try {
			seq = (Long) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							"Select iym.canak_atanmis_numaralar_seq.nextval from dual",
							new Object[] {}, new int[] {}, Long.class);
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}

		p.setCanakAtanmisNumaraId(seq);

		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("insert into canak_atanmis_numaralar (id,canak_no,hedef_no, mahkeme_karar_id, kurum_kod, kutu, tip, islem_tarihi)"
				+ " values (?,?,?, ?,?,?, ?, sysdate)");
		tjdbc.setData(new Object[] { seq, p.getCanakNo(), p.getHedefNo(),
				p.getMahkemeKararId(), p.getEvrakGeldigiKurum(), p.getKutu(),
				p.getCanakTip() });
		tjdbc.setType(new int[] { Types.BIGINT, Types.VARCHAR, Types.VARCHAR,
				Types.BIGINT, Types.VARCHAR, Types.BIGINT, Types.VARCHAR });

		try {
			getDao().ekle(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	public boolean canakNumaraSil(IymEvrakAramaDetayPojo dty) {
		TemelJDBC tjdbc = new TemelJDBC();

		String sql = "DELETE FROM iym.canak_atanmis_numaralar  c "
				+ "  WHERE c.hedef_no = ? AND canak_no = ? AND c.mahkeme_karar_id = ? AND c.kutu = ? AND c.kurum_kod = ? ";

		Object[] obj = { dty.getHedefNo(), dty.getCanakNo(),
				dty.getMahkemeKararId(), dty.getKutu(),
				dty.getEvrakGeldigiKurum() };
		int[] i = { Types.VARCHAR, Types.VARCHAR, Types.BIGINT, Types.INTEGER,
				Types.VARCHAR };

		tjdbc.setStatement(sql);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().sil(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	public boolean canakNumaraSil(Long id) {
		TemelJDBC tjdbc = new TemelJDBC();
		String statement = "DELETE FROM iym.canak_atanmis_numaralar WHERE id=?";
		Object[] obj = { id };
		int[] i = { Types.BIGINT };
		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().sil(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	public String canakNumaraGetir(IymEvrakAramaDetayPojo p) {
		String s;
		// p = new IymEvrakAramaDetayPojo();
		// p.setHedefNo("905057370638");
		// p.setKutu(4);
		// p.setMahkemeKararId(580258L);
		try {
			s = (String) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							"SELECT id || '_' || c.canak_no || '_' || c.tip FROM canak_atanmis_numaralar c "
									+ " WHERE C.HEDEF_NO = ? AND C.MAHKEME_KARAR_ID = ? AND C.KUTU = ? AND C.KURUM_KOD = ? AND ROWNUM < 2",
							new Object[] { p.getHedefNo(),
									p.getMahkemeKararId(), p.getKutu(),
									p.getEvrakGeldigiKurum() },
							new int[] { Types.VARCHAR, Types.BIGINT,
									Types.BIGINT, Types.VARCHAR }, String.class);

		} catch (Exception e) {
			s = "";
		}
		return s;
	}

	
	public class kullaniciKurumIdMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			String kurum = new String(rs.getString("kurum_kod"));
			return kurum;
		}
	}
	
	public List<String> kullaniciKurumGetir(Long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("select kurum_kod from kullanici_kurum k where  k.kullanici_id=?");
		tjdbc.setData(new Object[] { kullaniciId });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new kullaniciKurumIdMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	public String birimGetir(String birimKod) {
		String birim  = (String) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						"select birim_kod from evrak_kurum_birim k where  "
								+ " k.kurum_kod = ?",
						new Object[] { birimKod },
						new int[] { Types.VARCHAR }, String.class);
		return birim;
	}
	
	public String kurumKodGetir(String kurumKod) {
		String birim  = (String) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						"select kurum_harf from evrak_kurum_birim k where  "
								+ " k.kurum_kod = ?",
						new Object[] { kurumKod },
						new int[] { Types.VARCHAR }, String.class);
		return birim;
	}
	
	public Long sucTipiKontrol(String sucTipiKodu) {
		Long sayi  = (Long) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						"select count(*) from mahkeme_suc_tipleri t where  "
								+ " t.suc_tipi = ? and durum='A' ",
						new Object[] { sucTipiKodu },
						new int[] { Types.VARCHAR }, Long.class);
		return sayi;
	}

	public Long mahkemeKontrol(String mahkemeKodu) {
		Long sayi  = (Long) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						"select count(*) from mahkeme_adi t where  "
								+ " t.mahkeme_kodu = ? ",
						new Object[] { mahkemeKodu },
						new int[] { Types.VARCHAR }, Long.class);
		return sayi;
	}

	public Long aidiyatKontrol(String aidiyatKod) {
		Long sayi  = (Long) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						"select count(*) from kdm.kdmkullanicilar t where  "
								+ " t.aidiyat_no = ? ",
						new Object[] { aidiyatKod },
						new int[] { Types.VARCHAR }, Long.class);
		return sayi;
	}

	public Long hitapKuralKontrol(String hedefNo, String hedefTipi,String icindeMi) {
		Long sayi  = (Long) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						"SELECT count(*) FROM hitap.hitap_kurallar hk " +
                          " WHERE hk.hedef_kodu = ? " +
                          " AND ( ? BETWEEN hk.hedef_baslangic AND hk.hedef_bitis) " +
                          " AND ((length(?) between length(hk.hedef_baslangic) AND length(hk.hedef_bitis)))" +
                          " AND hk.icindemi = ?" +
                          " AND hk.state = 'A' ",
						new Object[] { hedefTipi ,hedefNo , hedefNo , icindeMi },
						new int[] { Types.VARCHAR, Types.VARCHAR,Types.VARCHAR,Types.VARCHAR }, Long.class);
		return sayi;
	}

	public Long emailTipiHitapKuralKontrol(String hedefNo, String hedefTipi,String icindeMi) {
		Long sayi  = (Long) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						"SELECT count(*) FROM hitap.hitap_kurallar hk " +
                          " WHERE hk.hedef_kodu = ? " +
                          " AND instr( ? , hedef_baslangic)>0 " +
                          " AND hk.icindemi = ?" +
                          " AND hk.state = 'A' ",
						new Object[] { hedefTipi ,hedefNo , icindeMi },
						new int[] { Types.VARCHAR, Types.VARCHAR,Types.VARCHAR }, Long.class);
		return sayi;
	}
	
	
	public Long xdslUsernameHitapKuralKontrol(String hedefNo, String hedefTipi,String icindeMi) {
		Long sayi  = (Long) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						"SELECT count(*) FROM hitap.hitap_kurallar hk " +
                          " WHERE hk.hedef_kodu = ? " +
                          " AND instr(hedef_baslangic , ? )>0" +
                          " AND SUBSTR(HK.HEDEF_BASLANGIC, INSTR(HK.HEDEF_BASLANGIC, ?)) = ? " +
                          " AND hk.icindemi = ?" +
                          " AND hk.state = 'A' ",
						new Object[] { hedefTipi ,hedefNo,hedefNo,hedefNo , icindeMi },
						new int[] { Types.VARCHAR, Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR }, Long.class);
		return sayi;
	}
	
	public Long turksatMacHedefTipiHitapKuralKontrol(String hedefNo, String hedefTipi,String icindeMi) {
		Long sayi  = (Long) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						" SELECT COUNT(*) FROM hitap.hitap_kurallar hk WHERE" +
						" hk.hedef_kodu = ? " +
						" AND REGEXP_LIKE(UPPER(?), '[0-9A-F]{4}[.][0-9A-F]{4}[.][0-9A-F]{4}') AND LENGTH(?) = 14" +
						" AND REGEXP_LIKE(UPPER(hk.hedef_baslangic), '[0-9A-F]{4}[.][0-9A-F]{4}[.][0-9A-F]{4}') AND LENGTH(hk.hedef_baslangic) = 14" +
						" AND REGEXP_LIKE(UPPER(hk.hedef_bitis), '[0-9A-F]{4}[.][0-9A-F]{4}[.][0-9A-F]{4}') AND LENGTH(hk.hedef_bitis) = 14 " +
						" AND hk.icindemi = ?" +
                        " AND hk.state = 'A' ",
						new Object[] { hedefTipi ,hedefNo ,hedefNo , icindeMi },
						new int[] { Types.VARCHAR, Types.VARCHAR,Types.VARCHAR,Types.VARCHAR }, Long.class);
		return sayi;
	}

	public Long evrakDahaOnceGelmisMiKontrol(String evrakNo, String evrakKurum,String gelenIl, String evrakTarihi) {
		Long sayi  = (Long) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						" SELECT COUNT(*) FROM iym.evrak_kayit e " +
						" WHERE " +
						" e.evrak_no = ? " +
						" AND e.evrak_geldigi_kurum = ? " +
						" AND e.gel_il = ? " +
						" AND e.evrak_tarihi = ? ",
						new Object[] { evrakNo ,evrakKurum ,gelenIl , TemelIslemler.ParseTarih(evrakTarihi)},
						new int[] { Types.VARCHAR, Types.VARCHAR,Types.VARCHAR,Types.DATE }, 
						Long.class);
		System.out.println(evrakNo+" +"+evrakKurum +" +"+gelenIl+" +"+ TemelIslemler.ParseTarih(evrakTarihi));
		return sayi;
	}
	
	public Long sonGonderilenEvrakSayiGetir(String evrakNo, String evrakKurum,String gelenIl, String evrakTarihi) {
		Long sayi  = (Long) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						" SELECT nvl(max(uniq_col),0) FROM iym.evrak_kayit e " +
						" WHERE " +
						" e.evrak_no = ? " +
						" AND e.evrak_geldigi_kurum = ? " +
						" AND e.gel_il = ? " +
						" AND e.evrak_tarihi = ? ",
						new Object[] { evrakNo ,evrakKurum ,gelenIl , TemelIslemler.ParseTarih(evrakTarihi)},
						new int[] { Types.VARCHAR, Types.VARCHAR,Types.VARCHAR,Types.DATE }, 
						Long.class);
		return sayi;
	}
	
	public String mahkemeAdiGetir(String mahkemeKodu) {
		String mahkemeAdi = null;
		try {
			mahkemeAdi  = (String) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							" SELECT m.mahkeme_adi FROM iym.mahkeme_adi m " +
							" WHERE " +
							" m.mahkeme_kodu = ? ",
							new Object[] { mahkemeKodu },
							new int[] { Types.VARCHAR}, 
							String.class);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return mahkemeAdi;
	}
	
	public  void xmlIslemLog(Long kullaniciId,String ip,String islem,String islem_tablo,String islem_tablo_ref_id,Long islemId){
		TemelJDBC tjdbc = new TemelJDBC();
		
		String kullaniciAd = kullaniciAdiGetir(kullaniciId);
		
		String sql = "INSERT INTO XML_ISLEM_LOG (ID,KULLANICI_ID, IP, TARIH, ISLEM, ISLEM_TABLO, ISLEM_TABLO_REF_ID,ISLEM_ID,KULLANICI_ADI) " +
					" VALUES "+
					" (XML_ISLEM_LOG_SEQ.nextval, ?,?,SYSDATE, ?, ?, ?, ?, ?)";
		
		Object[] obj = { kullaniciId, ip, islem, islem_tablo, islem_tablo_ref_id,islemId,kullaniciAd };
		int[] i = {Types.BIGINT, Types.VARCHAR, Types.VARCHAR,Types.VARCHAR,Types.VARCHAR ,Types.BIGINT,Types.VARCHAR};
		
		tjdbc.setStatement(sql);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().ekle(tjdbc);
			Log4jService.getIymv3XmlIslemLog().info(TemelIslemler.TurkZaman(new java.util.Date())+"|"+tjdbc.getStatement()+"|"+kullaniciId+"|"+kullaniciAd+"|"+ip+"|"+islem+"|"+islem_tablo+"|"+islem_tablo_ref_id+"|"+islemId);
		} catch (Exception e) {
			Log4jService.getIymv3XmlIslemLog().info(TemelIslemler.TurkZaman(new java.util.Date())+"|"+tjdbc.getStatement()+"|"+kullaniciId+"|"+kullaniciAd+"|"+ip+"|"+islem+"|"+islem_tablo+"|"+islem_tablo_ref_id+"|"+islemId);
		}
	}
	
	private String kullaniciAdiGetir(Long kullaniciId) {
		
		String kullaniciAdi = "";
		TemelJDBC tjdbc = new TemelJDBC();
		try{
			kullaniciAdi  = (String) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							" Select k.kullanici_adi from kullanicilar k where k.id = ? ",
							new Object[] { kullaniciId },
							new int[] { Types.BIGINT }, 
							String.class);
		
		}catch(Exception ex){
			kullaniciAdi = "";
			logger.error("kullaniciAdiGetir kullaniciId:" + kullaniciId, ex);
		}
		
		return kullaniciAdi;
	}

	public String evrakSayiAl(String evrakTipi, String evrakKurum) {

		CallableStatement cs = null;
		String procedure = "{call GET_EVRAK_SIRANO2(?,?,?)}";
		String sonuc = null;
		try {
			cs = getDao().procedureCall(procedure);
			cs.setString(1, evrakTipi);
			cs.setString(2, evrakKurum);
			cs.registerOutParameter(3, Types.VARCHAR);
			cs.execute();
			sonuc = cs.getString(3);

		} catch (Exception e) {
			e.printStackTrace();
		}

		finally {
			if (cs != null) {
				try {
					cs.close();
				} catch (Exception e) {
					// TODO: handle exception
				}
			}
		}
		return sonuc;
	}
	public String getEvrakDurumByKullaniciEvrakId(String eNo,String kNo){
		TemelJDBC tjdbc = new TemelJDBC();
		String durum  = (String) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						" SELECT k.durumu FROM evrak_kayit k  " +
						" WHERE " +
						" k.evrak_no = ? " +
						" and k.kay_kullanici = ?",
						new Object[] { eNo,kNo },
						new int[] { Types.VARCHAR,Types.VARCHAR}, 
						String.class);
		return durum;
	}
	
	public String getEvrakSiraNoByKullaniciEvrakId(String eNo,String kNo){
		TemelJDBC tjdbc = new TemelJDBC();
		String durum  = (String) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						" SELECT k.evrak_sira_no FROM evrak_kayit k  " +
						" WHERE " +
						" k.evrak_no = ? " +
						" and k.kay_kullanici = ?",
						new Object[] { eNo,kNo },
						new int[] { Types.VARCHAR,Types.VARCHAR}, 
						String.class);
		return durum;
	}

	////KT.2024-008804 Geregi. gonderilen canak numarasinin sistemde kayitli/onayli olup olmadigini kontrol et.
	public boolean canakNumaraOnayliMi(String canakNo, String kurumKodu){
		boolean result = false;

		Integer onayliCanakNo = 0;
		try {
			onayliCanakNo = (Integer) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							"select count(*) from canak_numaralar where canak_no=? and kurum_kod=?",
							new Object[] { canakNo, kurumKodu},
							new int[] { Types.VARCHAR, Types.VARCHAR}, Integer.class);
		} catch (Exception e) {
			result = false;
		}
		result = onayliCanakNo > 0;

		return result;
	}

	public EVRAK_KAYIT yeniXmlKayit(EVRAK_KAYIT xmlEvrak, String evrakKurum,String evrakBirim, String evrakTipi,Long personelIymId, String xmlYolu, String clientIp){
		Connection con = null;
		String anaKlasorYolu = "";
		String anaKlasor="";
		Long mahkemeKararId = 0L;
		
		XmlEvrakPojo evrak = Utility.mapEvrak(xmlEvrak, personelIymId,evrakBirim,evrakTipi,evrakKurum);

		try {
			con = getDao().getJdbcTemplate().getDataSource().getConnection();
			if (con == null) {
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-2-01] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-2-01] Lütfen BTK ile irtibata geçiniz!");
			}
			con.setAutoCommit(false);
			/*******************EVRAK KAYIT*************************************/
			Long evrakId = evrakIdAl();
			if(evrakId == 0L){
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-2-02] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-2-02] Lütfen BTK ile irtibata geçiniz!");
			}
			evrak.setId(evrakId);
			evrak.setEvrakTipi(evrakTipi);
			String evrakSiraNo = evrakSayiAl(evrak.getEvrakTipi(),evrak.getEvrakGeldigiKurum());
			if(evrakSiraNo == null || evrakSiraNo.equals("")){
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-2-03] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-2-03] Lütfen BTK ile irtibata geçiniz!");
			}
			evrak.setEvrakSiraNo(evrakSiraNo);
			evrak.setEvrakYonu(EVRAK_TIPLERI.ILETISIMIN_DENETLENMESI.getTip());
			if (!xmlEvrakKaydet(con, evrak,personelIymId,clientIp,xmlEvrak.islemId)) {
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-2-04] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-2-04] Lütfen BTK ile irtibata geçiniz!");
			}
			String kurumKod = kurumKodGetir(evrakKurum);
			boolean isCanakUpdate = false;
			for(MAHKEME_KARAR mk:xmlEvrak.MAHKEME_KARAR){
				/***********MAHKEME KARAR TALEP ****************************/
				MahkemeKararTalepPojo mahkemeKarar = Utility.mapMahkemeKarar(mk,evrak.getId(),personelIymId);
				String mahkemeAdi = mahkemeAdiGetir(mahkemeKarar.getMahkemeKodu());
				if(mahkemeAdi == null || mahkemeAdi.equals("")){
					mk.hatalar2.add(new XMLHATA("MAHKEME_KODU"," MAHKEME KARARDAKİ ["+mahkemeKarar.getMahkemeKodu()+"] MAHKEME KODU SİSTEMDE YOK!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-2-05] Lütfen BTK ile irtibata geçiniz!");
				}
				mahkemeKarar.setMahkemeAdi(mahkemeAdi);
				mahkemeKarar.setHukukBirim("HUKUK-TALEP");
			    mahkemeKararId = mahkemeKararTalepIdAl();
				if(mahkemeKararId == 0L){
					mk.hatalar2.add(new XMLHATA("MAHKEME_KARAR","HATA  Kod : [HATA-2-06] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-2-06] Lütfen BTK ile irtibata geçiniz!");
				}
				mahkemeKarar.setId(mahkemeKararId);
				
				//TODO ÖNEMLİ
				if(mahkemeKarar.getKararTip().equals("410")){
					mahkemeKarar.setKararTip("400");
					mahkemeKarar.setAciklama(mahkemeKarar.getAciklama()!=null?(mahkemeKarar.getAciklama()+"--ASIL karar tipi:410"):"ASIL karar tipi:410");
				}
				if(mahkemeKarar.getKararTip().equals("730")){
					mahkemeKarar.setKararTip("700");
					mahkemeKarar.setAciklama(mahkemeKarar.getAciklama()!=null?(mahkemeKarar.getAciklama()+"--ASIL karar tipi:730"):"ASIL karar tipi:730");
				}
				if(mahkemeKarar.getKararTip().equals(MAHKEME_KARAR_TIPLERI.HEDEF_CANAK_DEGISTIRME.getKararKodu())){
					mahkemeKarar.setAciklama(mahkemeKarar.getAciklama() + " - Hedef CANAK Güncelleme ");
					isCanakUpdate = true;
				}
				
				if (!mahkemeKararTalepKaydet(con, mahkemeKarar,personelIymId,clientIp,xmlEvrak.islemId)) {
					mk.hatalar2.add(new XMLHATA("MAHKEME_KARAR","HATA  Kod : [HATA-2-07] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-2-07] Lütfen BTK ile irtibata geçiniz!");
				}				
				/*********************************MAHKEME AİDİYAT ********	********************/
				List<MahkemeAidiyatTalepPojo> aidiyatList= Utility.mapMahkemeAidiyat(mk.MAHKEME_AIDIYAT, mahkemeKarar.getId());
				List<String> aidiyatKodList = Utility.mapAidiyatKodList(aidiyatList);
				for(MahkemeAidiyatTalepPojo a : aidiyatList){
					Long aidiyatId = mahkemeAidiyatTalepIdAl();
					if(aidiyatId == 0L){
						mk.MAHKEME_AIDIYAT.hatalar2.add(new XMLHATA("MAHKEME_KARAR","HATA  Kod : [HATA-2-08] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
						throw new Exception("HATA  Kod : [HATA-2-08] Lütfen BTK ile irtibata geçiniz!");
					}
					a.setId(aidiyatId);
					if (!mahkemeAidiyatTalepKaydet(con, a,personelIymId,clientIp,xmlEvrak.islemId)) {
						mk.MAHKEME_AIDIYAT.hatalar2.add(new XMLHATA("MAHKEME_KARAR","HATA  Kod : [HATA-2-09] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
						throw new Exception("HATA  Kod : [HATA-2-09] Lütfen BTK ile irtibata geçiniz!");
					}
				}
				/********************************MAHKEME SUÇLAR******************************/
				List<MahkemeSuclarTalepPojo> sucTipiList= Utility.mapMahkemeSuclar(mk.MAHKEME_SUC_TIPI, mahkemeKarar.getId());
				for(MahkemeSuclarTalepPojo s : sucTipiList){
					Long sucId = mahkemeSuclarTalepIdAl();
					if(sucId == 0L){
						mk.MAHKEME_SUC_TIPI.hatalar2.add(new XMLHATA("MAHKEME_KARAR","HATA  Kod : [HATA-2-10] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
						throw new Exception("HATA  Kod : [HATA-2-10] Lütfen BTK ile irtibata geçiniz!");
					}
					s.setId(sucId);
					if (!mahkemeSuclarTalepKaydet(con, s,personelIymId,clientIp,xmlEvrak.islemId)) {
						mk.MAHKEME_SUC_TIPI.hatalar2.add(new XMLHATA("MAHKEME_KARAR","HATA  Kod : [HATA-2-11] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
						throw new Exception("HATA  Kod : [HATA-2-11] Lütfen BTK ile irtibata geçiniz!");
					}
				}
				/*************************MAHEKEME KARAR DETAY**********************************/
				for(MAHKEME_KARAR_DETAY d : mk.MAHKEME_KARAR_DETAY){
					MahkemeKararDetayTalepPojo mahkemeKararDetay = Utility.mapMahkemeKararDetay(d,evrak.getId() , null ,mahkemeKarar.getId(),personelIymId);
					String iliskiliMahkemeId ="";
					String mahkemeAdiDetay = mahkemeAdiGetir(mahkemeKararDetay.getMahkemeKoduDetay());
					if(mahkemeAdiDetay == null || mahkemeAdiDetay.equals("")){
						d.hatalar2.add(new XMLHATA("MAHKEME_KODU_DETAY","HATA  Kod : [HATA-2-12] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
						throw new Exception("HATA  Kod : [HATA-2-12] Lütfen BTK ile irtibata geçiniz!");
					}
					//Redmine: 0000 geregi
					if(mahkemeKarar.getKararTip().equals(MAHKEME_KARAR_TIPLERI.HEDEF_CANAK_DEGISTIRME.getKararKodu())) {
						Long anaMahkemeKararId = mahkemeKararIdGetir(mahkemeKararDetay.getMahkemeIliDetay(), mahkemeKararDetay.getMahkemeKararNoDetay(), mahkemeKararDetay.getMahkemeKoduDetay(), mahkemeKararDetay.getSorusturmaNoDetay(), evrakKurum);
						if (anaMahkemeKararId == null || anaMahkemeKararId < 0L) {
							d.hatalar2.add(new XMLHATA("MAHKEME_KARAR_DETAY", "DETAYDA BELİRTİLEN KARAR NO[" + mahkemeKararDetay.getMahkemeKararNoDetay() + "] SOR.NO[" + mahkemeKararDetay.getSorusturmaNoDetay() + "] ILI [" + mahkemeKararDetay.getMahkemeIliDetay() + "] MAH.KODU[" + mahkemeKararDetay.getMahkemeKoduDetay() + "] SISTEMDE BULUNAMADI", personelIymId, clientIp, xmlEvrak.islemId));
							throw new Exception("HATA  Kod : [HATA-2-99] Lütfen BTK ile irtibata geçiniz!");
						}
					}



					mahkemeKararDetay.setMahkemeAdiDetay(mahkemeAdiDetay);
					Long detayTalepId = mahkemeKararDetayTalepIdAl();
					if(detayTalepId == 0L){
						d.hatalar2.add(new XMLHATA("MAHKEME_KODU_DETAY","HATA  Kod : [HATA-2-13] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
						throw new Exception("HATA  Kod : [HATA-2-13] Lütfen BTK ile irtibata geçiniz!");
					}
					mahkemeKararDetay.setId(detayTalepId);
					/***********************MAHKEME AIDIYAT DETAY***********************************/
					List<MahkemeAidiyatDetayTalepPojo> aidiyatDetayTalepList = Utility.mapMahkemeAidiyatDetay(d.MAHKEME_AIDIYAT_DETAY, null, mahkemeKarar.getId(), mahkemeKararDetay.getId());
					for(MahkemeAidiyatDetayTalepPojo detay: aidiyatDetayTalepList){
						Long aidiyatDetayId = mahkemeAidiyatDetayTalepIdAl();
						if(aidiyatDetayId == 0L){
							d.MAHKEME_AIDIYAT_DETAY.hatalar2.add(new XMLHATA("MAHKEME_AIDIYAT_DETAY","HATA  Kod : [HATA-2-15] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
							throw new Exception("HATA  Kod : [HATA-2-15] Lütfen BTK ile irtibata geçiniz!");
						}
					    iliskiliMahkemeId = iliskiliMahkemeIdGetir(mahkemeKararDetay.getMahkemeIliDetay(),mahkemeKararDetay.getMahkemeKararNoDetay(),mahkemeKararDetay.getMahkemeKoduDetay(),mahkemeKararDetay.getSorusturmaNoDetay(),evrakKurum);
						if(iliskiliMahkemeId== null || iliskiliMahkemeId.equals("")){							
							d.MAHKEME_AIDIYAT_DETAY.hatalar2.add(new XMLHATA("MAHKEME_AIDIYAT_DETAY","DETAYDA BELİRTİLEN KARAR NO["+mahkemeKararDetay.getMahkemeKararNoDetay()+"] SOR.NO["+mahkemeKararDetay.getSorusturmaNoDetay()+"] ILI ["+mahkemeKararDetay.getMahkemeIliDetay()+"] MAH.KODU["+mahkemeKararDetay.getMahkemeKoduDetay()+"] MAHKEME KARAR SİSTEMDE BULUNAMADI",personelIymId,clientIp,xmlEvrak.islemId));					    	
							throw new Exception("HATA  Kod : [HATA-2-35] Lütfen BTK ile irtibata geçiniz!");
						}
						detay.setIliskiliMahkemeKararId(new Long(iliskiliMahkemeId));
						detay.setId(aidiyatDetayId);
						if (!mahkemeAidiyatDetayTalepKaydet(con, detay,personelIymId,clientIp,xmlEvrak.islemId)) {
							d.MAHKEME_AIDIYAT_DETAY.hatalar2.add(new XMLHATA("MAHKEME_AIDIYAT_DETAY","HATA  Kod : [HATA-2-16] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
							throw new Exception("HATA  Kod : [HATA-2-16] Lütfen BTK ile irtibata geçiniz!");
						}
					}
					/***********************HEDEFLER DETAY***********************************/
					for(HEDEFLER_DETAY hd : d.HEDEFLER_DETAY){
						HedeflerDetayTalepPojo hedefDetay = Utility.mapHedeflerDetay(hd,null,mahkemeKarar.getId(),mahkemeKararDetay.getId());
						String gercekHedefTipi =  hd.HEDEF_TIPI;
					    iliskiliMahkemeId = hedefIliskiliMahkemeIdGetir(mahkemeKararDetay.getMahkemeIliDetay(),mahkemeKararDetay.getMahkemeKararNoDetay(),mahkemeKararDetay.getMahkemeKoduDetay(),mahkemeKararDetay.getSorusturmaNoDetay(),evrakKurum,hd.HEDEF_NO,gercekHedefTipi);
						if(iliskiliMahkemeId== null || iliskiliMahkemeId.equals("")){
					    	hd.hatalar2.add(new XMLHATA("HEDEFLER_DETAY",hd.HEDEF_NO + " DETAYDA BELİRTİLEN KARAR NO["+mahkemeKararDetay.getMahkemeKararNoDetay()+"] SOR.NO["+mahkemeKararDetay.getSorusturmaNoDetay()+"] ILI ["+mahkemeKararDetay.getMahkemeIliDetay()+"] MAH.KODU["+mahkemeKararDetay.getMahkemeKoduDetay()+"] MAHKEME KARARDA BULUNAMADI",personelIymId,clientIp,xmlEvrak.islemId));
							throw new Exception("HATA  Kod : [HATA-2-36] Lütfen BTK ile irtibata geçiniz!");
						}
						if(!Utility.hedefMahkemeKararKontrol(iliskiliMahkemeId,hedefDetay.getHedefNo(),evrakKurum,gercekHedefTipi,mk.SORUSTURMA_NO)){
							hd.hatalar2.add(new XMLHATA("HEDEFLER_DETAY","DETAYDA BELİRTİLEN HEDEF ["+hedefDetay.getHedefNo()+"] İLİŞKİLENDİRİLEN KARARDA BULUNAMADI",personelIymId,clientIp,xmlEvrak.islemId));
							throw new Exception("HATA  Kod : [HATA-2-37] Lütfen BTK ile irtibata geçiniz!");
						}

						if(mahkemeKarar.getKararTip().equals(MAHKEME_KARAR_TIPLERI.HEDEF_CANAK_DEGISTIRME.getKararKodu())){

							//KT.2024-008804 Geregi gonderilen canak numarasinin sistemde onayli olup olmadigini kontrol et.
							String canakNo = hd.CANAK_NO;
							if(!TemelIslemler.isNullOrEmpty(canakNo)){
								boolean onayliCanakNo = canakNumaraOnayliMi(canakNo, evrak.getEvrakGeldigiKurum());
								if(!onayliCanakNo){
									hd.hatalar2.add(new XMLHATA("HEDEFLER_DETAY","HATA  Kod : [HATA-2-100] CANAK NO :" + hd.CANAK_NO + " sistemde tanımlı değildir. BTK ile irtibata geçiniz.",personelIymId,clientIp,xmlEvrak.islemId));
									throw new Exception("HATA  Kod : [HATA-2-100] CANAK NO :" + hd.CANAK_NO + " sistemde tanımlı değildir. BTK ile irtibata geçiniz.");
								}
							}

						}



						Long hedefDetayId = hedeflerDetayTalepIdAl();
						if(hedefDetayId == 0L){
							hd.hatalar2.add(new XMLHATA("HEDEFLER_DETAY","HATA  Kod : [HATA-2-17] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
							throw new Exception("HATA  Kod : [HATA-2-17] Lütfen BTK ile irtibata geçiniz!");
						}
						hedefDetay.setId(hedefDetayId);
						Long iliskiliHedefId = iliskiliHedefGetir(new Long(iliskiliMahkemeId),hedefDetay.getHedefNo(),hedefDetay.getHedefTipi());
						hedefDetay.setIliskiliHedefId(iliskiliHedefId);
						if (!hedeflerDetayTalepKaydet(con, hedefDetay,personelIymId,clientIp,xmlEvrak.islemId)) {
							hd.hatalar2.add(new XMLHATA("HEDEFLER_DETAY","HATA  Kod : [HATA-2-18] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
							throw new Exception("HATA  Kod : [HATA-2-18] Lütfen BTK ile irtibata geçiniz!");
						}
					}
					/**************MAHKEME KODU DEGISTIR ve DETAY KAYDET*********/
					if(mahkemeKarar.getKararTip().equalsIgnoreCase(MAHKEME_KARAR_TIPLERI.MAHKEME_KODU_DEGISTIRME.getKararKodu())){
					    iliskiliMahkemeId = iliskiliMahkemeIdGetir(mahkemeKararDetay.getMahkemeIliDetay(),mahkemeKararDetay.getMahkemeKararNoDetay(),mahkemeKararDetay.getMahkemeKoduDetay(),mahkemeKararDetay.getSorusturmaNoDetay(),evrakKurum);
						if(iliskiliMahkemeId== null || iliskiliMahkemeId.equals("")){
					    	d.hatalar2.add(new XMLHATA("MAHKEME_KARAR_DETAY","DETAYDA BELİRTİLEN KARAR NO["+mahkemeKararDetay.getMahkemeKararNoDetay()+"] SOR.NO["+mahkemeKararDetay.getSorusturmaNoDetay()+"] ILI ["+mahkemeKararDetay.getMahkemeIliDetay()+"] MAH.KODU["+mahkemeKararDetay.getMahkemeKoduDetay()+"] MAHKEME KARAR SİSTEMDE BULUNAMADI",personelIymId,clientIp,xmlEvrak.islemId));
							throw new Exception("HATA  Kod : [HATA-2-34] Lütfen BTK ile irtibata geçiniz!");
						}	
					}
					if(!(iliskiliMahkemeId== null || iliskiliMahkemeId.equalsIgnoreCase("")))
						mahkemeKararDetay.setIliskiliMahkemeKararId(new Long(iliskiliMahkemeId));
					
					if (!mahkemeKararDetayTalepKaydet(con, mahkemeKararDetay,personelIymId,clientIp,xmlEvrak.islemId)) {
						d.hatalar2.add(new XMLHATA("MAHKEME_KODU_DETAY","HATA  Kod : [HATA-2-14] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
						throw new Exception("HATA  Kod : [HATA-2-14] Lütfen BTK ile irtibata geçiniz!");
					}
				}
				/***********************HEDEFLER***********************************/
				for(HEDEFLER h : mk.HEDEFLER){
					String iliskiliMahkemeId = "";
					MahkemeKararDetayTalepPojo mahkemeKararDetay =null;
					/********************MAHKEME KARAR DETAY YENİ**************************/
						for(MAHKEME_KARAR_DETAY dty: h.MAHKEME_KARAR_DETAY){
							mahkemeKararDetay = Utility.mapMahkemeKararDetay(dty,evrak.getId() , null ,mahkemeKarar.getId(),personelIymId);
						
							String mahkemeAdiDetay = mahkemeAdiGetir(mahkemeKararDetay.getMahkemeKoduDetay());
							if(mahkemeAdiDetay == null || mahkemeAdiDetay.equals("")){
								dty.hatalar2.add(new XMLHATA("MAHKEME_KARAR_DETAY","DETAY KARARDAKİ MAHKEME ["+mahkemeKararDetay.getMahkemeKoduDetay()+"] SİSTEMDE BULUNAMADI!",personelIymId,clientIp,xmlEvrak.islemId));
								throw new Exception("HATA  Kod : [HATA-2-19] Lütfen BTK ile irtibata geçiniz!");
								
							}
							mahkemeKararDetay.setMahkemeAdiDetay(mahkemeAdiDetay);
							Long detayTalepId = mahkemeKararDetayTalepIdAl();
							if(detayTalepId == 0L){
								dty.hatalar2.add(new XMLHATA("MAHKEME_KARAR_DETAY","HATA  Kod : [HATA-2-20] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
								throw new Exception("HATA  Kod : [HATA-2-20] Lütfen BTK ile irtibata geçiniz!");
							}
							mahkemeKararDetay.setId(detayTalepId);
		
							String gercekHedefTipi =  h.getHedefTipi();
						//	iliskiliMahkemeId = iliskiliKararBul(xmlEvrak,evrakKurum, personelIymId, clientIp, h, mahkemeKararDetay, dty,gercekHedefTipi);
							iliskiliMahkemeId = hedefIliskiliMahkemeIdGetir(mahkemeKararDetay.getMahkemeIliDetay(),mahkemeKararDetay.getMahkemeKararNoDetay(),mahkemeKararDetay.getMahkemeKoduDetay(),mahkemeKararDetay.getSorusturmaNoDetay(),evrakKurum,h.HEDEF_NO,gercekHedefTipi);
							if(iliskiliMahkemeId== null || iliskiliMahkemeId.equals("")){
								dty.hatalar2.add(new XMLHATA("MAHKEME_KARAR_DETAY","HEDEF NO ["+h.HEDEF_NO+"] HEDEFIN DETAY KARARINDA BELİRTİLEN   KARAR NO["+mahkemeKararDetay.getMahkemeKararNoDetay()+"] SOR.NO["+mahkemeKararDetay.getSorusturmaNoDetay()+"] ILI ["+mahkemeKararDetay.getMahkemeIliDetay()+"] MAH.KODU["+mahkemeKararDetay.getMahkemeKoduDetay()+"]  MAHKEME KARAR SİSTEMDE BULUNAMADI",personelIymId,clientIp,xmlEvrak.islemId));
								throw new Exception("HATA  Kod : [HATA-2-22] Lütfen BTK ile irtibata geçiniz!");
							}
							
							
							mahkemeKararDetay.setIliskiliMahkemeKararId(new Long(iliskiliMahkemeId));
							
							if (!mahkemeKararDetayTalepKaydet(con, mahkemeKararDetay,personelIymId,clientIp,xmlEvrak.islemId)) {
								dty.hatalar2.add(new XMLHATA("MAHKEME_KARAR_DETAY","HATA  Kod : [HATA-2-21] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
								throw new Exception("HATA  Kod : [HATA-2-21] Lütfen BTK ile irtibata geçiniz!");
							}
							
						}
					/***********************HEDEFLER***********************************/
					HedeflerTalepPojo hedefTalep = Utility.mapHedefler(h, mahkemeKarar.getId(), personelIymId);
					
					if((hedefTalep.getSureTipi()==null || hedefTalep.getSureTipi().equals(""))
							&& mahkemeKarar.getKararTip().equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_YAZILI_EMIR.getKararKodu())){
						hedefTalep.setSureTipi("0");
					}
					Long hedefId = hedeflerTalepIdAl();
					if(hedefId == 0L){
						h.hatalar2.add(new XMLHATA("HEDEFLER","HATA  Kod : [HATA-2-23] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
						throw new Exception("HATA  Kod : [HATA-2-23] Lütfen BTK ile irtibata geçiniz!");
					}
					hedefTalep.setId(hedefId);
					
					String mahkemeKararTuru = mk.turBelirle(h).toString();
					if(mahkemeKararTuru.equals(MAHKEME_KARAR_ISLEM_TURU.UZATMA.toString())){
						if(h.UZATMA_SAYISI.equals("0")){	//uzatma bilgisi olan bir kararda uzatma sayısı 0 olamaz
							h.hatalar2.add(new XMLHATA("HEDEFLER","UZATMA BİLGİSİ  HEDEF NO : ["+h.HEDEF_NO+"] OLAN BİR HEDEF DE UZATMA SAYISI 0 OLAMAZ!",personelIymId,clientIp,xmlEvrak.islemId));
							throw new Exception("HATA  Kod : [HATA-2-24] Lütfen BTK ile irtibata geçiniz!");
						}else{
							String gercekHedefTipi =  h.getHedefTipi();
							if(Utility.hedefMahkemeKararKontrol(iliskiliMahkemeId,hedefTalep.getHedefNo(),evrakKurum,gercekHedefTipi,mk.SORUSTURMA_NO)){
								if(Utility.hedefSonMahkemeKarariIleUzatilmisMiKontrol(iliskiliMahkemeId,hedefTalep,evrakKurum,mahkemeKararDetay,personelIymId)){
									hedefTalep.setUzatmaId(iliskiliMahkemeId);
								}else {
									h.hatalar2.add(new XMLHATA("HEDEFLER","DETAY KARARDAKİ HEDEF NO : ["+h.HEDEF_NO+"] OLAN HEDEFİN UZATMA KARARI YANLIŞ GİRİLMİŞ!",personelIymId,clientIp,xmlEvrak.islemId));
									throw new Exception("HATA  Kod : [HATA-2-25] Lütfen BTK ile irtibata geçiniz!");
								}
							}else{
								h.hatalar2.add(new XMLHATA("HEDEFLER","DETAY KARARDAKİ HEDEF NO : ["+h.HEDEF_NO+"] OLAN HEDEFİN UZATMA BİLGİSİ İLE İLİŞKİLENDİRİLEMEDİ(SORUŞTURMA NO DEĞİŞİKLİĞİ)!",personelIymId,clientIp,xmlEvrak.islemId));
								throw new Exception("HATA  Kod : [HATA-2-26] Lütfen BTK ile irtibata geçiniz!");
							}
						}
					}else if(mahkemeKararTuru.equals(MAHKEME_KARAR_ISLEM_TURU.SONLANDIRMA.toString())){
						String gercekHedefTipi = h.getHedefTipi();
						if(Utility.hedefMahkemeKararKontrol(iliskiliMahkemeId,hedefTalep.getHedefNo(),evrakKurum,gercekHedefTipi,mk.SORUSTURMA_NO)){
							hedefTalep.setKapatmaKararId(iliskiliMahkemeId);
						}else{
							h.hatalar2.add(new XMLHATA("HEDEFLER","DETAY KARARDAKİ HEDEF NO : ["+h.HEDEF_NO+"] OLAN HEDEF SONLANDIRMA BİLGİSİ İLE İLİŞKİLENDİRİLEMEDİ!",personelIymId,clientIp,xmlEvrak.islemId));
							throw new Exception("HATA  Kod : [HATA-2-27] Lütfen BTK ile irtibata geçiniz!");
						}
					}


					//KT.2024-008804 Geregi gonderilen canak numarasinin sistemde onayli olup olmadigini kontrol et.
					String canakNo = hedefTalep.getCanakNo();
					if(!TemelIslemler.isNullOrEmpty(canakNo)){
						boolean onayliCanakNo = canakNumaraOnayliMi(canakNo, evrak.getEvrakGeldigiKurum());
						if(!onayliCanakNo){
							h.hatalar2.add(new XMLHATA("HEDEFLER","HATA  Kod : [HATA-2-100] CANAK NO :" + h.CANAK_NO + " sistemde tanımlı değildir. BTK ile irtibata geçiniz.",personelIymId,clientIp,xmlEvrak.islemId));
							throw new Exception("HATA  Kod : [HATA-2-100] CANAK NO :" + h.CANAK_NO + " sistemde tanımlı değildir. BTK ile irtibata geçiniz.");
						}
					}

					if(mahkemeKararTuru.equals(MAHKEME_KARAR_ISLEM_TURU.HEDEF_CANAK_DEGISTIRME.toString())){
						hedefTalep.setSuresi("1");
						hedefTalep.setSureTipi("1");
					}


					if (!hedeflerTalepKaydet(con, hedefTalep,personelIymId,clientIp,xmlEvrak.islemId)) {
						h.hatalar2.add(new XMLHATA("HEDEFLER","HATA  Kod : [HATA-2-28] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
						throw new Exception("HATA  Kod : [HATA-2-28] Lütfen BTK ile irtibata geçiniz!");
					}
					/**********************BIM HEDEFLER AIDIYAT************************/
					List<HedeflerAidiyatTalepPojo> aidiyatTalepList = Utility.mapBimAidiyat(h.BIM_AIDIYAT_KOD,hedefTalep.getId(),personelIymId);
					for(HedeflerAidiyatTalepPojo bimAidiyat : aidiyatTalepList){
						Long bimAidiyatId = hedeflerBimAidiyatTalepIdAl();
						if(bimAidiyatId == 0L){
							h.hatalar2.add(new XMLHATA("BIM_AIDIYAT_KOD","HATA  Kod : [HATA-2-29] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
							throw new Exception("HATA  Kod : [HATA-2-29] Lütfen BTK ile irtibata geçiniz!");
						}
						bimAidiyat.setId(bimAidiyatId);
						if (!bimAidiyatTalepKaydet(con, bimAidiyat,personelIymId,clientIp,xmlEvrak.islemId)) {
							h.hatalar2.add(new XMLHATA("BIM_AIDIYAT_KOD","HATA  Kod : [HATA-2-30] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
							throw new Exception("HATA  Kod : [HATA-2-30] Lütfen BTK ile irtibata geçiniz!");
						}
					}
					/**********************MAHKEME-HEDEF AIDIYAT YENI*********************/
					List<String> toplamAidiyatList = new ArrayList<String>();
					if(aidiyatTalepList.size()>0)
						 toplamAidiyatList.addAll(h.BIM_AIDIYAT_KOD); /*Bim aidiyatları eklendi*/
					if(aidiyatKodList.size()>0)
						toplamAidiyatList.addAll(aidiyatKodList); /*diger aidiyatlar eklendi*/
					List<MahkemeHedeflerAidiyatTalepPojo> mahkemeHedeflerAidiyatList = Utility.mapMahkemeHedeflerAidiyat(toplamAidiyatList,hedefTalep.getId(),personelIymId,mahkemeKarar.getId());
					for(MahkemeHedeflerAidiyatTalepPojo a : mahkemeHedeflerAidiyatList){
						Long aidiyatId = mahkemeHedeflerAidiyatTalepIdAl();
						if(aidiyatId == 0L){
							mk.MAHKEME_AIDIYAT.hatalar2.add(new XMLHATA("MAHKEME_KARAR","HATA  Kod : [HATA-2-39] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
							throw new Exception("HATA  Kod : [HATA-2-39] Lütfen BTK ile irtibata geçiniz!");
						}
						a.setId(aidiyatId);
						if (!mahkemeHedefAidiyatTalepKaydet(con, a ,personelIymId,clientIp,xmlEvrak.islemId)) {
							mk.MAHKEME_AIDIYAT.hatalar2.add(new XMLHATA("MAHKEME_KARAR","HATA  Kod : [HATA-2-40] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
							throw new Exception("HATA  Kod : [HATA-2-40] Lütfen BTK ile irtibata geçiniz!");
						}
					}
				}
				
			}
			if(!isCanakUpdate) {
				if (!xmlEvrakIslemKaydet(con, evrak, personelIymId, clientIp, xmlEvrak.islemId, kurumKod)) {
					xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT", "HATA  Kod : [HATA-2-38] Lütfen BTK ile irtibata geçiniz!", personelIymId, clientIp, xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-2-38] Lütfen BTK ile irtibata geçiniz!");
				}
			}

			/**************İŞLEMLER BİTTİKTEN SONRA DOSYA İŞLEMLERİ********************/
			
			logger.info("IymService:yeniXmlKayit: ftp islemleri" );
			
			anaKlasorYolu = Utility.anaKlasorYoluBul(xmlYolu);
			anaKlasor = Utility.anaKlasorBul(xmlYolu);
			
			//System.out.println("yeniXmlKayit: 1");
			
		
				/*
				if(Utility.evrakFtple(con,anaKlasorYolu,anaKlasor,evrak.getEvrakSiraNo(),evrak.getId(),personelIymId,clientIp,xmlEvrak.islemId)){  //tiff,jpg vb. evraklarin tasinmasi
					System.out.println("yeniXmlKayit: 2");
					
					if(Utility.gelenEvrakZipFilesTasi(con,anaKlasorYolu,anaKlasor,evrak.getEvrakSiraNo())){	// zip dosyasinin tasinmasi
						if(Utility.xmlEvrakiTempKlasoreTasi(con,anaKlasorYolu,anaKlasor,personelIymId,evrak.getId(),mahkemeKararId,clientIp,xmlEvrak.islemId)){ // tmp klasorune ilgili klasoru ve xmlinmasi
							
							System.out.println("yeniXmlKayit: 3");
							
						}else{
							
							System.out.println("yeniXmlKayit: 4");
							
							xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","Hata Kod :  [HATA-2-31] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
							throw new Exception("Hata Kod :  [HATA-2-31] Lütfen BTK ile irtibata geçiniz!");
						}
					}else{
						
						System.out.println("yeniXmlKayit: 5");
						
						xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","Hata Kod :  [HATA-2-32] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
						throw new Exception("Hata Kod :  [HATA-2-32] Lütfen BTK ile irtibata geçiniz!");
					}
				}else{
					
					logger.info("IymService:yeniXmlKayit: Utility.evrakFtple false" );
					
					//System.out.println("yeniXmlKayit: 6");
					
					xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","Hata Kod :  [HATA-2-33] Lütfen BTK ile irtibata geçiniz!"+ anaKlasor + "/" + anaKlasorYolu,personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("Hata Kod :  [HATA-2-33] Lütfen BTK ile irtibata geçiniz!"+ anaKlasor + "/" + anaKlasorYolu);
				}
				*/
		
			
			/*******************************************************************************/
			xmlEvrak.setEvrakId(evrak.getId());
			con.commit();
		}catch (Exception e) {
			
			logger.info("IymService:yeniXmlKayit:", e);
			
			try {
				con.rollback();
			} catch (SQLException e11) {
			}
			e.printStackTrace();
			try {
				Utility.dosyalariSil(anaKlasorYolu,anaKlasor,evrak.getId(),evrak.getEvrakSiraNo(),personelIymId);
				throw new Exception(e.getMessage());
			} catch (Exception e1) {
				e1.printStackTrace();
			}
		} finally {
			try {
				if (con != null) {
					con.setAutoCommit(true);
					con.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return xmlEvrak;
	}

	public String iliskiliKararBul(EVRAK_KAYIT xmlEvrak, String evrakKurum,
			Long personelIymId, String clientIp, HEDEFLER h,
			MahkemeKararDetayTalepPojo mahkemeKararDetay,
			MAHKEME_KARAR_DETAY dty, String gercekHedefTipi) throws Exception {
		
		String iliskiliMahkemeId = null;
		List<HedeflerTalepPojo> iliskiliKararlar = hedefIliskiliMahkemeIdListGetir(mahkemeKararDetay.getMahkemeIliDetay(),mahkemeKararDetay.getMahkemeKararNoDetay(),mahkemeKararDetay.getMahkemeKoduDetay(),mahkemeKararDetay.getSorusturmaNoDetay(),evrakKurum,h.HEDEF_NO,gercekHedefTipi);
		
		if(iliskiliKararlar.size()>0){/*ilişkili karar var*/
			Long imhaSayisi = 0L;
			Long iliskiKurulabilecekKararSayisi = 0L;
			for(HedeflerTalepPojo t: iliskiliKararlar){
				if(t.getDurumu() != null && t.getDurumu().equalsIgnoreCase("IMHA")){
					imhaSayisi++;
				}else{
					iliskiKurulabilecekKararSayisi++;
					if(iliskiKurulabilecekKararSayisi == 1L){
						if(t.getMahkemeKararId() != null){
							try{
								iliskiliMahkemeId = t.getMahkemeKararId().toString();
							}catch(Exception ex1){
								dty.hatalar2.add(new XMLHATA("MAHKEME_KARAR_DETAY","HATA  Kod : [HATA-2-42] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
								throw new Exception("HATA  Kod : [HATA-2-42] Lütfen BTK ile irtibata geçiniz!");
							}
						}
					}
				}
			}
			
			if(iliskiKurulabilecekKararSayisi > 1){/*Kararların eslesigi birden fazla karar var*/
				dty.hatalar2.add(new XMLHATA("MAHKEME_KARAR_DETAY","HEDEF NO ["+h.HEDEF_NO+"] HEDEFIN DETAY KARARINDA BELİRTİLEN   KARAR NO["+mahkemeKararDetay.getMahkemeKararNoDetay()+"] SOR.NO["+mahkemeKararDetay.getSorusturmaNoDetay()+"] ILI ["+mahkemeKararDetay.getMahkemeIliDetay()+"] MAH.KODU["+mahkemeKararDetay.getMahkemeKoduDetay()+"]  BİLGİLERİ İÇİN BİRDEN FAZLA SONUÇ MAHKEME KARARI SİSTEMDE BULUNMAKTADIR.",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-2-43] Lütfen BTK ile irtibata geçiniz!");
			}
			
			if(imhaSayisi == iliskiliKararlar.size()){/*Kararların tamamı imha olmus*/
				dty.hatalar2.add(new XMLHATA("MAHKEME_KARAR_DETAY","HEDEF NO ["+h.HEDEF_NO+"] HEDEFIN DETAY KARARINDA BELİRTİLEN   KARAR NO["+mahkemeKararDetay.getMahkemeKararNoDetay()+"] SOR.NO["+mahkemeKararDetay.getSorusturmaNoDetay()+"] ILI ["+mahkemeKararDetay.getMahkemeIliDetay()+"] MAH.KODU["+mahkemeKararDetay.getMahkemeKoduDetay()+"]  MAHKEME KARARI İMHA EDİLMİŞ",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-2-44] Lütfen BTK ile irtibata geçiniz!");
			}
			
			if(iliskiKurulabilecekKararSayisi == 0){/*Karar eşleşmiyor*/
				dty.hatalar2.add(new XMLHATA("MAHKEME_KARAR_DETAY","HEDEF NO ["+h.HEDEF_NO+"] HEDEFIN DETAY KARARINDA BELİRTİLEN   KARAR NO["+mahkemeKararDetay.getMahkemeKararNoDetay()+"] SOR.NO["+mahkemeKararDetay.getSorusturmaNoDetay()+"] ILI ["+mahkemeKararDetay.getMahkemeIliDetay()+"] MAH.KODU["+mahkemeKararDetay.getMahkemeKoduDetay()+"]  MAHKEME KARAR SİSTEMDE BULUNAMADI",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-2-41] Lütfen BTK ile irtibata geçiniz!");
			}
		}else{
			dty.hatalar2.add(new XMLHATA("MAHKEME_KARAR_DETAY","HEDEF NO ["+h.HEDEF_NO+"] HEDEFIN DETAY KARARINDA BELİRTİLEN   KARAR NO["+mahkemeKararDetay.getMahkemeKararNoDetay()+"] SOR.NO["+mahkemeKararDetay.getSorusturmaNoDetay()+"] ILI ["+mahkemeKararDetay.getMahkemeIliDetay()+"] MAH.KODU["+mahkemeKararDetay.getMahkemeKoduDetay()+"]  MAHKEME KARAR SİSTEMDE BULUNAMADI",personelIymId,clientIp,xmlEvrak.islemId));
			throw new Exception("HATA  Kod : [HATA-2-41] Lütfen BTK ile irtibata geçiniz!");
		}
		return iliskiliMahkemeId;
	}

	
	public EVRAK_KAYIT yeniGenelEvrakXmlKayit(EVRAK_KAYIT xmlEvrak, String evrakKurum,String evrakBirim, String evrakTipi,Long personelIymId, String xmlYolu, String clientIp){
		Connection con = null;
		String anaKlasorYolu = "";
		String anaKlasor="";
		Long mahkemeKararId = 0L;
		
		XmlEvrakPojo evrak = Utility.mapEvrak(xmlEvrak, personelIymId,evrakBirim,evrakTipi,evrakKurum);

		try {
			if (evrak == null) {
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-11-01] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-11-01] Lütfen BTK ile irtibata geçiniz!");
			}
			con = getDao().getJdbcTemplate().getDataSource().getConnection();
			if (con == null) {
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-11-02] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-11-02] Lütfen BTK ile irtibata geçiniz!");
			}
			con.setAutoCommit(false);
			/*******************EVRAK KAYIT*************************************/
			Long evrakId = evrakIdAl();
			if(evrakId == 0L){
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-11-03] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-11-03] Lütfen BTK ile irtibata geçiniz!");
			}
			evrak.setId(evrakId);
			String evrakSiraNo = evrakSayiAl(evrak.getEvrakTipi(),evrak.getEvrakGeldigiKurum());
			if(evrakSiraNo == null || evrakSiraNo.equals("")){
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-11-04] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-11-04] Lütfen BTK ile irtibata geçiniz!");
			}
			evrak.setEvrakSiraNo(evrakSiraNo);
			evrak.setEvrakYonu(null);
			if(evrak.getAcilMi()==null || evrak.getAcilMi().equals(""))
				evrak.setAcilMi("H");
			evrak.setHavaleBirim("");
			if (!xmlEvrakKaydet(con, evrak,personelIymId,clientIp,xmlEvrak.islemId)) {
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-11-05] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-11-05] Lütfen BTK ile irtibata geçiniz!");
			}
			
			/**************İŞLEMLER BİTTİKTEN SONRA DOSYA İŞLEMLERİ********************/
			anaKlasorYolu = Utility.anaKlasorYoluBul(xmlYolu);
			anaKlasor = Utility.anaKlasorBul(xmlYolu);
			if(Utility.evrakFtple(con,anaKlasorYolu,anaKlasor,evrak.getEvrakSiraNo(),evrak.getId(),personelIymId,clientIp,xmlEvrak.islemId)){
				if(Utility.gelenEvrakZipFilesTasi(con,anaKlasorYolu,anaKlasor,evrak.getEvrakSiraNo())){
					if(Utility.xmlEvrakiTempKlasoreTasi(con,anaKlasorYolu,anaKlasor,personelIymId,evrak.getId(),mahkemeKararId,clientIp,xmlEvrak.islemId)){
						
					}else{
						xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","Hata Kod :  [HATA-11-13] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
						throw new Exception("Hata Kod :  [HATA-11-13] Lütfen BTK ile irtibata geçiniz!");
					}
				}else{
					xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","Hata Kod :  [HATA-11-14] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("Hata Kod :  [HATA-11-14] Lütfen BTK ile irtibata geçiniz!");
				}
			}else{
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","Hata Kod :  [HATA-11-15] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("Hata Kod :  [HATA-11-15] Lütfen BTK ile irtibata geçiniz!");
			}
			/*******************************************************************************/
			xmlEvrak.setEvrakId(evrak.getId());
			con.commit();
		}catch (Exception e) {
			try {
				con.rollback();
			} catch (SQLException e11) {
			}
			e.printStackTrace();
			try {
				Utility.dosyalariSil(anaKlasorYolu,anaKlasor,evrak.getId(),evrak.getEvrakSiraNo(),personelIymId);
				throw new Exception(e.getMessage());
			} catch (Exception e1) {
				e1.printStackTrace();
			}
		} finally {
			try {
				if (con != null) {
					con.setAutoCommit(true);
					con.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return xmlEvrak;
	}
			
	public EVRAK_KAYIT yeniHtsXmlKayit(EVRAK_KAYIT xmlEvrak, String evrakKurum,String evrakBirim, String evrakTipi,Long personelIymId, String xmlYolu, String clientIp){
		Connection con = null;
		String anaKlasorYolu = "";
		String anaKlasor="";
		Long mahkemeKararId = 0L;
		
		XmlEvrakPojo evrak = Utility.mapEvrakHts(xmlEvrak, personelIymId,evrakBirim,evrakTipi,evrakKurum);

		try {
			if (evrak == null) {
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-10-01] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-10-01] Lütfen BTK ile irtibata geçiniz!");
			}
			con = getDao().getJdbcTemplate().getDataSource().getConnection();
			if (con == null) {
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-10-02] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-10-02] Lütfen BTK ile irtibata geçiniz!");
			}
			con.setAutoCommit(false);
			/*******************EVRAK KAYIT*************************************/
			Long evrakId = evrakIdAl();
			if(evrakId == 0L){
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-10-03] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-10-03] Lütfen BTK ile irtibata geçiniz!");
			}
			evrak.setId(evrakId);
			if(evrak.getAcilMi()==null || evrak.getAcilMi().equals(""))
				evrak.setAcilMi("H");
			evrak.setHavaleBirim("");
			String evrakSiraNo = evrakSayiAl(evrak.getEvrakTipi(),evrak.getEvrakGeldigiKurum());
			if(evrakSiraNo == null || evrakSiraNo.equals("")){
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-10-04] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-10-04] Lütfen BTK ile irtibata geçiniz!");
			}
			evrak.setEvrakSiraNo(evrakSiraNo);
			evrak.setEvrakYonu(EVRAK_TIPLERI.ILETISIMIN_TESPITI.getTip());
			if (!xmlEvrakKaydet(con, evrak,personelIymId,clientIp,xmlEvrak.islemId)) {
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-10-05] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-10-05] Lütfen BTK ile irtibata geçiniz!");
			}
			for(MAHKEME_KARAR mk:xmlEvrak.MAHKEME_KARAR){
				/***********MAHKEME KARAR TALEP ****************************/
				HtsMahkemeKararTalepPojo mahkemeKarar = Utility.mapMahkemeKararHts(mk,evrak.getId(),personelIymId);
				String mahkemeAdi = mahkemeAdiGetir(mahkemeKarar.getMahkemeKodu());
				if(mahkemeAdi == null || mahkemeAdi.equals("")){
					mk.hatalar2.add(new XMLHATA("MAHKEME_KODU"," MAHKEME KARARDAKİ ["+mahkemeKarar.getMahkemeKodu()+"] MAHKEME KODU SİSTEMDE YOK!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-10-06] Lütfen BTK ile irtibata geçiniz!");
				}
				mahkemeKarar.setMahkemeAdi(mahkemeAdi);
				mahkemeKarar.setHukukBirim("HUKUK-TALEP");
			    mahkemeKararId = htsMahkemeKararTalepIdAl();
				if(mahkemeKararId == 0L){
					mk.hatalar2.add(new XMLHATA("MAHKEME_KARAR","HATA  Kod : [HATA-10-07] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-10-07] Lütfen BTK ile irtibata geçiniz!");
				}
				mahkemeKarar.setId(mahkemeKararId);
				if (!htsMahkemeKararTalepKaydet(con, mahkemeKarar,personelIymId,clientIp,xmlEvrak.islemId)) {
					mk.hatalar2.add(new XMLHATA("MAHKEME_KARAR","HATA  Kod : [HATA-10-08] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-10-08] Lütfen BTK ile irtibata geçiniz!");
				}
				/***********************HEDEFLER***********************************/
				for(ITK_HEDEFLER h : mk.ITK_HEDEFLER){
					/***********************HEDEFLER***********************************/
					HtsHedeflerTalepPojo hedefTalep = Utility.mapHtsHedefler(h, mahkemeKarar.getId(), personelIymId);
	
					Long hedefId = htsHedeflerTalepIdAl();
					if(hedefId == 0L){
						h.hatalar2.add(new XMLHATA("HEDEFLER","HATA  Kod : [HATA-10-11] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
						throw new Exception("HATA  Kod : [HATA-10-11] Lütfen BTK ile irtibata geçiniz!");
					}
					hedefTalep.setId(hedefId);
					if (!htsHedeflerTalepKaydet(con, hedefTalep,personelIymId,clientIp,xmlEvrak.islemId)) {
						h.hatalar2.add(new XMLHATA("HEDEFLER","HATA  Kod : [HATA-10-12] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
						throw new Exception("HATA  Kod : [HATA-10-12] Lütfen BTK ile irtibata geçiniz!");
					}
				}
				
			}
			/**************İŞLEMLER BİTTİKTEN SONRA DOSYA İŞLEMLERİ********************/
			anaKlasorYolu = Utility.anaKlasorYoluBul(xmlYolu);
			anaKlasor = Utility.anaKlasorBul(xmlYolu);
			if(Utility.evrakFtple(con,anaKlasorYolu,anaKlasor,evrak.getEvrakSiraNo(),evrak.getId(),personelIymId,clientIp,xmlEvrak.islemId)){
				if(Utility.gelenEvrakZipFilesTasi(con,anaKlasorYolu,anaKlasor,evrak.getEvrakSiraNo())){
					if(Utility.xmlEvrakiTempKlasoreTasi(con,anaKlasorYolu,anaKlasor,personelIymId,evrak.getId(),mahkemeKararId,clientIp,xmlEvrak.islemId)){
						
					}else{
						xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","Hata Kod :  [HATA-10-13] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
						throw new Exception("Hata Kod :  [HATA-10-13] Lütfen BTK ile irtibata geçiniz!");
					}
				}else{
					xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","Hata Kod :  [HATA-10-14] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("Hata Kod :  [HATA-10-14] Lütfen BTK ile irtibata geçiniz!");
				}
			}else{
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","Hata Kod :  [HATA-10-15] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("Hata Kod :  [HATA-10-15] Lütfen BTK ile irtibata geçiniz!");
			}
			/*******************************************************************************/
			xmlEvrak.setEvrakId(evrak.getId());
			con.commit();
		}catch (Exception e) {
			try {
				con.rollback();
			} catch (SQLException e11) {
			}
			e.printStackTrace();
			try {
				Utility.dosyalariSil(anaKlasorYolu,anaKlasor,evrak.getId(),evrak.getEvrakSiraNo(),personelIymId);
				throw new Exception(e.getMessage());
			} catch (Exception e1) {
				e1.printStackTrace();
			}
		} finally {
			try {
				if (con != null) {
					con.setAutoCommit(true);
					con.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return xmlEvrak;
	}
	
	public String evrakTasnifeGonder(Connection con,Long evrakId,Long personelIymId,String kullaniciGorev,String aciklama) {
		boolean hata = false;
		CallableStatement cs = null;
		String procedure = "{call EVRAK_ONAYLA_YENI(?,?,?,?,?)}";
		String sonuc = null;

		
		
		try {
			/*Kurumlara ait kullanicilarin görevleri yok*/
			if(kullaniciGorev == null || kullaniciGorev.equalsIgnoreCase("") )
				kullaniciGorev = "0";
			
			if(aciklama == null || aciklama.equalsIgnoreCase(""))
				aciklama = " ";
			
			//System.out.println("ONAYLA TEST GOREV : "+kullaniciGorev+ " PERSONEL : "+personelIymId+" EVRAKID : "+evrakId+" ACIKLAMA :"+aciklama);
			
			cs = (CallableStatement) con.prepareCall(procedure);
			cs.setLong(1, evrakId );
			cs.setLong(2, personelIymId );
			cs.setLong(3, new Long(kullaniciGorev) );
			cs.setString(4, aciklama);
			cs.registerOutParameter(5, Types.VARCHAR);
			cs.execute();
			sonuc = cs.getString(5);

	     } catch (Exception e) {
			try {
				con.rollback();
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			e.printStackTrace();
			sonuc = null;
		} finally {
			if (cs != null) {
				try {
					cs.close();
				} catch (Exception e) {
				}
			}
		}
		return sonuc;
	}

	public ArrayList<XMLHATA> genelEvrakOnayla(EVRAK_KAYIT xmlEvrak,Long evrakId, long personelIymId, String clientIp,String gorev) {
		Connection con = null;
		XmlEvrakPojo evrak = null;
		try {
			evrak = evrakGetir(evrakId,personelIymId);
			if(evrak == null){
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-13-01] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-13-01] Lütfen BTK ile irtibata geçiniz!");
			}
			con = getDao().getJdbcTemplate().getDataSource().getConnection();
			if (con == null) {
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-13-02] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-13-02] Lütfen BTK ile irtibata geçiniz!");
			}
			con.setAutoCommit(false);
			boolean sonuc = evrakOnayla(con,evrak.getId(),personelIymId,clientIp,xmlEvrak.islemId);
			if(!sonuc){
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-13-03] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-13-03] Lütfen BTK ile irtibata geçiniz!");
			}
			if(!evrakTasnifeGonder(con, evrakId, personelIymId, gorev,xmlEvrak.ACIKLAMA).equalsIgnoreCase("1")){
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-13-04] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-13-04] Lütfen BTK ile irtibata geçiniz!");
			}
			con.commit();
		}catch (Exception e) {
			try {
				con.rollback();
			} catch (SQLException e11) {
			}
			e.printStackTrace();
			try {
				throw new Exception(e.getMessage());
			} catch (Exception e1) {
				e1.printStackTrace();
			}
		} finally {
			try {
				if (con != null) {
					con.setAutoCommit(true);
					con.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return xmlEvrak.hatalar2;
				
	}
	
	public ArrayList<XMLHATA> htsEvrakOnayla(EVRAK_KAYIT xmlEvrak,Long evrakId, long personelIymId, String clientIp, String gorev) {
		Connection con = null;
		XmlEvrakPojo evrak = null;
		try {
			evrak = evrakGetir(evrakId,personelIymId);
			if(evrak == null){
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-12-01] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-12-01] Lütfen BTK ile irtibata geçiniz!");
			}
		
			con = getDao().getJdbcTemplate().getDataSource().getConnection();
			if (con == null) {
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-12-02] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-12-02] Lütfen BTK ile irtibata geçiniz!");
			}
			con.setAutoCommit(false);
			boolean sonuc = evrakElektronikTeslimYap(con,evrak.getId(),personelIymId,clientIp,xmlEvrak.islemId);
			if(!sonuc){
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-12-03] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-12-03] Lütfen BTK ile irtibata geçiniz!");
			}
		    sonuc = evrakOnayla(con,evrak.getId(),personelIymId,clientIp,xmlEvrak.islemId);
			if(!sonuc){
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-12-04] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-12-03] Lütfen BTK ile irtibata geçiniz!");
			}
			List<MahkemeKararTalepPojo> mahkemeKararList = mahkemeKararTalepGetir(evrak.getId(),personelIymId);
			for(MahkemeKararTalepPojo mahkemeKararTalep : mahkemeKararList){
				sonuc = htsMahkemeKararTalepOnayla(con,mahkemeKararTalep.getId(),personelIymId,clientIp,xmlEvrak.islemId);
				if(!sonuc){
					xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-12-05] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-12-05] Lütfen BTK ile irtibata geçiniz!");
				}
				
				sonuc = htsHedeflerTalepOnayla(con,mahkemeKararTalep.getId(),personelIymId,clientIp,xmlEvrak.islemId);
				if(!sonuc){
					xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-12-06] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-12-06] Lütfen BTK ile irtibata geçiniz!");
				}
			}
			if(!evrakTasnifeGonder(con, evrakId, personelIymId, gorev,xmlEvrak.ACIKLAMA).equalsIgnoreCase("1")){
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-12-07] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-12-07] Lütfen BTK ile irtibata geçiniz!");
			}
			con.commit();
		}catch (Exception e) {
			try {
				con.rollback();
			} catch (SQLException e11) {
			}
			e.printStackTrace();
			try {
				throw new Exception(e.getMessage());
			} catch (Exception e1) {
				e1.printStackTrace();
			}
		} finally {
			try {
				if (con != null) {
					con.setAutoCommit(true);
					con.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return xmlEvrak.hatalar2;
				
	}

	
	public ArrayList<XMLHATA> xmlOnayla(EVRAK_KAYIT xmlEvrak,Long evrakId, Long personelIymId, String clientIp) {
		Connection con = null;
		XmlEvrakPojo evrak = null;
		try {
			evrak = evrakGetir(evrakId,personelIymId);			
			if(evrak == null){
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-4-01] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-4-01] Lütfen BTK ile irtibata geçiniz!");
			}
			
			System.out.println("--------------------------1-------------------------"+evrak.getAciklama() + " durum: " + evrak.getDurumu());
			
			if(evrak != null && evrak.getAciklama()!=null && evrak.getAciklama().contains("İADE")){
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-4-01-1] BTK'dan iade edilen evraklar tekrar onaylanamaz. Evrak silinip, iade açıklaması dikkate alınarak tekrar gönderilebilir.",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-4-01-01] BTK'dan iade edilen evraklar tekrar onaylanamaz. Evrak silinip, iade açıklaması dikkate alınarak tekrar gönderilebilir.");
			}
			
			if(evrak != null && evrak.getDurumu() != null  && evrak.getDurumu().equals("KISMI_IADE")){
				//xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-4-01-2] BTK'dan iade edilen evraklar tekrar onaylanamaz. Evrak silinip, iade açıklaması dikkate alınarak tekrar gönderilebilir.",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-4-01-02] BTK'dan kismi iade edilen evraklar tekrar onaylanamaz. Evrak silinip, kısmi iade açıklaması dikkate alınarak tekrar gönderilebilir.");
			}
			
			System.out.println("--------------------------2-------------------------");
			
			con = getDao().getJdbcTemplate().getDataSource().getConnection();
			if (con == null) {
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-4-02] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-4-02] Lütfen BTK ile irtibata geçiniz!");
			}
			con.setAutoCommit(false);
			boolean sonuc = evrakOnayla(con,evrak.getId(),personelIymId,clientIp,xmlEvrak.islemId);
			if(!sonuc){
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-4-03] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
				throw new Exception("HATA  Kod : [HATA-4-03] Lütfen BTK ile irtibata geçiniz!");
			}
			List<MahkemeKararTalepPojo> mahkemeKararList = mahkemeKararTalepGetir(evrak.getId(),personelIymId);
			for(MahkemeKararTalepPojo mahkemeKararTalep : mahkemeKararList){
				sonuc = mahkemeKararTalepOnayla(con,mahkemeKararTalep.getId(),personelIymId,clientIp,xmlEvrak.islemId);
				if(!sonuc){
					xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-4-04] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-4-04] Lütfen BTK ile irtibata geçiniz!");
				}
				
				sonuc = mahkemeSuclarTalepOnayla(con,mahkemeKararTalep.getId(),personelIymId,clientIp,xmlEvrak.islemId);
				if(!sonuc){
					xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-4-05] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-4-05] Lütfen BTK ile irtibata geçiniz!");
				}
				
				sonuc = mahkemeAidiyatDetayTalepOnayla(con,mahkemeKararTalep.getId(),personelIymId,clientIp,xmlEvrak.islemId);
				if(!sonuc){
					xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-4-06] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-4-06] Lütfen BTK ile irtibata geçiniz!");
				}
				
				sonuc = mahkemeAidiyatTalepOnayla(con,mahkemeKararTalep.getId(),personelIymId,clientIp);
				if(!sonuc){
					xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-4-07] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-4-07] Lütfen BTK ile irtibata geçiniz!");
				}
				
				sonuc = mahkemeKararDetayTalepOnayla(con,mahkemeKararTalep.getId(),personelIymId,clientIp,xmlEvrak.islemId);
				if(!sonuc){
					xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-4-08] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-4-08] Lütfen BTK ile irtibata geçiniz!");
				}
				
				sonuc = hedeflerTalepOnayla(con,mahkemeKararTalep.getId(),personelIymId,clientIp,xmlEvrak.islemId);
				if(!sonuc){
					xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-4-09] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-4-09] Lütfen BTK ile irtibata geçiniz!");
				}
				
				sonuc = hedeflerAidiyatTalepOnayla(con,mahkemeKararTalep.getId(),personelIymId,clientIp,xmlEvrak.islemId);
				if(!sonuc){
					xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-4-10] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-4-10] Lütfen BTK ile irtibata geçiniz!");
				}
			
				sonuc = hedeflerDetayTalepOnayla(con,mahkemeKararTalep.getId(),personelIymId,clientIp,xmlEvrak.islemId);
				if(!sonuc){
					xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATA  Kod : [HATA-4-11] Lütfen BTK ile irtibata geçiniz!",personelIymId,clientIp,xmlEvrak.islemId));
					throw new Exception("HATA  Kod : [HATA-4-11] Lütfen BTK ile irtibata geçiniz!");
				}

				if(mahkemeKararTalep.getKararTip() != null && mahkemeKararTalep.getKararTip().equals(MAHKEME_KARAR_TIPLERI.HEDEF_CANAK_DEGISTIRME.getKararKodu())){

				}

			}
			con.commit();
		}catch (Exception e) {
			try {
				con.rollback();
			} catch (SQLException e11) {
			}
			e.printStackTrace();
			try {
				throw new Exception(e.getMessage());
			} catch (Exception e1) {
				e1.printStackTrace();
			}
		} finally {
			try {
				if (con != null) {
					con.setAutoCommit(true);
					con.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return xmlEvrak.hatalar2;
	}

	

	private boolean hedeflerDetayTalepOnayla(Connection con, Long mahkemeKararTalepId,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;

		String sql = "Update HEDEFLER_DETAY_TALEP set durumu='ONAYLANDI' where MAHKEME_KARAR_ID=?";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Hedefler Detay Talep Onayla", "HEDEFLER_DETAY_TALEP", mahkemeKararTalepId.toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, mahkemeKararTalepId);
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}

	private boolean hedeflerAidiyatTalepOnayla(Connection con, Long mahkemeKararTalepId,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;

		String sql = "Update HEDEFLER_AIDIYAT_TALEP set durumu='ONAYLANDI' where HEDEF_ID in ( SELECT ID FROM HEDEFLER_TALEP t WHERE t.MAHKEME_KARAR_ID = ?) AND KULLANICI_ID=?";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Hedefler Aidiyat Talep Onayla", "HEDEFLER_AIDIYAT_TALEP", mahkemeKararTalepId.toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, mahkemeKararTalepId);
			st.setLong(2, personelId);
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}

	private boolean hedeflerTalepOnayla(Connection con, Long mahkemeKararTalepId,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;

		String sql = "Update HEDEFLER_TALEP set durumu='ONAYLANDI' where MAHKEME_KARAR_ID=? AND KULLANICI_ID=?";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Hedefler Talep Onayla", "HEDEFLER_TALEP", mahkemeKararTalepId.toString(),islemId);
			
			st = con.prepareStatement(sql);
			st.setLong(1, mahkemeKararTalepId);
			st.setLong(2, personelId);
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}

	private boolean htsHedeflerTalepOnayla(Connection con, Long mahkemeKararTalepId,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;

		String sql = "Update HTS_HEDEFLER_TALEP set durumu='ONAYLANDI' where MAHKEME_KARAR_ID=? AND KULLANICI_ID=?";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Hts Hedefler Talep Onayla", "ITK_HEDEFLER", mahkemeKararTalepId.toString(),islemId);
			
			st = con.prepareStatement(sql);
			st.setLong(1, mahkemeKararTalepId);
			st.setLong(2, personelId);
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}
	
	private boolean mahkemeKararDetayTalepOnayla(Connection con, Long mahkemeKararTalepId,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;

		String sql = "Update DMAHKEME_KARAR_TALEP set durum='ONAYLANDI' where MAHKEME_KARAR_ID=? AND KULLANICI_ID=?";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Detay Mahkeme Karar Talep Onayla", "DMAHKEME_KARAR_TALEP", mahkemeKararTalepId.toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, mahkemeKararTalepId);
			st.setLong(2, personelId);
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}

	private boolean mahkemeAidiyatTalepOnayla(Connection con, Long mahkemeKararTalepId,Long kullaniciId,String clientIp) {
		boolean sonuc = false;
		PreparedStatement st = null;

		String sql = "Update MAHKEME_AIDIYAT_TALEP set durumu='ONAYLANDI' where MAHKEME_ID=?";
		try {
			st = con.prepareStatement(sql);
			st.setLong(1, mahkemeKararTalepId);
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}

	private boolean mahkemeAidiyatDetayTalepOnayla(Connection con, Long mahkemeKararTalepId,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;

		String sql = "Update MAHKEME_AIDIYAT_DETAY_TALEP set durum='ONAYLANDI' where MAHKEME_KARAR_ID=?";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Mahkeme Aidiyat Detay Talep Onayla", "MAHKEME_AIDIYAT_DETAY_TALEP", mahkemeKararTalepId.toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, mahkemeKararTalepId);
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}

	private boolean mahkemeSuclarTalepOnayla(Connection con, Long mahkemeKararTalepId,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;

		String sql = "Update MAHKEME_SUCLAR_TALEP set durumu='ONAYLANDI' where MAHKEME_KARAR_ID=?";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Mahkeme Suclar Talep Onayla", "MAHKEME_SUCLAR_TALEP", mahkemeKararTalepId.toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, mahkemeKararTalepId);
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}

	private boolean mahkemeKararTalepOnayla(Connection con, Long mahkemeKararTalepId,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;

		String sql = "Update MAHKEME_KARAR_TALEP set durum='ONAYLANDI' where ID=?  and kullanici_id = ?";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Mahkeme Karar Talep Onayla", "MAHKEME_KARAR_TALEP", mahkemeKararTalepId.toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, mahkemeKararTalepId);
			st.setLong(2, personelId);
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}

	
	private boolean htsMahkemeKararTalepOnayla(Connection con, Long mahkemeKararTalepId,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;

		String sql = "Update HTS_MAHKEME_KARAR_TALEP set durum='ONAYLANDI' where ID=?  and kullanici_id = ?";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Hts Mahkeme Karar Talep Onayla", "MAHKEME_KARAR_TALEP", mahkemeKararTalepId.toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, mahkemeKararTalepId);
			st.setLong(2, personelId);
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}

	
	private boolean evrakElektronikTeslimYap(Connection con, Long evrakId, Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;

		String sql = "Update EVRAK_KAYIT set elden_teslim='T'  where id=?  and kay_kullanici = ?";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Evrak Elden Teslim Yap", "EVRAK_KAYIT", evrakId.toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, evrakId);
			st.setLong(2, personelId);
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}
	
	private boolean evrakOnayla(Connection con, Long evrakId, Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;

		String sql = "Update EVRAK_KAYIT set durumu='ONAYLANDI',onay_tarihi=sysdate  where id=?  and kay_kullanici = ? and durumu is null";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Evrak Kayıt Onayla", "EVRAK_KAYIT", evrakId.toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, evrakId);
			st.setLong(2, personelId);
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}

	public class XmlEvrakRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			XmlEvrakPojo evrak = new XmlEvrakPojo();
			evrak.setId(rs.getLong("id"));
			evrak.setEvrakSiraNo(rs.getString("evrak_sira_no"));
			evrak.setEvrakNo(rs.getString("evrak_no"));
			evrak.setGirisTarih(rs.getString("giris_tarih"));
			evrak.setEvrakTarihi(rs.getString("evrak_tarihi"));
			evrak.setEvrakGeldigiKurum(rs.getString("evrak_geldigi_kurum"));
			evrak.setKayitEdenKullanici(rs.getLong("kay_kullanici"));
			evrak.setGelenIl(rs.getString("gel_il"));
			evrak.setEvrakKonusu(rs.getString("evrak_konusu"));
			evrak.setAciklama(rs.getString("aciklama"));
			evrak.setDurumu(rs.getString("DURUMU"));
			evrak.setAcilMi(rs.getString("acilmi"));
			return evrak;

		}
	}
	public XmlEvrakPojo evrakGetir(Long evrakId,Long kayitEdenKullanici) {	
		String sql = "SELECT e.ID, "
				+ "       e.evrak_sira_no,"
				+ "       e.evrak_no,"
				+ "       e.giris_tarih,"
				+ "       e.evrak_tarihi," 
				+ "       e.evrak_geldigi_kurum," 
				+ "       e.kay_kullanici," 
				+ "       e.gel_il," 
				+ "       e.evrak_konusu," 
				+ "       e.aciklama," 
				+ "       e.durumu," 
				+ "       e.acilmi "
				+ "  FROM evrak_kayit e"
				+ " WHERE     e.id = ? " +
				"         AND e.kay_kullanici = ?";
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(sql);
		tjdbc.setData(new Object[] { evrakId , kayitEdenKullanici});
		tjdbc.setType(new int[] { Types.BIGINT , Types.BIGINT});
		tjdbc.setRowMapper(new XmlEvrakRowMapper());
		return (XmlEvrakPojo) getDao().bulRowMapper(tjdbc).get(0);
	}

	public class MahkemeKararTalepRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			MahkemeKararTalepPojo mahkemeKarar = new MahkemeKararTalepPojo();
			mahkemeKarar.setId(rs.getLong("ID"));
			mahkemeKarar.setEvrakId(rs.getLong("EVRAK_ID"));
			mahkemeKarar.setKullaniciId(rs.getLong("KULLANICI_ID"));
			mahkemeKarar.setKayitTarihi(rs.getString("KAYIT_TARIHI"));
			mahkemeKarar.setDurum(rs.getString("DURUM"));
			mahkemeKarar.setHukukBirim(rs.getString("HUKUK_BIRIM"));
			mahkemeKarar.setKararTip(rs.getString("KARAR_TIP"));
			mahkemeKarar.setMahkemeKararBaslamaTarihi(rs.getString("MAH_KARAR_BAS_TAR"));
			mahkemeKarar.setMahkemeKararBitisTarihi(rs.getString("MAH_KARAR_BITIS_TAR"));
			mahkemeKarar.setMahkemeAdi(rs.getString("MAHKEME_ADI"));
			mahkemeKarar.setMahkemeKararNo(rs.getString("MAHKEME_KARAR_NO"));
			mahkemeKarar.setMahkemeIli(rs.getString("MAHKEME_ILI"));
			mahkemeKarar.setAciklama(rs.getString("ACIKLAMA"));
			mahkemeKarar.setSorusturmaNo(rs.getString("SORUSTURMA_NO"));
			mahkemeKarar.setMahkemeKodu(rs.getString("MAHKEME_KODU"));
			return mahkemeKarar;

		}
	}
	
	public List<MahkemeKararTalepPojo> mahkemeKararTalepGetir(Long evrakId,Long kullaniciId) {
		String sql = " SELECT  ID," +
					" EVRAK_ID," +
					" KULLANICI_ID," +
					" KAYIT_TARIHI," +
					" DURUM," +
					" HUKUK_BIRIM," +
					" KARAR_TIP," +
					" MAH_KARAR_BAS_TAR," +
					" MAH_KARAR_BITIS_TAR," +
					" MAHKEME_ADI," +
					" MAHKEME_KARAR_NO," +
					" MAHKEME_ILI," +
					" ACIKLAMA," +
					" SORUSTURMA_NO, " +
					" MAHKEME_KODU "
				+ "  FROM MAHKEME_KARAR_TALEP m"
				+ " WHERE     m.EVRAK_ID = ? " +
				"         AND m.KULLANICI_ID = ? ";


		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(sql);
		tjdbc.setData(new Object[] { evrakId,kullaniciId });
		tjdbc.setType(new int[] { Types.BIGINT,Types.BIGINT});
		tjdbc.setRowMapper(new MahkemeKararTalepRowMapper());
		return (List<MahkemeKararTalepPojo>) getDao().bulRowMapper(tjdbc);
	}
	
	public MahkemeKararTalepPojo mahkemeKararTalepGetir(Long mahkemeKararId) {
		String sql = " SELECT  ID," +
					" EVRAK_ID," +
					" KULLANICI_ID," +
					" KAYIT_TARIHI," +
					" DURUM," +
					" HUKUK_BIRIM," +
					" KARAR_TIP," +
					" MAH_KARAR_BAS_TAR," +
					" MAH_KARAR_BITIS_TAR," +
					" MAHKEME_ADI," +
					" MAHKEME_KARAR_NO," +
					" MAHKEME_ILI," +
					" ACIKLAMA," +
					" SORUSTURMA_NO, " +
					" MAHKEME_KODU "
				+ "  FROM MAHKEME_KARAR_TALEP m"
				+ " WHERE     m.ID = ? ";


		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(sql);
		tjdbc.setData(new Object[] { mahkemeKararId});
		tjdbc.setType(new int[] { Types.BIGINT});
		tjdbc.setRowMapper(new MahkemeKararTalepRowMapper());
		return (MahkemeKararTalepPojo) getDao().bulRowMapper(tjdbc);
	}
	
	public class MahkemeAidiyatTalepRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			MahkemeAidiyatTalepPojo aidiyat = new MahkemeAidiyatTalepPojo();
			aidiyat.setId(rs.getLong("ID"));
			aidiyat.setMahkemeId(rs.getLong("MAHKEME_ID"));
			aidiyat.setAidiyatKod(rs.getString("AIDIYAT_KOD"));
			aidiyat.setDurumu(rs.getString("DURUMU"));
			return aidiyat;

		}
	}
	
	private List<MahkemeAidiyatTalepPojo> mahkemeAidiyatTalepGetir(Long mahkemeKararTalepId) {
		String sql = " SELECT  ID," +
				"  MAHKEME_ID, " +
				"  AIDIYAT_KOD, " +
				"  DURUMU "
				+ "  FROM MAHKEME_AIDIYAT_TALEP m"
				+ " WHERE     m.EVRAK_ID = ? ";


		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(sql);
		tjdbc.setData(new Object[] { mahkemeKararTalepId });
		tjdbc.setType(new int[] { Types.BIGINT});
		tjdbc.setRowMapper(new MahkemeAidiyatTalepRowMapper());
		return (List<MahkemeAidiyatTalepPojo>) getDao().bulRowMapper(tjdbc);
	}
	
	
	public class MahkemeSuclarTalepRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			MahkemeSuclarTalepPojo suclar = new MahkemeSuclarTalepPojo();
			suclar.setId(rs.getLong("ID"));
			suclar.setMahkemeKararId(rs.getLong("MAHKEME_KARAR_ID"));
			suclar.setMahkemeSucTipKod(rs.getString("MAHKEME_SUC_TIP_KOD"));
			suclar.setDurumu(rs.getString("DURUMU"));
			return suclar;

		}
	}
	
	private List<MahkemeSuclarTalepPojo> mahkemeSuclarTalepGetir(Long mahkemeKararId) {
		String sql = " SELECT  ID, " +
				" MAHKEME_KARAR_ID, " +
				" MAHKEME_SUC_TIP_KOD," +
				" DURUMU "
				+ "  FROM MAHKEME_SUCLAR_TALEP m"
				+ " WHERE     m. = ? ";


		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(sql);
		tjdbc.setData(new Object[] { mahkemeKararId });
		tjdbc.setType(new int[] { Types.BIGINT});
		tjdbc.setRowMapper(new MahkemeSuclarTalepRowMapper());
		return (List<MahkemeSuclarTalepPojo>) getDao().bulRowMapper(tjdbc);
	}
	
	public String hedefIliskiliMahkemeIdGetir(String mahkemeIliDetay,String mahkemeKararNoDetay, String mahkemeKoduDetay,String sorusturmaNoDetay,String evrakGelenKurum,String hedefNo,String hedefTipi) {
		String mahkemeKararId  = null;
		if(sorusturmaNoDetay==null || sorusturmaNoDetay.equals(""))
				mahkemeKararId = (String) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						" SELECT max(m.id) as id FROM iym.mahkeme_karar m,hedefler h " +
						" WHERE " +
						" m.mahkeme_ili = ? AND " +
						" m.mahkeme_karar_no = ? AND "+
						" m.karar_tip not in ('500','510','520','530','540','550') AND " +
						" m.mahkeme_kodu = ? AND " +
						" m.evrak_id in (SELECT K.ID FROM EVRAK_KAYIT K WHERE K.EVRAK_GELDIGI_KURUM = ?) AND "+
						" h.mahkeme_karar_id = m.id AND "+
						" h.hedef_no = ? AND "+
						" h.hedef_tipi = ? AND h.IMHA is null",
						new Object[] { mahkemeIliDetay, mahkemeKararNoDetay, mahkemeKoduDetay,evrakGelenKurum,hedefNo,hedefTipi },
						new int[] { Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR}, 
						String.class);
		else
			mahkemeKararId = (String) ((TemelDAO) getDao())
			.getJdbcTemplate()
			.queryForObject(
					" SELECT max(m.id) as id FROM iym.mahkeme_karar m,hedefler h " +
					" WHERE " +
					" m.mahkeme_ili = ? AND " +
					" m.mahkeme_karar_no = ? AND "+
					" m.karar_tip not in ('500','510','520','530','540','550') AND " +
					" m.mahkeme_kodu = ? AND "+
					" m.sorusturma_no = ? AND "+
					" m.evrak_id in (SELECT K.ID FROM EVRAK_KAYIT K WHERE K.EVRAK_GELDIGI_KURUM = ?) AND "+
					" h.mahkeme_karar_id = m.id AND "+
					" h.hedef_no = ? AND "+
					" h.hedef_tipi = ? AND h.IMHA is null",
					new Object[] { mahkemeIliDetay, mahkemeKararNoDetay, mahkemeKoduDetay, sorusturmaNoDetay,evrakGelenKurum,hedefNo,hedefTipi  },
					new int[] { Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR}, 
					String.class);
		return mahkemeKararId;
	}

	public Long mahkemeKararIdGetir(String mahkemeIliDetay,String mahkemeKararNoDetay, String mahkemeKoduDetay,String sorusturmaNoDetay,String evrakGelenKurum) {
		Long mahkemeKararId  = null;
		if(sorusturmaNoDetay==null || sorusturmaNoDetay.equals(""))
			mahkemeKararId = (Long) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							" SELECT max(m.id) as id FROM iym.mahkeme_karar m,hedefler h " +
									" WHERE " +
									" m.mahkeme_ili = ? AND " +
									" m.mahkeme_karar_no = ? AND "+
									" m.karar_tip not in ('500','510','520','530','540','550') AND " +
									" m.mahkeme_kodu = ? AND " +
									" m.evrak_id in (SELECT K.ID FROM EVRAK_KAYIT K WHERE K.EVRAK_GELDIGI_KURUM = ?) ",
							new Object[] { mahkemeIliDetay, mahkemeKararNoDetay, mahkemeKoduDetay,evrakGelenKurum},
							new int[] { Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR},
							Long.class);
		else
			mahkemeKararId = (Long) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							" SELECT max(m.id) as id FROM iym.mahkeme_karar m " +
									" WHERE " +
									" m.mahkeme_ili = ? AND " +
									" m.mahkeme_karar_no = ? AND "+
									" m.karar_tip not in ('500','510','520','530','540','550') AND " +
									" m.mahkeme_kodu = ? AND "+
									" m.sorusturma_no = ? AND "+
									" m.evrak_id in (SELECT K.ID FROM EVRAK_KAYIT K WHERE K.EVRAK_GELDIGI_KURUM = ?) ",
							new Object[] { mahkemeIliDetay, mahkemeKararNoDetay, mahkemeKoduDetay, sorusturmaNoDetay,evrakGelenKurum  },
							new int[] { Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR},
							Long.class);
		return mahkemeKararId;
	}


	public class HedefIliskiliKararRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			HedeflerTalepPojo hedefKarar = new HedeflerTalepPojo();
			hedefKarar.setMahkemeKararId(rs.getLong("ID"));
			hedefKarar.setDurumu(rs.getString("DURUMU"));
			return hedefKarar;

		}
	}
	
	public List<HedeflerTalepPojo>  hedefIliskiliMahkemeIdListGetir(String mahkemeIliDetay,String mahkemeKararNoDetay, String mahkemeKoduDetay,String sorusturmaNoDetay,String evrakGelenKurum,String hedefNo,String hedefTipi) {
		List<HedeflerTalepPojo> hedefIliskiliKararList  = null;
		TemelJDBC tjdbc = new TemelJDBC();
		String sql = " SELECT m.id,h.imha as durumu FROM iym.mahkeme_karar m,hedefler h " +
					" WHERE " +
					" m.mahkeme_ili = ? AND " +
					" m.mahkeme_karar_no = ? AND "+
					" m.mahkeme_kodu = ? AND "+
					" m.evrak_id in (SELECT K.ID FROM EVRAK_KAYIT K WHERE K.EVRAK_GELDIGI_KURUM = ?) AND "+
					" h.mahkeme_karar_id = m.id AND "+
					" h.hedef_no = ? AND "+
					" h.hedef_tipi = ? ";
		if(sorusturmaNoDetay==null || sorusturmaNoDetay.equals("")){
			tjdbc.setStatement(sql);
			tjdbc.setData(new Object[] { mahkemeIliDetay, mahkemeKararNoDetay, mahkemeKoduDetay,evrakGelenKurum,hedefNo,hedefTipi});
			tjdbc.setType(new int[] {  Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR});
		}else{
			sql += " AND  m.sorusturma_no = ? ";
			tjdbc.setStatement(sql);
			tjdbc.setData(new Object[] { mahkemeIliDetay, mahkemeKararNoDetay, mahkemeKoduDetay,evrakGelenKurum,hedefNo,hedefTipi,sorusturmaNoDetay });
			tjdbc.setType(new int[] {  Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR});
		}
		tjdbc.setRowMapper(new HedefIliskiliKararRowMapper());
		hedefIliskiliKararList = (List<HedeflerTalepPojo>) getDao().bulRowMapper(tjdbc);
		return hedefIliskiliKararList;
	}
	
	public List<String> iliskiliMahkemeIdGetir(String mahkemeIliDetay, String mahkemeKoduDetay,String sorusturmaNoDetay,String evrakGelenKurum) {
		String mahkemeKararId  = null;
		List<String> listMahkemeKarar;
		
		if(sorusturmaNoDetay==null || sorusturmaNoDetay.equals(""))
			listMahkemeKarar =  ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForList(
						" SELECT mahkeme_karar_no FROM iym.mahkeme_karar m " +
						" WHERE " +
						" m.mahkeme_ili = ? AND " +
						" m.mahkeme_kodu = ? AND "+
						" m.karar_tip not in ('500','510','520','530','540','550') AND " +
						" m.evrak_id in (SELECT K.ID FROM EVRAK_KAYIT K WHERE K.EVRAK_GELDIGI_KURUM = ?) ",
						new Object[] { mahkemeIliDetay, mahkemeKoduDetay,evrakGelenKurum },
						new int[] { Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR}, 
						String.class);
		else
			listMahkemeKarar = ((TemelDAO) getDao())
			.getJdbcTemplate()
			.queryForList(
					" SELECT mahkeme_karar_no FROM iym.mahkeme_karar m" +
					" WHERE " +
					" m.mahkeme_ili = ? AND " +
					" m.karar_tip not in ('500','510','520','530','540','550') AND " +
					" m.mahkeme_kodu = ? AND "+
					" m.sorusturma_no = ? AND "+
					" m.evrak_id in (SELECT K.ID FROM EVRAK_KAYIT K WHERE K.EVRAK_GELDIGI_KURUM = ?) ",
					new Object[] { mahkemeIliDetay, mahkemeKoduDetay, sorusturmaNoDetay,evrakGelenKurum },
					new int[] { Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR}, 
					String.class);
		
		return listMahkemeKarar;
	}

	public String iliskiliMahkemeIdGetir(String mahkemeIliDetay,String mahkemeKararNoDetay, String mahkemeKoduDetay,String sorusturmaNoDetay,String evrakGelenKurum) {
		String mahkemeKararId  = null;
		if(sorusturmaNoDetay==null || sorusturmaNoDetay.equals(""))
				mahkemeKararId = (String) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						" SELECT max(m.id) as id FROM iym.mahkeme_karar m " +
						" WHERE " +
						" m.mahkeme_ili = ? AND " +
						" m.mahkeme_karar_no = ? AND "+
						" m.mahkeme_kodu = ? AND " +
						" m.karar_tip not in ('500','510','520','530','540','550') AND " +
						" m.evrak_id in (SELECT K.ID FROM EVRAK_KAYIT K WHERE K.EVRAK_GELDIGI_KURUM = ?) ",
						new Object[] { mahkemeIliDetay, mahkemeKararNoDetay, mahkemeKoduDetay,evrakGelenKurum },
						new int[] { Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR}, 
						String.class);
		else
			mahkemeKararId = (String) ((TemelDAO) getDao())
			.getJdbcTemplate()
			.queryForObject(
					" SELECT max(m.id) as id FROM iym.mahkeme_karar m" +
					" WHERE " +
					" m.mahkeme_ili = ? AND " +
					" m.mahkeme_karar_no = ? AND "+
					" m.mahkeme_kodu = ? AND "+
					" m.sorusturma_no = ? AND "+
					" m.karar_tip not in ('500','510','520','530','540','550') AND " +
					" m.evrak_id in (SELECT K.ID FROM EVRAK_KAYIT K WHERE K.EVRAK_GELDIGI_KURUM = ?) ",
					new Object[] { mahkemeIliDetay, mahkemeKararNoDetay, mahkemeKoduDetay, sorusturmaNoDetay,evrakGelenKurum },
					new int[] { Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR}, 
					String.class);
		return mahkemeKararId;
	}

	public Long hedefMahkemeKararKontrol(String iliskiliMahkemeId,String hedefNo,String evrakGelenKurum,String gercekHedefTipi,String sorusturmaNo) {
		Long sayi  = null;
		
		if(sorusturmaNo==null || sorusturmaNo.equals("")){
			sayi = (Long) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							" SELECT COUNT(*) " +
							"  FROM HEDEFLER T, MAHKEME_KARAR K "+
							"  WHERE K.ID = ? "+
							"	 	 AND T.MAHKEME_KARAR_ID = K.ID "+
							"		 AND t.hedef_no = ? "+
							"		 AND t.hedef_tipi = ? "+
							" 		 AND K.EVRAK_ID IN "+
							" (SELECT E.ID FROM EVRAK_KAYIT E WHERE E.EVRAK_GELDIGI_KURUM IN (?))",
							new Object[] { iliskiliMahkemeId, hedefNo, gercekHedefTipi,evrakGelenKurum},
							new int[] { Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR}, 
							Long.class);
		}else{
			sayi = (Long) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							" SELECT COUNT(*) " +
							"  FROM HEDEFLER T, MAHKEME_KARAR K "+
							"  WHERE K.ID = ? "+
							"	 	 AND T.MAHKEME_KARAR_ID = K.ID "+
							"        AND K.SORUSTURMA_NO = ? "+
							"		 AND t.hedef_no = ? "+
							"		 AND t.hedef_tipi = ? "+
							" 		 AND K.EVRAK_ID IN "+
							" (SELECT E.ID FROM EVRAK_KAYIT E WHERE E.EVRAK_GELDIGI_KURUM IN (?))",
							new Object[] { iliskiliMahkemeId,sorusturmaNo, hedefNo, gercekHedefTipi,evrakGelenKurum},
							new int[] { Types.VARCHAR,Types.VARCHAR ,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR}, 
							Long.class);
		}
		return sayi;
	}

	

	public Long hedefSonMahkemeKarariIleUzatilmisMiKontrol(String iliskiliMahkemeId, HedeflerTalepPojo hedefTalep,String evrakKurum, MahkemeKararDetayTalepPojo mahkemeKararDetay,Long kullaniciId) {
		Long sayi  = null;
		if(mahkemeKararDetay.getSorusturmaNoDetay()==null || mahkemeKararDetay.getSorusturmaNoDetay().equals(""))
			sayi = (Long) ((TemelDAO) getDao())
			.getJdbcTemplate()
			.queryForObject(
					" SELECT COUNT(*) FROM mahkeme_karar gmk " +
							" WHERE gmk.id = ( "+
							"			SELECT max(h.mahkeme_karar_id) "+
							"			  FROM HEDEFLER H "+ 
							"			 WHERE H.HEDEF_NO = ? "+
							"			   AND H.HEDEF_TIPI = ? "+ 
							"			   AND H.IMHA IS NULL "+ 
							"			   AND H.KAPATMA_KARAR_ID IS NULL "+ 
							"			   AND H.MAHKEME_KARAR_ID IN "+
							"			       (SELECT MK.ID "+
							"			          FROM MAHKEME_KARAR MK "+
							"			         WHERE MK.EVRAK_ID IN "+
							"			               (SELECT K.ID "+
							"			                  FROM EVRAK_KAYIT K "+
							"			                 WHERE K.EVRAK_GELDIGI_KURUM = ? AND k.KAY_KULLANICI = ? )" +
							"			    		AND mk.mahkeme_karar_no = ? "+ 
							"			    		AND mk.mahkeme_ili = ? "+
							"			    		AND mk.mahkeme_kodu = ? "+
							"					)" +
							"				) "+
							"			    AND gmk.id = ? ",
							new Object[] { hedefTalep.getHedefNo(), hedefTalep.getHedefTipi(),evrakKurum,kullaniciId,
											mahkemeKararDetay.getMahkemeKararNoDetay(),mahkemeKararDetay.getMahkemeIliDetay(),
											mahkemeKararDetay.getMahkemeKoduDetay(),iliskiliMahkemeId},
					new int[] { Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.BIGINT,Types.VARCHAR,
							 Types.VARCHAR,Types.VARCHAR,Types.VARCHAR}, 
					Long.class);
		else
			sayi = (Long) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							" SELECT COUNT(*) FROM mahkeme_karar gmk " +
							" WHERE gmk.id = ( "+
							"			SELECT max(h.mahkeme_karar_id) "+
							"			  FROM HEDEFLER H "+ 
							"			 WHERE H.HEDEF_NO = ? "+
							"			   AND H.HEDEF_TIPI = ? "+ 
							"			   AND H.IMHA IS NULL "+ 
							"			   AND H.KAPATMA_KARAR_ID IS NULL "+ 
							"			   AND H.MAHKEME_KARAR_ID IN "+
							"			       (SELECT MK.ID "+
							"			          FROM MAHKEME_KARAR MK "+
							"			         WHERE MK.EVRAK_ID IN "+
							"			               (SELECT K.ID "+
							"			                  FROM EVRAK_KAYIT K "+
							"			                 WHERE K.EVRAK_GELDIGI_KURUM = ? AND k.KAY_KULLANICI = ? )" +
							"			    		AND mk.mahkeme_karar_no = ? "+ 
							"			    		AND mk.mahkeme_ili = ? "+
							"			    		AND mk.mahkeme_kodu = ? "+
							"			    		AND mk.sorusturma_no = ? "+
							"					)" +
							"				) "+
							"			    AND gmk.id = ? ",
							new Object[] { hedefTalep.getHedefNo(), hedefTalep.getHedefTipi(),evrakKurum,kullaniciId,
											mahkemeKararDetay.getMahkemeKararNoDetay(),mahkemeKararDetay.getMahkemeIliDetay(),
											mahkemeKararDetay.getMahkemeKoduDetay(),mahkemeKararDetay.getSorusturmaNoDetay(),
											iliskiliMahkemeId},
							new int[] { Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.BIGINT,Types.VARCHAR,
									 Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.VARCHAR}, 
							Long.class);
		return sayi;
	}
	
	
	public Long iliskiliHedefGetir(Long mahkemeKararId,String hedefNo,String hedefTipi) {
			Long iliskiliHedefId = (Long) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						" SELECT h.id as id FROM hedefler h " +
						" WHERE " +
						" h.hedef_no = ? AND "+
						" h.hedef_tipi = ?  AND " +
						" h.mahkeme_karar_id = ? ",
						new Object[] {hedefNo,hedefTipi,mahkemeKararId },
						new int[] { Types.VARCHAR,Types.VARCHAR,Types.BIGINT}, 
						Long.class);
	
		return iliskiliHedefId;
	}
	
	
	public Long ilKontrol(String ilKodu) {
		Long adet =  (Long) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						" SELECT count(*) as sayi FROM iller i where i.il_kod = ?  ",
						new Object[] { ilKodu },
						new int[] { Types.VARCHAR}, 
						Long.class);
		return adet;
	}
	
	public Long mukerrerEvrakKontrol(String evrakNo,String gelenKurum) {
		Long adet =  (Long) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						" SELECT COUNT(*) as sayi FROM evrak_kayit k WHERE k.evrak_no = ? AND nvl(k.durumu,'X') != 'SILINDI' and nvl(k.evrak_yonu,'X')='ILETISIMIN_DENETLENMESI' AND k.evrak_geldigi_kurum = ? ",
						new Object[] { evrakNo,gelenKurum  },
						new int[] { Types.VARCHAR, Types.VARCHAR}, 
						Long.class);
		System.out.println(evrakNo+" +"+gelenKurum );
		
		return adet;
	}
	
	private Long hedeflerBimAidiyatTalepIdAl() {
		Long hedeflerAidiyatTalepId = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
				.queryForLong("select HEDEFLER_AIDIYAT_TALEP_SEQ.nextval from dual",
						new Object[] {}, new int[] {});

		return hedeflerAidiyatTalepId;
	}

	private Long hedeflerTalepIdAl() {
		Long hedeflerTalepId = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
				.queryForLong("select HEDEFLER_TALEP_SEQ.nextval from dual",
						new Object[] {}, new int[] {});

		return hedeflerTalepId;
	}
	
	private Long htsHedeflerTalepIdAl() {
		Long hedeflerTalepId = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
				.queryForLong("select HTS_HEDEFLER_TALEP_SEQ.nextval from dual",
						new Object[] {}, new int[] {});

		return hedeflerTalepId;
	}
	
	private Long hedeflerDetayTalepIdAl() {
		Long hedeflerDetayTalepId = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
				.queryForLong("select HEDEFLER_DETAY_TALEP_SEQ.nextval from dual",
						new Object[] {}, new int[] {});

		return hedeflerDetayTalepId;
	}
	private Long mahkemeAidiyatDetayTalepIdAl() {
		Long mahkemeAidiyatTalepDetayId = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
				.queryForLong("select MAHKEME_AIDIYAT_DETAY_TLP_SEQ.nextval from dual",
						new Object[] {}, new int[] {});

		return mahkemeAidiyatTalepDetayId;
	}
	
	private Long mahkemeKararDetayTalepIdAl() {
		Long mahkemeKararTalepDetayId = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
				.queryForLong("select DMAHKEME_KARAR_TALEP_SEQ.nextval from dual",
						new Object[] {}, new int[] {});

		return mahkemeKararTalepDetayId;
	}

	private Long mahkemeKararTalepIdAl() {
		Long mahkemeKararTalepId = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
				.queryForLong("select MAHKEME_KARAR_TALEP_SEQ.nextval from dual",
						new Object[] {}, new int[] {});

		return mahkemeKararTalepId;
	}

	private Long htsMahkemeKararTalepIdAl() {
		Long mahkemeKararTalepId = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
				.queryForLong("select HTS_MAHKEME_KARAR_TALEP_SEQ.nextval from dual",
						new Object[] {}, new int[] {});

		return mahkemeKararTalepId;
	}
	
	
	private Long evrakIdAl() {
		Long yaziId = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
				.queryForLong("select EVRAK_KAYIT_SEQ.nextval from dual",
						new Object[] {}, new int[] {});

		return yaziId;
	}
	private Long mahkemeAidiyatTalepIdAl() {
		Long mahkemeAidiyatId = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
				.queryForLong("select MAHKEME_AIDIYAT_TALEP_SEQ.nextval from dual",
						new Object[] {}, new int[] {});

		return mahkemeAidiyatId;
	}
	
	private Long mahkemeHedeflerAidiyatTalepIdAl() {
		Long mahkemeHedefAidiyatId = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
				.queryForLong("select MAHKEME_HEDEF_AIDIYAT_TLP_SEQ.nextval from dual",
						new Object[] {}, new int[] {});

		return mahkemeHedefAidiyatId;
	}
	
	private Long mahkemeSuclarTalepIdAl() {
		Long mahkemeSuclarId = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
				.queryForLong("select MAHKEME_SUCLAR_TALEP_SEQ.nextval from dual",
						new Object[] {}, new int[] {});

		return mahkemeSuclarId;
	}
	
	public boolean dogruXmlLogKaydet(Connection con,String hedefKlasorYolu, Long personelId,Long evrakId,Long mahkemeKararTalepId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;
		

		String sql = "insert into dogru_xml_log  (ID, kullanici_id,mahkeme_karar_talep_id, tarih, xml_konum,evrak_id)"
				+ "VALUES(dogru_xml_log_seq.nextval,?,?,sysdate,?,?) ";
		try {
			if (con == null) {
				throw new Exception(
						"HATA  Kod : [HATA-8-01] Lütfen BTK ile irtibata geçiniz!");
			}
			Utility.xmlIslemLog(personelId, clientIp, "Dogru Xml Log Kaydet", "DOGRU_XML_LOG", null,islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, personelId);
			st.setLong(2, mahkemeKararTalepId);
			st.setString(3, hedefKlasorYolu);
			st.setLong(4, evrakId);
			st.executeUpdate();
			sonuc = true;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}
	
	public boolean arsivXmlDosyaKaydet(String hedefKlasorYolu, Long kullaniciId) {

		boolean sonuc = false;
		PreparedStatement st = null;

		String sql = "insert into ARSIV_XML_ISLEM_DOSYALARI  (ID, kullanici_id, tarih, xml_konum)"
				+ "VALUES(arsiv_xml_islem_dosya_seq.nextval,?,sysdate,?) ";
		try {
			Connection con = getDao().getJdbcTemplate().getDataSource()
					.getConnection();
			if (con == null) {
				throw new Exception(
						"HATA  Kod : [HATA-5-01] Lütfen BTK ile irtibata geçiniz!");
			}
			st = con.prepareStatement(sql);
			st.setLong(1, kullaniciId);
			st.setString(2, hedefKlasorYolu);
			st.executeUpdate();
			con.commit();
			sonuc = true;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;

	}
	
	public boolean evrakFilesKaydet(Connection con,String fileName, Long evrakId, Long siraNo,Long personelId,String clientIp,Long islemId) {

		boolean sonuc = false;
		PreparedStatement st = null;

		String sql = "insert into evrak_files  (ID, file_name, evrak_id,sira_no,silindi) "
				+ "VALUES(evrak_files_seq.nextval,?,?,?,?) ";
		try {
			if (con == null) {
				throw new Exception(
						"HATA  Kod : [HATA-9-01] Lütfen BTK ile irtibata geçiniz!");
			}
			Utility.xmlIslemLog(personelId, clientIp, "Evrak Files Kaydet", "EVRAK_FILES", null,islemId);
			st = con.prepareStatement(sql);
			st.setString(1, fileName);
			st.setLong(2, evrakId);
			st.setLong(3, siraNo);
			st.setLong(4, 0L);
			st.executeUpdate();
			sonuc = true;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;

	}
	
	private boolean bimAidiyatTalepKaydet(Connection con,HedeflerAidiyatTalepPojo bimAidiyat,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;
		
			String sql = "INSERT INTO HEDEFLER_AIDIYAT_TALEP " +
				 " (ID,HEDEF_ID, AIDIYAT_KOD, TARIH, "+
				 "	KULLANICI_ID,DURUMU) VALUES " +
				" (?,?,?,SYSDATE,?," +
				"  ?)";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Hedefler Aidiyat Talep Kaydet", "HEDEFLER_AIDIYAT_TALEP", bimAidiyat.getId().toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, bimAidiyat.getId());
			st.setLong(2, bimAidiyat.getHedefId());
			st.setString(3, bimAidiyat.getAidiyatKod());
			st.setLong(4, bimAidiyat.getKullaniciId());
			st.setString(5, bimAidiyat.getDurumu());
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}
	
	private boolean mahkemeHedefAidiyatTalepKaydet(Connection con,MahkemeHedeflerAidiyatTalepPojo aidiyat,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;
		
		String sql = "INSERT INTO MAHKEME_HEDEFLER_AIDIYAT_TALEP " +
				 " (ID,HEDEF_ID, AIDIYAT_KOD, TARIH, MAHKEME_KARAR_ID,"+
				 "	KULLANICI_ID,DURUMU) VALUES " +
				" (?,?,?,SYSDATE,?,?," +
				"  ?)";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Mahkeme Hedefler Aidiyat Talep Kaydet", "MAHKEME_HEDEFLER_AIDIYAT_TALEP", aidiyat.getId().toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, aidiyat.getId());
			st.setLong(2, aidiyat.getHedefId());
			st.setString(3, aidiyat.getAidiyatKod());
			st.setLong(4, aidiyat.getMahkemeKararId());
			st.setLong(5,aidiyat.getKullaniciId());
			st.setString(6, aidiyat.getDurumu());
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}
	
	private boolean hedeflerTalepKaydet(Connection con,HedeflerTalepPojo hedefTalep,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;
		
		String sql = "INSERT INTO HEDEFLER_TALEP " +
				 " (ID,BIRIM_KOD,KULLANICI_ID, "+
				 "	HEDEF_NO,HEDEF_TIPI,HEDEF_ADI,HEDEF_SOYADI, "+
				 "  BASLAMA_TARIHI,SURESI,SURE_TIPI,UZATMA_SAYISI," +
				 "  DURUMU,ACIKLAMA,MAHKEME_KARAR_ID," +
				 "	GRUP_KOD,AIDIYAT_KOD,KAPATMA_KARAR_ID,UZATMA_ID," +
				 "  HEDEF_118_ADI,HEDEF_118_SOYADI,HEDEF_118_ADRES, CANAK_NO) VALUES " +
				" (?,?,?," +
				"  ?,?,?,?," +
				"  to_date(?,'dd.mm.yyyy'),?,?,?," +
				"  ?,?,?," +
				"  ?,?,?,?," +
				"  ?,?,?,?)";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Hedefler Talep Kaydet", "HEDEFLER_TALEP", hedefTalep.getId().toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, hedefTalep.getId());
			st.setString(2, hedefTalep.getBirimKod());
			st.setLong(3, hedefTalep.getKullaniciId());
			st.setString(4, hedefTalep.getHedefNo());
			st.setString(5, hedefTalep.getHedefTipi());
			st.setString(6, hedefTalep.getHedefAdi());
			st.setString(7, hedefTalep.getHedefSoyadi());
			st.setString(8, hedefTalep.getBaslamaTarihi());
			st.setString(9, hedefTalep.getSuresi());
			st.setString(10, hedefTalep.getSureTipi());
			st.setString(11, hedefTalep.getUzatmaSayisi());
			st.setString(12, hedefTalep.getDurumu());
			st.setString(13, hedefTalep.getAciklama());
			st.setLong(14, hedefTalep.getMahkemeKararId());
			st.setString(15, hedefTalep.getGrupKod());
			st.setString(16, hedefTalep.getAidiyatKod());
			st.setString(17, hedefTalep.getKapatmaKararId());
			st.setString(18, hedefTalep.getUzatmaId());
			st.setString(19, hedefTalep.getHedef118Adi());
			st.setString(20, hedefTalep.getHedef118Soyadi());
			st.setString(21, hedefTalep.getHedef118Adres());
			st.setString(22, hedefTalep.getCanakNo());
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}
	
	private boolean htsHedeflerTalepKaydet(Connection con,HtsHedeflerTalepPojo hedefTalep,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;
		
		String sql = "INSERT INTO HTS_HEDEFLER_TALEP " +
				 " (ID,KULLANICI_ID, MAHKEME_KARAR_ID,"+
				 "	HEDEF_NO,KARSI_HEDEF_NO,SORGU_TIPI, "+
				 "  BASLANGIC_TARIHI,BITIS_TARIHI,TESPIT_TURU) VALUES " +
				" (?,?,?," +
				"  ?,?,?,"+ 
				"  to_date(?,'dd.mm.yyyy hh24:mi:ss'),to_date(?,'dd.mm.yyyy hh24:mi:ss'),?)";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "HTS Hedefler Talep Kaydet", "HTS_HEDEFLER_TALEP", hedefTalep.getId().toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, hedefTalep.getId());
			st.setLong(2, hedefTalep.getKullaniciId());
			st.setLong(3, hedefTalep.getMahkemeKararId());
			st.setString(4, hedefTalep.getHedefNo());
			st.setString(5, hedefTalep.getKarsiHedefNo());
			st.setString(6, hedefTalep.getSorguTipi());
			st.setString(7, hedefTalep.getBaslangicTarihi());
			st.setString(8, hedefTalep.getBitisTarihi());
			st.setString(9, hedefTalep.getTespitTuru());
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}
	
	
	private boolean hedeflerDetayTalepKaydet(Connection con,HedeflerDetayTalepPojo hedefDetay,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;


		
		String sql = "INSERT INTO HEDEFLER_DETAY_TALEP " +
				 " (ID,HEDEF_NO, HEDEF_TIPI, HEDEF_ADI, "+
				 "	HEDEF_SOYADI,KAYIT_TARIHI,DURUMU,MAHKEME_KARAR_ID, "+
				 " MAHKEME_KARAR_DETAY_ID,ILISKILI_HEDEF_ID,CANAK_NO) VALUES " +
				" (?,?,?,?," +
				"  ?,SYSDATE,?,?," +
				"  ?,?,?)";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Hedefler Detay Talep Kaydet", "HEDEFLER_DETAY_TALEP", hedefDetay.getId().toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, hedefDetay.getId());
			st.setString(2, hedefDetay.getHedefNo());
			st.setString(3, hedefDetay.getHedefTipi());
			st.setString(4, hedefDetay.getHedefAdi());
			st.setString(5, hedefDetay.getHedefSoyadi());
			st.setString(6, hedefDetay.getDurumu());
			st.setLong(7, hedefDetay.getMahkemeKararId());
			st.setLong(8, hedefDetay.getMahkemeKararDetayId());
			st.setLong(9, hedefDetay.getIliskiliHedefId());
			st.setString(10, hedefDetay.getCanakNo());
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}

	
	private boolean mahkemeAidiyatDetayTalepKaydet(Connection con,MahkemeAidiyatDetayTalepPojo aidiyatDetay,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;
		
		String sql = "INSERT INTO MAHKEME_AIDIYAT_DETAY_TALEP " +
				" (ID,ILISKILI_MAHKEME_KARAR_ID,MAHKEME_KARAR_ID,MAHKEME_AIDIYAT_KODU_EKLE," +
				"  MAHKEME_AIDIYAT_KODU_CIKAR,TARIH, DURUM, MAHKEME_KARAR_DETAY_ID) VALUES " +
				" (?,?,?,?," +
				"  ?,SYSDATE,?,?)";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Mahkeme Aidiyat Detay Talep Kaydet", "MAHKEME_AIDIYAT_DETAY_TALEP", aidiyatDetay.getId().toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, aidiyatDetay.getId());
			st.setLong(2, aidiyatDetay.getIliskiliMahkemeKararId());
			st.setLong(3, aidiyatDetay.getMahkemeKararId());
			st.setString(4, aidiyatDetay.getMahkemeAidiyatKoduEkle());
			st.setString(5, aidiyatDetay.getMahkemeAidiyatKoduCikar());
			st.setString(6, aidiyatDetay.getDurum());
			st.setLong(7, aidiyatDetay.getMahkemeKararDetayId());
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}

	
	private boolean mahkemeKararDetayTalepKaydet(Connection con,MahkemeKararDetayTalepPojo mahkemeKararDetay,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;
		
		String sql = "INSERT INTO DMAHKEME_KARAR_TALEP " +
				" (ID,MAHKEME_KARAR_ID,EVRAK_ID,KULLANICI_ID," +
				" KAYIT_TARIHI,DURUM,MAHKEME_KODU_DETAY,MAHKEME_KARAR_NO_DETAY," +
				" MAHKEME_ILI_DETAY,SORUSTURMA_NO_DETAY,MAHKEME_ADI_DETAY,ILISKILI_MAHKEME_KARAR_ID) VALUES " +
				" (?,?,?,?," +
				"  SYSDATE,?,?,?," +
				"   ?,?,?,nvl(?,''))";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Detay Mahkeme Karar Talep Kaydet", "DMAHKEME_KARAR_TALEP", mahkemeKararDetay.getId().toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, mahkemeKararDetay.getId());
			st.setLong(2, mahkemeKararDetay.getMahkemeKararId());
			st.setLong(3, mahkemeKararDetay.getEvrakId());
			st.setLong(4, mahkemeKararDetay.getKullaniciId());
			st.setString(5, mahkemeKararDetay.getDurum());
			st.setString(6, mahkemeKararDetay.getMahkemeKoduDetay());
			st.setString(7, mahkemeKararDetay.getMahkemeKararNoDetay());
			st.setString(8, mahkemeKararDetay.getMahkemeIliDetay());
			st.setString(9, mahkemeKararDetay.getSorusturmaNoDetay());
			st.setString(10, mahkemeKararDetay.getMahkemeAdiDetay());
			st.setLong(11, mahkemeKararDetay.getIliskiliMahkemeKararId());
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}
	
	private boolean mahkemeSuclarTalepKaydet(Connection con,MahkemeSuclarTalepPojo suc,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;
		
		String sql = "INSERT INTO IYM.MAHKEME_SUCLAR_TALEP " +
				" (ID,MAHKEME_KARAR_ID,MAHKEME_SUC_TIP_KOD) VALUES " +
				" (?,?,?)";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Mahkeme Suclar Talep Kaydet", "MAHKEME_SUCLAR_TALEP", suc.getId().toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, suc.getId());
			st.setLong(2, suc.getMahkemeKararId());
			st.setString(3, suc.getMahkemeSucTipKod());
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}
	
	private boolean mahkemeAidiyatTalepKaydet(Connection con,MahkemeAidiyatTalepPojo aidiyat,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;
		
		String sql = "INSERT INTO IYM.MAHKEME_AIDIYAT_TALEP " +
				" (ID,MAHKEME_ID,AIDIYAT_KOD) VALUES " +
				" (?,?,?)";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Mahkeme Aidiyat Talep Kaydet", "MAHKEME_AIDIYAT_TALEP", aidiyat.getId().toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, aidiyat.getId());
			st.setLong(2, aidiyat.getMahkemeId());
			st.setString(3, aidiyat.getAidiyatKod());
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}
	private boolean mahkemeKararTalepKaydet(Connection con,MahkemeKararTalepPojo mahkemeKarar,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;
		
		String sql = "INSERT INTO IYM.MAHKEME_KARAR_TALEP " +
				" (ID,EVRAK_ID,KULLANICI_ID,KAYIT_TARIHI,DURUM," +
				"  HUKUK_BIRIM,KARAR_TIP,MAH_KARAR_BAS_TAR,MAH_KARAR_BITIS_TAR," +
				" MAHKEME_ADI,MAHKEME_KARAR_NO,MAHKEME_ILI,ACIKLAMA," +
				" SORUSTURMA_NO,GERCEK_MAH_ID,MAHKEME_KODU) VALUES " +
				" (?,?,?,SYSDATE,?," +
				"  ?,?,to_date(?,'dd.mm.yyyy'),to_date(?,'dd.mm.yyyy')," +
				"  ?,?,?,?," +
				"  ?,?,?)";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "Mahkeme Karar Talep Kaydet", "MAHKEME_KARAR_TALEP", mahkemeKarar.getId().toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, mahkemeKarar.getId());
			st.setLong(2, mahkemeKarar.getEvrakId());
			st.setLong(3,mahkemeKarar.getKullaniciId());
			st.setString(4, mahkemeKarar.getDurum());
			st.setString(5, mahkemeKarar.getHukukBirim());
			st.setString(6, mahkemeKarar.getKararTip());
			st.setString(7, mahkemeKarar.getMahkemeKararBaslamaTarihi());
			st.setString(8, mahkemeKarar.getMahkemeKararBitisTarihi());
			st.setString(9, mahkemeKarar.getMahkemeAdi());
			st.setString(10, mahkemeKarar.getMahkemeKararNo());
			st.setString(11, mahkemeKarar.getMahkemeIli());
			st.setString(12, mahkemeKarar.getAciklama());
			st.setString(13, mahkemeKarar.getSorusturmaNo());
			st.setString(14, mahkemeKarar.getGercekMahId());
			st.setString(15, mahkemeKarar.getMahkemeKodu());
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}

	private boolean htsMahkemeKararTalepKaydet(Connection con,HtsMahkemeKararTalepPojo mahkemeKarar,Long personelId,String clientIp,Long islemId) {
		boolean sonuc = false;
		PreparedStatement st = null;
		
		String sql = "INSERT INTO IYM.HTS_MAHKEME_KARAR_TALEP " +
				" (ID,EVRAK_ID,KULLANICI_ID,KAYIT_TARIHI,DURUM," +
				"  HUKUK_BIRIM,KARAR_TIP," +
				" MAHKEME_ADI,MAHKEME_KARAR_NO,MAHKEME_ILI,ACIKLAMA," +
				" SORUSTURMA_NO,MAHKEME_KODU) VALUES " +
				" (?,?,?,SYSDATE,?," +
				"  ?,?,?,?," +
				"  ?,?,?,?)";
		try {
			Utility.xmlIslemLog(personelId, clientIp, "HTS Mahkeme Karar Talep Kaydet", "HTS_MAHKEME_KARAR_TALEP", mahkemeKarar.getId().toString(),islemId);
			st = con.prepareStatement(sql);
			st.setLong(1, mahkemeKarar.getId());
			st.setLong(2, mahkemeKarar.getEvrakId());
			st.setLong(3,mahkemeKarar.getKullaniciId());
			st.setString(4, mahkemeKarar.getDurum());
			st.setString(5, mahkemeKarar.getHukukBirim());
			st.setString(6, mahkemeKarar.getKararTip());
			st.setString(7, mahkemeKarar.getMahkemeAdi());
			st.setString(8, mahkemeKarar.getMahkemeKararNo());
			st.setString(9, mahkemeKarar.getMahkemeIli());
			st.setString(10, mahkemeKarar.getAciklama());
			st.setString(11, mahkemeKarar.getSorusturmaNo());
			st.setString(12, mahkemeKarar.getMahkemeKodu());
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
	}
	
	private boolean xmlEvrakIslemKaydet(Connection con, XmlEvrakPojo evrak,Long personelId,String clientIp,Long islemId,String kurumKod) {
		boolean sonuc = false;
		PreparedStatement st = null;
		
		String sql = "insert into iym.evrak_mahkeme_karar_islem "
				+ "(EVRAK_ID, KURUM, SEVIYE) values"
				+ " (?,?,'0')";

		try {
			st = con.prepareStatement(sql);
			st.setLong(1, evrak.getId());
			st.setString(2, kurumKod);
			st.executeUpdate();
			sonuc = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			if (st != null) {
				try {
					st.close();
				} catch (SQLException e) {
				}
			}
		}
		return sonuc;
		}
	
	private boolean xmlEvrakKaydet(Connection con, XmlEvrakPojo evrak,Long personelId,String clientIp,Long islemId) {
	boolean sonuc = false;
	PreparedStatement st = null;
	
	String sql = "insert into iym.evrak_kayit "
			+ "(ID, EVRAK_SIRA_NO, EVRAK_NO, GIRIS_TARIH, EVRAK_TARIHI, "
			+ "EVRAK_GELDIGI_KURUM, KAY_KULLANICI, ACIKLAMA,	 "
			+ "GEL_IL, EVRAK_KONUSU, ACILMI,HAVALE_BIRIM,MAHKEME_KARAR_NO,SORUSTURMA_NO,EVRAK_YONU,UNIQ_COL,EVRAK_TIPI) values"
			+ " (?,?,?,sysdate,to_date(?,'dd.mm.yyyy'),?,?, ?,?,?,?,?, ?,?,?,?,?)";

	try {
		//System.out.println(" ****************** EVRAK DURUMU :" evra);
		Utility.xmlIslemLog(personelId, clientIp, "Evrak Kaydet", "EVRAK_KAYIT", evrak.getId().toString(),islemId);
		st = con.prepareStatement(sql);
		st.setLong(1, evrak.getId());
		st.setString(2, evrak.getEvrakSiraNo());
		st.setString(3, evrak.getEvrakNo());
		st.setString(4, evrak.getEvrakTarihi());
		st.setString(5, evrak.getEvrakGeldigiKurum());
		st.setLong(6, evrak.getKayitEdenKullanici());
		st.setString(7, evrak.getAciklama());
		st.setString(8, evrak.getGelenIl());
		st.setString(9, evrak.getEvrakKonusu());
		st.setString(10, evrak.getAcilMi());
		st.setString(11, evrak.getHavaleBirim());
		st.setString(12, evrak.getMahkemeKararNo());
		st.setString(13, evrak.getSorusturmaNo());
		st.setString(14, evrak.getEvrakYonu());
		st.setString(15, evrak.getUniqCol());
		st.setString(16, evrak.getEvrakTipi());
		st.executeUpdate();
		sonuc = true;
	} catch (SQLException e) {
		e.printStackTrace();
	} finally {
		if (st != null) {
			try {
				st.close();
			} catch (SQLException e) {
			}
		}
	}
	return sonuc;
	}
	
	public class EvrakKayitRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			XmlEvrakPojo evrak = new XmlEvrakPojo();
			evrak.setId(rs.getLong("ID"));
			evrak.setEvrakSiraNo(rs.getString("EVRAK_SIRA_NO"));
			evrak.setEvrakNo(rs.getString("EVRAK_NO"));
			evrak.setGirisTarih(TemelIslemler.TurkZamanNullCheck(rs.getTimestamp("GIRIS_TARIH")));
			evrak.setEvrakTarihi(TemelIslemler.TurkTarih(rs.getTimestamp("EVRAK_TARIHI")));
			evrak.setEvrakGeldigiKurum(rs.getString("EVRAK_GELDIGI_KURUM"));
			evrak.setKayitEdenKullanici(rs.getLong("KAY_KULLANICI"));
			evrak.setEvrakTipi(rs.getString("EVRAK_TIPI"));
			evrak.setHavaleBirim(rs.getString("HAVALE_BIRIM"));
			evrak.setAciklama(rs.getString("ACIKLAMA"));
			evrak.setDurumu(rs.getString("DURUMU"));
			evrak.setGelenIl(rs.getString("GEL_IL"));
			evrak.setEvrakKonusu(rs.getString("EVRAK_KONUSU"));
			evrak.setAcilMi(rs.getString("ACILMI"));
			evrak.setSorusturmaNo(rs.getString("SORUSTURMA_NO"));
			evrak.setMahkemeKararNo(rs.getString("MAHKEME_KARAR_NO"));
			evrak.setKayitEdenKullaniciAdi(rs.getString("KAYDEDEN_KUL_ISIM"));
			System.out.println("Durumu : " + evrak.getDurumu());
			return evrak;

		}
	}

	public XmlEvrakPojo getEvrakByID(Long evrakId) {
		String sql = " SELECT   ID,EVRAK_SIRA_NO,EVRAK_NO," +
				" GIRIS_TARIH,EVRAK_TARIHI,EVRAK_GELDIGI_KURUM," +
				" KAY_KULLANICI,EVRAK_TIPI,HAVALE_BIRIM,ACIKLAMA, " +
				" GEL_IL,EVRAK_KONUSU,DURUMU,ACILMI,SORUSTURMA_NO," +
				" MAHKEME_KARAR_NO,"
				+ " (select adi || ' ' || soyadi from iym.kullanicilar where id=e.kay_kullanici) AS KAYDEDEN_KUL_ISIM " 
				+ "  FROM EVRAK_KAYIT e "
				+ " WHERE ID = ? ";
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(sql);
		tjdbc.setData(new Object[] { evrakId });
		tjdbc.setType(new int[] { Types.BIGINT});
		tjdbc.setRowMapper(new EvrakKayitRowMapper());
		List<XmlEvrakPojo> evrak = getDao().bulRowMapper(tjdbc);

		if (evrak != null) {
			if (evrak.size() > 0) {
				return evrak.get(0);
			}
		}
		return null;
	}
	
	
	public List<XmlEvrakPojo> getEvrakKismiIadeListByKurum(String kurum, Long kullaniciId) {
		String sql = " SELECT   (select adi || ' ' || soyadi from iym.kullanicilar where id=kay_kullanici) AS KAYDEDEN_KUL_ISIM,"
				+ " ID,EVRAK_SIRA_NO,EVRAK_NO," +
				" GIRIS_TARIH,EVRAK_TARIHI,EVRAK_GELDIGI_KURUM," +
				" KAY_KULLANICI,EVRAK_TIPI,HAVALE_BIRIM,ACIKLAMA," +
				" i.il_adi AS gel_il,EVRAK_KONUSU,DURUMU,ACILMI,SORUSTURMA_NO," +
				" MAHKEME_KARAR_NO " 
				+ "  FROM EVRAK_KAYIT,ILLER i "
				+ " WHERE evrak_geldigi_kurum = ?  AND durumu is null AND i.il_kod = gel_il " 
				+ "  AND kay_kullanici in ( select k.id from iym.kullanicilar k where k.grup_kodu is not null and k.grup_kodu in "
				+ " 		(select grup_kodu from iym.kullanicilar where id=?) ) "
				+ " order by id desc";
		
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(sql);
		tjdbc.setData(new Object[] { kurum,kullaniciId });
		tjdbc.setType(new int[] { Types.VARCHAR,Types.BIGINT});
		tjdbc.setRowMapper(new EvrakKayitRowMapper());
		List<XmlEvrakPojo> evrak = getDao().bulRowMapper(tjdbc);
		return evrak;
	}
	
	
	public List<XmlEvrakPojo> getEvrakByKurum(String kurum,Long kullaniciId) {
		String sql = " SELECT   (select adi || ' ' || soyadi from iym.kullanicilar where id=kay_kullanici) AS KAYDEDEN_KUL_ISIM,"
				+ " ID,EVRAK_SIRA_NO,EVRAK_NO," +
				" GIRIS_TARIH,EVRAK_TARIHI,EVRAK_GELDIGI_KURUM," +
				" KAY_KULLANICI,EVRAK_TIPI,HAVALE_BIRIM,ACIKLAMA," +
				" i.il_adi AS gel_il,EVRAK_KONUSU,DURUMU,ACILMI,SORUSTURMA_NO," +
				" MAHKEME_KARAR_NO " 
				+ "  FROM EVRAK_KAYIT,ILLER i "
				+ " WHERE evrak_geldigi_kurum = ?  AND nvl(durumu, 'NULL') in('KISMI_IADE', 'NULL') AND i.il_kod = gel_il " 
				+ "  AND kay_kullanici in ( select k.id from iym.kullanicilar k where k.grup_kodu is not null and k.grup_kodu in "
				+ " 		(select grup_kodu from iym.kullanicilar where id=?) ) "
				+ " order by id desc";
		
		/*String sql = " SELECT   ID,EVRAK_SIRA_NO,EVRAK_NO," +
				" GIRIS_TARIH,EVRAK_TARIHI,EVRAK_GELDIGI_KURUM," +
				" KAY_KULLANICI,EVRAK_TIPI,HAVALE_BIRIM,ACIKLAMA," +
				" i.il_adi AS gel_il,EVRAK_KONUSU,DURUMU,ACILMI,SORUSTURMA_NO," +
				" MAHKEME_KARAR_NO " 
				+ "  FROM EVRAK_KAYIT,ILLER i "
				+ " WHERE evrak_geldigi_kurum = ?  AND durumu is null AND i.il_kod = gel_il " 
				+ "  AND kay_kullanici = ?  order by id desc";*/
		
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(sql);
		System.out.print("sql" + sql);
	
		tjdbc.setData(new Object[] { kurum,kullaniciId });
		tjdbc.setType(new int[] { Types.VARCHAR,Types.BIGINT});
		tjdbc.setRowMapper(new EvrakKayitRowMapper());
		List<XmlEvrakPojo> evrak = getDao().bulRowMapper(tjdbc);
	
		
		return evrak;
	}
	
	public class XmlKonumRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			String  xmlKonum = new String();
			xmlKonum = rs.getString("XML_KONUM");
			return xmlKonum;

		}
	}

	public String getXmlKonumByEvrakID(Long evrakId) {
		String sql = " SELECT   XML_KONUM " 
				+ "  FROM DOGRU_XML_LOG "
				+ " WHERE EVRAK_ID = ? ";

		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(sql);
		tjdbc.setData(new Object[] { evrakId });
		tjdbc.setType(new int[] { Types.BIGINT});
		tjdbc.setRowMapper(new XmlKonumRowMapper());
		List<String> konum = getDao().bulRowMapper(tjdbc);

		if (konum != null) {
			if (konum.size() > 0) {
				return konum.get(0);
			}
		}
		return null;
	}
	
	
	public Long evrakSayisi(String evrakNo, long personelIymId,String gelenIl) {
		Long sayi = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
				.queryForLong("SELECT count(*) as sayi FROM EVRAK_KAYIT K WHERE instr(K.EVRAK_NO,?||'-')>0   AND k.gel_il = ?",
						new Object[] {evrakNo,gelenIl}, new int[] {Types.VARCHAR,Types.VARCHAR});
		
		return sayi;
	}

	public boolean evrakSil(Long evrakId, long personelIymId,String clientIp) {
		XmlEvrakPojo p = this.getEvrakByID(evrakId);
		if(p == null){
			return false;
		}
		System.out.println(p.getEvrakNo()+"-"+ personelIymId+"-"+  p.getGelenIl());
		Long silinenEvrakSayisi = evrakSayisi(p.getEvrakNo(), personelIymId, p.getGelenIl());
		System.out.println(silinenEvrakSayisi+"-:silinenEvrakSayisi + evrakId:"+evrakId);
		
		TemelJDBC tjdbc = new TemelJDBC();

		String statement = "Update EVRAK_KAYIT set durumu='SILINDI',evrak_no = evrak_no||'-SIL'||?   where id=?  AND durumu is null";
		Object[] obj = {silinenEvrakSayisi+1, evrakId };
		int[] i = { Types.BIGINT, Types.VARCHAR };

		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().degistir(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		
	}
	
	
	public boolean evrakMahkemeKararIslemSil(Long evrakId, long personelIymId,String clientIp) {
		XmlEvrakPojo p = this.getEvrakByID(evrakId);
		if(p == null){
			return false;
		}
		
		TemelJDBC tjdbc = new TemelJDBC();

		String statement = "Delete from EVRAK_MAHKEME_KARAR_ISLEM  where evrak_id=?  ";
		Object[] obj = {evrakId};
		int[] i = { Types.BIGINT};

		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().degistir(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		
	}


	public class EvrakDurumMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
		    XmlEvrakPojo evrak  = new XmlEvrakPojo();
			evrak.setEvrakSiraNo(rs.getString("EVRAK_SIRA_NO"));
			evrak.setEvrakNo(rs.getString("EVRAK_NO"));
			evrak.setDurumu(rs.getString("DURUMU"));
			evrak.setGirisTarih(rs.getString("KAYIT_TARIHI"));
			return evrak;
		}
	}

	public List<XmlEvrakPojo> evrakDurumGetirByKullaniciIdEvrakNo(String evrakNo,Long kullaniciId,String kullaniciAdi) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(" SELECT  ev.evrak_sira_no as EVRAK_SIRA_NO, ev.evrak_no as EVRAK_NO,"+
				"               CASE "+
				"                 WHEN (MK.KARAR_TIP IN (510, 520, 530) AND "+
				"                      (SELECT COUNT(*) "+
				"                          FROM MAHKEME_KARAR K "+
				"                         WHERE MK.ID = K.ID "+
				"                           AND K.DURUM = 'ONAYLANDI') > 0) THEN "+
				"                  'TANIMLANDI' "+
				"                 WHEN (SELECT COUNT(*) "+
				"                         FROM HEDEFLER H1, MAHKEME_KARAR MR "+
				"                        WHERE H1.MAHKEME_KARAR_ID = MR.ID "+
				"                          AND MR.ID = MK.ID "+
				"                          AND (H1.DURUMU IS NULL OR "+
				"                              H1.DURUMU = 'TANIMLANAMADI')) > 0 OR "+
				"                      (SELECT COUNT(*) "+
				"                         FROM HEDEFLER H1, MAHKEME_KARAR MR "+
				"                        WHERE H1.MAHKEME_KARAR_ID = MR.ID "+
				"                          AND MR.ID = MK.ID "+
				"                          AND H1.DURUMU = 'TANIMLANDI') = 0 THEN "+
				"                  'İŞLEMDE' "+
				"                 WHEN (SELECT COUNT(*) "+
				"                         FROM HEDEFLER H1, MAHKEME_KARAR MR "+
				"                        WHERE H1.MAHKEME_KARAR_ID = MR.ID "+
				"                          AND MR.ID = MK.ID "+
				"                          AND (H1.DURUMU IS NULL OR "+
				"                              H1.DURUMU = 'TANIMLANAMADI')) = 0 OR "+
				"                      (SELECT COUNT(*) "+
				"                         FROM HEDEFLER H1, MAHKEME_KARAR MR "+
				"                        WHERE H1.MAHKEME_KARAR_ID = MR.ID "+
				"                          AND MR.ID = MK.ID "+
				"                          AND H1.DURUMU = 'TANIMLANDI') > 0 THEN "+
				"                  'TANIMLANDI' "+
				"               END AS DURUMU," +
				"				EV.GIRIS_TARIH AS KAYIT_TARIHI "+
				"          FROM EVRAK_KAYIT EV, MAHKEME_KARAR MK "+
				"         WHERE  "+
				"                 EV.ID = MK.EVRAK_ID(+) "+
				"           AND EV.DURUMU = 'ONAYLANDI' "+
				"           AND EV.EVRAK_GELDIGI_KURUM IN "+
				"               (SELECT EK.KURUM_KOD "+
				"                  FROM EVRAK_GELEN_KURUMLAR EK, KULLANICI_KURUM KK "+
				"                 WHERE EK.KURUM_KOD = KK.KURUM_KOD "+
				"                   AND KK.KULLANICI_ID = ? ) "+
				"           AND EV.KAY_KULLANICI IN "+
				"               (SELECT ID "+
				"                  FROM KULLANICILAR "+
				"                 WHERE TEMSIL_EDILEN_KURUM IS NOT NULL) "+
				"           AND EV.EVRAK_NO = ? "+
				"         ORDER BY EV.ID DESC ");
		System.out.println(tjdbc.getStatement());
		tjdbc.setData(new Object[] {kullaniciId,evrakNo});
		tjdbc.setType(new int[] {Types.BIGINT,Types.VARCHAR});
		tjdbc.setRowMapper(new EvrakDurumMapper());
		List<XmlEvrakPojo>  list =  getDao().bulRowMapper(tjdbc);
		/*************LOG KAYDET*******************************/
		IymEvrakAramaPojo evrak = new IymEvrakAramaPojo();
		evrak.setEvrakNo(evrakNo);
		evrakAramaLogKaydet(evrak, null, null, kullaniciId,"KATIP",kullaniciAdi);
		/******************************************************/
		return list;
	}

	
	
	public List<IymEvrakAramaPojo> elektronikTeslimEvrakAra(IymEvrakAramaPojo evrak,
			Date sorguBaslama, Date sorguBitis, Long kullaniciId,String kullaniciAdi) {
		TemelJDBC tjdbc = new TemelJDBC();

		String kosulStr = "";

		List<Object> data = new ArrayList();
		List<Integer> type = new ArrayList();
		// koşuldaki kullanici id alanı için eklendi
		data.add(kullaniciId);
		type.add(Types.BIGINT);
		
		data.add(kullaniciId);
		type.add(Types.BIGINT);
		
		data.add(kullaniciId);
		type.add(Types.BIGINT);
		
		


		if (!TemelIslemler.bosMu(evrak.getEvrakSiraNo())) {
			kosulStr += " ev.evrak_sira_no like ? and ";
			data.add("%" + evrak.getEvrakSiraNo());
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getEvrakNo())) {
			kosulStr += " ev.evrak_no like ? and ";

			data.add(evrak.getEvrakNo() + "%");
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getEvrakKonu())) {
			kosulStr += " ev.evrak_konusu like ? and ";
			data.add("%" + evrak.getEvrakKonu() + "%");
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(sorguBaslama)) {
			kosulStr += " trunc(ev.giris_tarih)>= ? and ";
			data.add(sorguBaslama);
			type.add(Types.VARCHAR);
		}

		String kurum = kurumGetir(kullaniciId);
		// if (1 == 1) {
		// Tib karar girici ve B kurum sadece kendi girdigi evrakı gorebilir...
		if (kurum == null || "B".equals(kurum) || kullaniciId.equals(2779L)) {
			kosulStr += " ev.kay_kullanici= ? and ";
			data.add(kullaniciId);
			type.add(Types.BIGINT);
		}

		if (!TemelIslemler.bosMu(sorguBitis)) {
			kosulStr += " trunc(ev.giris_tarih)<= ? and ";
			data.add(sorguBitis);
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getEvrakGelenKurumKod())) {
			kosulStr += " ev.evrak_geldigi_kurum =? and ";
			data.add(evrak.getEvrakGelenKurumKod());
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getEvrakGelenIl())) {
			kosulStr += " ev.gel_il = (select il_kod from iller where il_adi =?) and ";
			data.add(evrak.getEvrakGelenIl());
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getMahkemeIl())) {
			kosulStr += " mk.mahkeme_ili = (select il_kod from iller where il_adi =?) and ";
			data.add(evrak.getMahkemeIl());
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getEvrakAciklama())) {
			kosulStr += " ev.aciklama like ? and ";
			data.add("%" + evrak.getEvrakAciklama() + "%");
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getMahkemeKararNo())) {
			kosulStr += " mk.mahkeme_karar_no = ? and ";
			data.add(evrak.getMahkemeKararNo());
			type.add(Types.VARCHAR);
		}
		if (!TemelIslemler.bosMu(evrak.getHedefNo())) {
			kosulStr += " mk.ID in (select mahkeme_karar_id from hts_hedefler_talep where  hedef_no = ? ) and ";
			data.add(evrak.getHedefNo());
			type.add(Types.VARCHAR);
		}

		
		if (!TemelIslemler.bosMu(evrak.getEvrakDurum())) {
			kosulStr += " y.durum = ? and ";
			data.add(evrak.getEvrakDurum());
			type.add(Types.VARCHAR);
		}
		
		
		
		data.add(kullaniciId);
		type.add(Types.BIGINT);
		
		data.add(kullaniciId);
		type.add(Types.BIGINT);
		
		String kosul2Str = "";
		if (!TemelIslemler.bosMu(sorguBaslama)) {
			kosul2Str += " and trunc(ya.tarih)>= ? ";
			data.add(sorguBaslama);
			type.add(Types.VARCHAR);
		}
		
		if (!TemelIslemler.bosMu(evrak.getEvrakNo())) {
			kosul2Str += "  and ya.sayisi like ? ";
			data.add(evrak.getEvrakNo() + "%");
			type.add(Types.VARCHAR);
		}
		
		if (!TemelIslemler.bosMu(sorguBitis)) {
			kosul2Str += " and trunc(ya.tarih)<= ? ";
			data.add(sorguBitis);
			type.add(Types.VARCHAR);
		}
		
		if (!TemelIslemler.bosMu(evrak.getEvrakKonu())) {
			kosul2Str += " and ya.konusu like ? ";
			data.add("%" + evrak.getEvrakKonu() + "%");
			type.add(Types.VARCHAR);
		}

		if (!TemelIslemler.bosMu(evrak.getEvrakDurum())) {
			kosul2Str += " and y.durum = ? ";
			data.add(evrak.getEvrakDurum());
			type.add(Types.VARCHAR);
		}
		
		
		
		if (kosulStr.length() > 0) {
			kosulStr = " and " + kosulStr.substring(0, kosulStr.length() - 5);
		} else {
			kosulStr = " and EV.giris_tarih > sysdate - 30";
		}
		
		tjdbc.setStatement(" select * from (	SELECT Y.ID,EV.GIRIS_TARIH,Y.SAYISI AS EVRAK_SIRA_NO,EV.EVRAK_NO AS EVRAK_NO, " +
				   " NVL(Y.DURUM, 'İŞLEMDE') AS DURUM,EV.ACILMI,IL.IL_ADI,mk.mahkeme_adi, mk.mahkeme_karar_no, "+
				   " (SELECT MKT.KARAR_TIPI "+
				   	" FROM MAH_KARAR_TIPLERI MKT "+
				   	" WHERE MKT.KARAR_KODU = MK.KARAR_TIP) AS KARAR_TIP, "+	
				   	" EV.EVRAK_GELDIGI_KURUM, mk.sorusturma_no,"+ 
				    " (SELECT IL_ADI FROM ILLER I WHERE I.IL_KOD = MK.MAHKEME_ILI) AS MAHKEME_ILI "+
				  " FROM YAZILAR_HARICI Y, EVRAK_KAYIT EV, ILLER IL, HTS_MAHKEME_KARAR_TALEP MK " +
				  " WHERE EV.ID = Y.UST_EVRAK_ID(+) "+
				  " AND EV.DURUMU = 'ONAYLANDI' "+
				  " AND Y.DURUM IN ('ONAYLANDI', 'AKTARILDI', 'ARSIV') "+
				  " AND ? IN " +
				  " (SELECT ID FROM KULLANICILAR WHERE TEMSIL_EDILEN_KURUM IS NOT NULL) "+
				  " AND ( EV.EVRAK_GELDIGI_KURUM IN "+
				  " (SELECT EK.KURUM_KOD "+
				  " FROM EVRAK_GELEN_KURUMLAR EK, KULLANICI_KURUM KK "+
				  " WHERE EK.KURUM_KOD = KK.KURUM_KOD "+
				  "    AND KK.KULLANICI_ID = ? ) " +
				  "   OR "+
                  "  Y.Id IN (SELECT y.id FROM yazilar y WHERE y.evrak_kurum IN (SELECT EK.KURUM_KOD "+
                  " FROM EVRAK_GELEN_KURUMLAR EK, KULLANICI_KURUM KK "+
                  " WHERE EK.KURUM_KOD = KK.KURUM_KOD "+
                  "  AND KK.KULLANICI_ID = ?)  ) )" +
				  " AND IL.IL_KOD = EV.GEL_IL "+
				  " AND MK.EVRAK_ID (+) = EV.ID "+
				  "  " + kosulStr+
				  " UNION ALL " +
				  " SELECT YA.ID,YA.TARIH,YA.SAYISI AS EVRAK_SIRA_NO, "+
				  "	'' AS EVRAK_NO, NVL(Y.DURUM, 'İŞLEMDE') AS DURUM, "+
				  " YA.Acilmi AS ACILMI,IL.IL_ADI, '' AS MAHKEME_ADI, "+
                  " '' AS MAHKEME_KARAR_NO,'' AS KARAR_TIP, "+
                  " YA.Evrak_Kurum AS EVRAK_GELDIGI_KURUM,'' AS SORUSTURMA_NO, '' AS MAHKEME_ILI "+
                  " FROM YAZILAR_HARICI          Y, "+
                  " ILLER                   IL  , "+
                  " YAZILAR YA "+
                  " WHERE "+
                  " Y.DURUM IN ('ONAYLANDI', 'AKTARILDI', 'ARSIV') "+
                  " AND NVL(Y.UST_EVRAK_ID,'0') = '0' "+
                  " AND ? IN (SELECT ID FROM KULLANICILAR WHERE TEMSIL_EDILEN_KURUM IS NOT NULL) "+
                  "	AND "+
                  "		Y.ID IN "+
                  " (SELECT Y.ID "+
                  " FROM YAZILAR Y "+
                  " WHERE Y.EVRAK_KURUM IN "+
                  "      (SELECT EK.KURUM_KOD "+
                  "         FROM EVRAK_GELEN_KURUMLAR EK, KULLANICI_KURUM KK "+
                  "        WHERE EK.KURUM_KOD = KK.KURUM_KOD "+
                  "          AND KK.KULLANICI_ID = ?)) "+
                  " AND IL.IL_KOD = YA.EVRAK_IL "+
                  " AND y.id = YA.id "+kosul2Str+") where rownum<101 order by giris_tarih desc"
				);

		int types[] = new int[type.size()];
		for (int i = 0; i < type.size(); i++) {
			types[i] = (Integer) type.get(i);
		}

		tjdbc.setData(data.toArray());
		tjdbc.setRowMapper(new EvrakAramaRowMapper());
		//System.out.println(tjdbc.getStatement());
		evrakAramaLogKaydet(evrak, sorguBaslama, sorguBitis, kullaniciId, "ELEKTRONIK_EVRAK_ARAMA",kullaniciAdi);
		return getDao().bulRowMapper(tjdbc);
	}
	
	public List<XmlEvrakPojo> tespitEvrakiDurumGetirByKullaniciId(long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(" SELECT * FROM (" +
				"   SELECT Y.SAYISI AS EVRAK_SIRA_NO, nvl(Y.DURUM,'İŞLEMDE') AS DURUMU, K.EVRAK_NO AS EVRAK_NO,y.kayit_tarihi " +
				" FROM YAZILAR_HARICI Y, EVRAK_KAYIT K WHERE K.ID = Y.UST_EVRAK_ID (+) " +
				" AND K.DURUMU ='ONAYLANDI' " +
				" AND Y.DURUM in ('ONAYLANDI','AKTARILDI') " +
				" AND K.KAY_KULLANICI IN "+
				"               (SELECT ID "+
				"                  FROM KULLANICILAR "+
				"                 WHERE TEMSIL_EDILEN_KURUM IS NOT NULL) "+
				" AND K.EVRAK_GELDIGI_KURUM IN "+
				"               (SELECT EK.KURUM_KOD "+
				"                  FROM EVRAK_GELEN_KURUMLAR EK, KULLANICI_KURUM KK "+
				"                 WHERE EK.KURUM_KOD = KK.KURUM_KOD "+
				"                   AND KK.KULLANICI_ID = ? ) "+
                " AND K.KAY_KULLANICI = ? " +
                "  UNION ALL " +
                "  SELECT " +
                "     YA.SAYISI AS EVRAK_SIRA_NO," +
                " NVL(Y.DURUM, 'İŞLEMDE') AS DURUM," +
                " '' AS EVRAK_NO, " +
                " YA.kayit_tarihi " +
                " FROM YAZILAR_HARICI Y, ILLER IL, YAZILAR YA " +
                " WHERE Y.DURUM IN ('ONAYLANDI', 'AKTARILDI') " +
                "  AND NVL(Y.UST_EVRAK_ID, '0') = '0'" +
                " AND  ? IN" +
                "   (SELECT ID FROM KULLANICILAR WHERE TEMSIL_EDILEN_KURUM IS NOT NULL)" +
                " AND Y.ID IN (SELECT Y.ID " +
                "   FROM YAZILAR Y WHERE Y.EVRAK_KURUM IN" +
                "  (SELECT EK.KURUM_KOD" +
                "  FROM EVRAK_GELEN_KURUMLAR EK, KULLANICI_KURUM KK" +
                " WHERE EK.KURUM_KOD = KK.KURUM_KOD AND KK.KULLANICI_ID =  ? ))" +
                " AND IL.IL_KOD = YA.EVRAK_IL AND Y.ID = YA.ID" +
                " ) A ORDER BY A.KAYIT_TARIHI DESC");
		tjdbc.setData(new Object[] {kullaniciId,kullaniciId,kullaniciId,kullaniciId});
		tjdbc.setType(new int[] {Types.BIGINT,Types.BIGINT,Types.BIGINT,Types.BIGINT});
		tjdbc.setRowMapper(new EvrakDurumMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	
	public List<XmlEvrakPojo> tespitEvrakiDurumGetirByKullaniciIdEvrakNo(String evrakNo, long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(" SELECT Y.SAYISI AS EVRAK_SIRA_NO, nvl(Y.DURUM,'İŞLEMDE') AS DURUMU, K.EVRAK_NO AS EVRAK_NO,K.GIRIS_TARIH as KAYIT_TARIHI " +
				" FROM YAZILAR_HARICI Y, EVRAK_KAYIT K WHERE K.ID = Y.UST_EVRAK_ID (+) " +
				" AND K.DURUMU ='ONAYLANDI' " +
				" AND Y.DURUM(+) in ('ONAYLANDI','AKTARILDI') " +
				" AND K.KAY_KULLANICI IN "+
				"               (SELECT ID "+
				"                  FROM KULLANICILAR "+
				"                 WHERE TEMSIL_EDILEN_KURUM IS NOT NULL) "+
				" AND K.EVRAK_GELDIGI_KURUM IN "+
				"               (SELECT EK.KURUM_KOD "+
				"                  FROM EVRAK_GELEN_KURUMLAR EK, KULLANICI_KURUM KK "+
				"                 WHERE EK.KURUM_KOD = KK.KURUM_KOD "+
				"                   AND KK.KULLANICI_ID = ? ) "+
                " AND K.EVRAK_NO =  ? "+
                " AND K.KAY_KULLANICI = ? "+
                " ORDER BY Y.KAYIT_TARIHI DESC");
		tjdbc.setData(new Object[] {kullaniciId,evrakNo,kullaniciId});
		tjdbc.setType(new int[] {Types.BIGINT,Types.VARCHAR,Types.BIGINT});
		tjdbc.setRowMapper(new EvrakDurumMapper());
		return getDao().bulRowMapper(tjdbc);
	}
	
	
	public class EvrakDurumIdMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
		    XmlEvrakPojo evrak  = new XmlEvrakPojo();
			evrak.setEvrakSiraNo(rs.getString("EVRAK_SIRA_NO"));
			evrak.setEvrakNo(rs.getString("EVRAK_NO"));
			evrak.setDurumu(rs.getString("DURUMU"));
			evrak.setId(rs.getLong("ID"));
			return evrak;
		}
	}
	
	public List<XmlEvrakPojo> tespitEvrakiDurumGetirByKullaniciIdTibEvrakNo(String evrakNo, long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(" SELECT Y.SAYISI AS EVRAK_SIRA_NO, nvl(Y.DURUM,'İŞLEMDE') AS DURUMU, K.EVRAK_NO AS EVRAK_NO,y.id " +
				" FROM YAZILAR_HARICI Y, EVRAK_KAYIT K WHERE K.ID = Y.UST_EVRAK_ID (+) " +
				" AND K.DURUMU ='ONAYLANDI' " +
				" AND Y.DURUM in ('ONAYLANDI','AKTARILDI') " +
				" AND K.KAY_KULLANICI IN "+
				"               (SELECT ID "+
				"                  FROM KULLANICILAR "+
				"                 WHERE TEMSIL_EDILEN_KURUM IS NOT NULL) "+
				" AND K.EVRAK_GELDIGI_KURUM IN "+
				"               (SELECT EK.KURUM_KOD "+
				"                  FROM EVRAK_GELEN_KURUMLAR EK, KULLANICI_KURUM KK "+
				"                 WHERE EK.KURUM_KOD = KK.KURUM_KOD "+
				"                   AND KK.KULLANICI_ID = ? ) "+
                " AND Y.SAYISI =  ? "+
                " AND K.KAY_KULLANICI = ? " +
                " ORDER BY Y.KAYIT_TARIHI DESC");
		tjdbc.setData(new Object[] {kullaniciId,evrakNo,kullaniciId});
		tjdbc.setType(new int[] {Types.BIGINT,Types.VARCHAR,Types.BIGINT});
		tjdbc.setRowMapper(new EvrakDurumIdMapper());
		return getDao().bulRowMapper(tjdbc);
	}
	
	
	public List<XmlEvrakPojo> evrakDurumGetirByKullaniciIdTibEvrakNo(String tibEvrakNo, long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("  SELECT Y.SAYISI AS EVRAK_SIRA_NO, nvl(Y.DURUM,'İŞLEMDE') AS DURUMU,NULL AS evrak_no,y.id  "+
						   " FROM YAZILAR_HARICI Y,YAZILAR YA WHERE "+
						   " Y.DURUM in ('ONAYLANDI','AKTARILDI') "+
                           " AND YA.EVRAK_KURUM IN "+
                           " (SELECT k.kurum_kod from kullanici_kurum k WHERE k.kullanici_id = ?) "+
                           " AND Y.SAYISI =  ?"+
                           " AND y.id = ya.id ");
		tjdbc.setData(new Object[] {kullaniciId,tibEvrakNo});
		tjdbc.setType(new int[] {Types.BIGINT,Types.VARCHAR});
		tjdbc.setRowMapper(new EvrakDurumIdMapper());
		return getDao().bulRowMapper(tjdbc);
	}
	
	public class MahkemeKodlariMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
		    MahkemeKodlariPojo mahkemeKodlari  = new MahkemeKodlariPojo();
		    mahkemeKodlari.setMahkemeKodu(rs.getString("MAHKEME_KODU"));
		    mahkemeKodlari.setMahkemeAdi(rs.getString("MAHKEME_ADI"));
			return mahkemeKodlari;
		}
	}

	public List<String> mahkemeleriGetir(Long kullaniciId,String ilKod) {
		TemelJDBC tjdbc = new TemelJDBC();
		String ekSql =" ";
		if(!(ilKod == null || "".equalsIgnoreCase(ilKod))){
			ekSql = " and t.mahkeme_il_ilce = '"+ilKod+"'";
		}
		List<String> kurumList = Utility.evrakKurumGetir(kullaniciId);
		String evrakKurum = kurumList.get(0);
		if(evrakKurum.equalsIgnoreCase(EVRAK_GELEN_KURUMLAR.JANDARMA.getKurumKodu()) )
			tjdbc.setStatement("select mahkeme_kodu,mahkeme_adi from mahkeme_adi t WHERE t.mahkeme_turu != '98' " +
					"and t.mahkeme_turu != '20' and t.mahkeme_turu != '19' and t.mahkeme_turu != '18' "+ekSql+" order by t.mahkeme_kodu");
		else if(evrakKurum.equalsIgnoreCase(EVRAK_GELEN_KURUMLAR.MIT.getKurumKodu()) )
			tjdbc.setStatement("select mahkeme_kodu,mahkeme_adi from mahkeme_adi t WHERE t.mahkeme_turu != '98'  " +
					"and t.mahkeme_turu != '20' and t.mahkeme_turu != '19' and t.mahkeme_turu != '22' and t.mahkeme_turu != '21' "+ekSql+" order by t.mahkeme_kodu");
		else if(evrakKurum.equalsIgnoreCase(EVRAK_GELEN_KURUMLAR.EGMIDB.getKurumKodu()) )
			tjdbc.setStatement("select mahkeme_kodu,mahkeme_adi from mahkeme_adi t WHERE t.mahkeme_turu != '98' " +
					"and t.mahkeme_turu != '18' and t.mahkeme_turu != '22' and t.mahkeme_turu != '21' "+ekSql+" order by t.mahkeme_kodu");
		else
			tjdbc.setStatement("select mahkeme_kodu,mahkeme_adi from mahkeme_adi t WHERE t.mahkeme_turu != '98' " +
					"and t.mahkeme_turu != '18' and t.mahkeme_turu != '22' and t.mahkeme_turu != '20' and t.mahkeme_turu != '19' "+ekSql+" and t.mahkeme_turu != '21'  order by t.mahkeme_kodu");
		tjdbc.setData(new Object[] {});
		tjdbc.setType(new int[] {});
		tjdbc.setRowMapper(new MahkemeKodlariMapper());
		return getDao().bulRowMapper(tjdbc);
	}
	
	public List<MahkemeKodlariPojo> mahkemeleriGetir(Long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		String ekSql =" ";
		List<String> kurumList = Utility.evrakKurumGetir(kullaniciId);
		String evrakKurum = kurumList.get(0);
		if(evrakKurum.equalsIgnoreCase(EVRAK_GELEN_KURUMLAR.JANDARMA.getKurumKodu()) )
			tjdbc.setStatement("select mahkeme_kodu,mahkeme_adi from mahkeme_adi t WHERE t.mahkeme_turu != '98' " +
					"and t.mahkeme_turu != '20' and t.mahkeme_turu != '19' and t.mahkeme_turu != '18' "+ekSql+" order by t.mahkeme_kodu");
		else if(evrakKurum.equalsIgnoreCase(EVRAK_GELEN_KURUMLAR.MIT.getKurumKodu()) )
			tjdbc.setStatement("select mahkeme_kodu,mahkeme_adi from mahkeme_adi t WHERE t.mahkeme_turu != '98'  " +
					"and t.mahkeme_turu != '20' and t.mahkeme_turu != '19' and t.mahkeme_turu != '22' and t.mahkeme_turu != '21' "+ekSql+" order by t.mahkeme_kodu");
		else if(evrakKurum.equalsIgnoreCase(EVRAK_GELEN_KURUMLAR.EGMIDB.getKurumKodu()) )
			tjdbc.setStatement("select mahkeme_kodu,mahkeme_adi from mahkeme_adi t WHERE t.mahkeme_turu != '98' " +
					"and t.mahkeme_turu != '18' and t.mahkeme_turu != '22' and t.mahkeme_turu != '21' "+ekSql+" order by t.mahkeme_kodu");
		else
			tjdbc.setStatement("select mahkeme_kodu,mahkeme_adi from mahkeme_adi t WHERE t.mahkeme_turu != '98' " +
					"and t.mahkeme_turu != '18' and t.mahkeme_turu != '22' and t.mahkeme_turu != '20' and t.mahkeme_turu != '19' "+ekSql+" and t.mahkeme_turu != '21'  order by t.mahkeme_kodu");
		tjdbc.setData(new Object[] {});
		tjdbc.setType(new int[] {});
		tjdbc.setRowMapper(new MahkemeKodlariMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	
	public class MahkemeKararTipleriMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
		    MahkemeKararKodlariPojo kod  = new MahkemeKararKodlariPojo();
		    kod.setKararKodu(rs.getString("KARAR_KODU"));
		    kod.setKararAdi(rs.getString("KARAR_TIPI"));
			return kod;
		}
	}

	public List<String> mahkemeKararTipleriGetir(Long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		List<String> kurumList = Utility.evrakKurumGetir(kullaniciId);
		String evrakKurum = kurumList.get(0);
		if(evrakKurum.equalsIgnoreCase(EVRAK_GELEN_KURUMLAR.MIT.getKurumKodu()) || evrakKurum.equalsIgnoreCase(EVRAK_GELEN_KURUMLAR.EGMIDB.getKurumKodu()) )
			tjdbc.setStatement("select karar_kodu,karar_tipi from mah_karar_tipleri where karar_kodu in (100,150,200,600) ORDER BY KARAR_KODU");
		else if(evrakKurum.equalsIgnoreCase(EVRAK_GELEN_KURUMLAR.MIT.getKurumKodu()) || evrakKurum.equalsIgnoreCase(EVRAK_GELEN_KURUMLAR.JANDARMA.getKurumKodu()) )
			tjdbc.setStatement("select karar_kodu,karar_tipi from mah_karar_tipleri where karar_kodu not in (150) ORDER BY KARAR_KODU ");
		
		else
			tjdbc.setStatement(" select karar_kodu,karar_tipi from mah_karar_tipleri where karar_kodu not in ('100','150','151','200','570','600','310','320','710','720') ORDER BY KARAR_KODU ");
		tjdbc.setData(new Object[] {});
		tjdbc.setType(new int[] {});
		tjdbc.setRowMapper(new MahkemeKararTipleriMapper());
		return getDao().bulRowMapper(tjdbc);
	}
	
	public class HedefTipleriMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			HedefTipleriPojo kod  = new HedefTipleriPojo();
		    kod.setHedefKodu(rs.getString("HEDEF_KODU"));
		    kod.setHedefTipi(rs.getString("HEDEF_TIPI"));
			return kod;
		}
	}

	public List<String> hedefTipleriGetir(Long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		List<String> kurumList = Utility.evrakKurumGetir(kullaniciId);
		String evrakKurum = kurumList.get(0);
		tjdbc.setStatement(" select hedef_kodu,hedef_tipi from hedef_tipleri t where t.hedef_kodu !='0' ORDER BY hedef_kodu");
		tjdbc.setData(new Object[] {});
		tjdbc.setType(new int[] {});
		tjdbc.setRowMapper(new HedefTipleriMapper());
		return getDao().bulRowMapper(tjdbc);
	}
	
	
	public class MahkemeSucTipleriMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			MahkemeSucTipleriPojo kod  = new MahkemeSucTipleriPojo();
		    kod.setSucTipi(rs.getString("SUC_TIPI"));
		    kod.setSucAciklama(rs.getString("SUC_ACIKLAMA"));
			return kod;
		}
	}

	public List<String> mahkemeSucTipleriGetir(Long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		List<String> kurumList = Utility.evrakKurumGetir(kullaniciId);
		String evrakKurum = kurumList.get(0);
		tjdbc.setStatement(" select suc_tipi,suc_aciklama from mahkeme_suc_tipleri t where t.durum='A' ORDER BY suc_tipi");
		tjdbc.setData(new Object[] {});
		tjdbc.setType(new int[] {});
		tjdbc.setRowMapper(new MahkemeSucTipleriMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	public class SorguTipleriMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			SorguTipleriPojo kod  = new SorguTipleriPojo();
		    kod.setSorguTipi(rs.getString("SORGU_TIPI"));
		    kod.setSorguAciklama(rs.getString("SORGU_ACIKLAMA"));
			return kod;
		}
	}

	public List<String> sorguTipleriGetir(Long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		List<String> kurumList = Utility.evrakKurumGetir(kullaniciId);
		String evrakKurum = kurumList.get(0);
		tjdbc.setStatement(" select sorgu_tipi,sorgu_aciklama from sorgu_tipleri t ORDER BY sorgu_tipi");
		tjdbc.setData(new Object[] {});
		tjdbc.setType(new int[] {});
		tjdbc.setRowMapper(new SorguTipleriMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	
	public class TespitTurleriMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			TespitTurleriPojo kod  = new TespitTurleriPojo();
		    kod.setTespitTuru(rs.getString("TESPIT_TURU"));
		    kod.setTespitAciklama(rs.getString("TESPIT_ACIKLAMA"));
			return kod;
		}
	}

	public List<String> tespitTurleriGetir(Long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		List<String> kurumList = Utility.evrakKurumGetir(kullaniciId);
		String evrakKurum = kurumList.get(0);
		tjdbc.setStatement(" select tespit_turu,tespit_aciklama from tespit_turleri t ORDER BY tespit_turu");
		tjdbc.setData(new Object[] {});
		tjdbc.setType(new int[] {});
		tjdbc.setRowMapper(new TespitTurleriMapper());
		return getDao().bulRowMapper(tjdbc);
	}
	
	public class YazilarHariciMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			HariciYazilarPojo yazi  = new HariciYazilarPojo();
			yazi.setId(rs.getLong("ID")); 
			yazi.setSayisi(rs.getString("SAYISI")); 
			yazi.setKonusu(rs.getString("KONUSU")); 
			yazi.setKayitTarihi(rs.getDate("KAYIT_TARIHI")); 
			yazi.setDurum(rs.getString("DURUM")); 
			yazi.setUstEvrakId(rs.getString("UST_EVRAK_ID")); 
			yazi.setEkSha(rs.getString("EK_SHA")); 
			yazi.setEkPath(rs.getString("EK_PATH")); 
			yazi.setAktarilmaTarihi(rs.getDate("AKTARILMA_TARIHI")); 
			return yazi;
		}
	}

	public List<HariciYazilarPojo> hariciYaziBul(String tibEvrakNo,Long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(" SELECT * FROM YAZILAR_HARICI H " +
				" WHERE H.SAYISI = ? " +
				" AND H.DURUM in ('ONAYLANDI','AKTARILDI') " +
				" ORDER BY H.ID DESC ");
		tjdbc.setData(new Object[] {tibEvrakNo});
		tjdbc.setType(new int[] {Types.VARCHAR});
		tjdbc.setRowMapper(new YazilarHariciMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	public boolean hariciEvrakAktarildi(Long hYaziId, long kullaniciId) {
		TemelJDBC tjdbc = new TemelJDBC();
		
		String statement = "Update YAZILAR_HARICI set " +
		" durum='AKTARILDI'," +
		"aktarilma_tarihi=sysdate  where id=?  ";
		Object[] obj = { hYaziId};
		int[] i = { Types.BIGINT };

		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().degistir(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		
	}

	public boolean hariciEvrakArsivle(Long hYaziId) {
		TemelJDBC tjdbc = new TemelJDBC();
		String statement = "Update YAZILAR_HARICI set durum='ARSIV' where id = ? ";
		Object[] obj = { hYaziId};
		int[] i = { Types.BIGINT };

		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().degistir(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		
	}

	public Long xmlLogIlslemIdGetir() {
		Long mahkemeAidiyatTalepDetayId = (Long) ((TemelDAO) getDao()).getJdbcTemplate()
				.queryForLong("select XML_LOG_ISLEM_ID_SEQ.nextval from dual",
						new Object[] {}, new int[] {});

		return mahkemeAidiyatTalepDetayId;
	}

	
	public class IllerMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			IllerPojo il = new IllerPojo();
			il.setIlAdi(rs.getString("IL_ADI"));
			il.setIlKod(rs.getString("IL_KOD"));
			return il;
		}
	}
	public List<IllerPojo> mahkemeIlListGetir() {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("select il_kod,il_adi from iller order by il_kod");
		tjdbc.setData(new Object[] {});
		tjdbc.setType(new int[] {});
		tjdbc.setRowMapper(new IllerMapper());
		return getDao().bulRowMapper(tjdbc);
	}

	
	public ArrayList<ThreadPojo> threadListesiGetir() {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc
				.setStatement("select k.id,K.THREAD_ISIM,K.THREAD_BAS_TARIH,K.THREAD_DURUM,K.THREAD_BIT_TARIH from IYM_THREAD_LOG k order by K.THREAD_DURUM ");
		tjdbc.setData(new Object[] { });
		tjdbc.setType(new int[] {  });
		tjdbc.setRowMapper(new ThreadRowMapper());

		return (ArrayList<ThreadPojo>) getDao().bulRowMapper(tjdbc);
	}
	
	public class ThreadRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			ThreadPojo kim = new ThreadPojo();

			kim.setThreadId(rs.getLong("ID"));
			kim.setThreadName(rs.getString("THREAD_ISIM"));
			kim.setBaslamaTarihi(rs.getTimestamp("THREAD_BAS_TARIH"));
			kim.setBitisTarihi(rs.getTimestamp("THREAD_BIT_TARIH"));
			kim.setDurum(rs.getString("THREAD_DURUM"));
			kim.setAciklama(rs.getString("ACIKLAMA"));
			return kim;
		}
	}
	
	public Long threadIdGetir(String threadAd) {
		Long siraNo = (Long) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForLong(
						"SELECT id FROM IYM_THREAD_LOG WHERE THREAD_ISIM = ? AND THREAD_DURUM='ISLEMDE'",
						new Object[] { threadAd }, new int[] { Types.VARCHAR });
		return siraNo;
	}

	public List<HariciYazilarPojo> hariciIslenecekYaziBul() {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(" SELECT * FROM YAZILAR_HARICI H " +
				" WHERE " +
				"  H.DURUM is NULL " +
				" ORDER BY H.ID DESC ");
		tjdbc.setData(new Object[] {});
		tjdbc.setType(new int[] {});
		tjdbc.setRowMapper(new YazilarHariciMapper());
		return getDao().bulRowMapper(tjdbc);
	}
	
	public IymYaziPojo imzalanmisYaziGetir(String sayisi) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(" SELECT  y.ID,y.durum,y.tarih,y.sayisi,y.konusu,y.ilgi,y.ekler,y.dagitim,y.kayit_tarihi,y.acilmi,y.aciklama,y.ust_evrak_sira_no as cevapsirano FROM YAZILAR Y " +
				" WHERE " +
				" Y.SAYISI = ? " +
				" AND Y.DURUM IS NOT NULL  AND Y.DURUM IN('ARSIV','ONAYLANDI')" +
				" AND Y.IMZA_ONAY_ID IS NOT NULL " +
				" AND Y.IMZA_TARIH IS NOT NULL" +
				" ORDER BY Y.ID DESC ");
		tjdbc.setData(new Object[] {sayisi});
		tjdbc.setType(new int[] { Types.VARCHAR});
		tjdbc.setRowMapper(new TalepFormAramaRowMapper());
		ArrayList<IymYaziPojo> list = (ArrayList<IymYaziPojo>)getDao().bulRowMapper(tjdbc);
		if (list != null) {
			if (list.size() > 0) {
				return list.get(0);
			}
		}
		return null;
	}

	public boolean hariciYaziOnaylandiGuncelle(HariciYazilarPojo hYazi) {
		TemelJDBC tjdbc = new TemelJDBC();
		
		String statement = "Update YAZILAR_HARICI set " +
		" durum='ONAYLANDI'," +
		" ek_sha = ?, ek_path = ?  where id=?  ";
		
		Object[] obj = { hYazi.getEkSha(),hYazi.getEkPath(),hYazi.getId()};
		int[] i = { Types.VARCHAR, Types.VARCHAR, Types.BIGINT };

		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().degistir(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	public boolean hariciYaziBasarisizGuncelle(HariciYazilarPojo hYazi) {
		TemelJDBC tjdbc = new TemelJDBC();
		
		String statement = "Update YAZILAR_HARICI set " +
		" durum='BASARISIZ' " +
		" where id=?  ";
		
		Object[] obj = { hYazi.getId()};
		int[] i = { Types.BIGINT };

		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().degistir(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	public boolean iymThreadLogEkle(ThreadPojo t) {
		TemelJDBC tjdbc = new TemelJDBC();
		System.out.println(t.getBaslamaTarihi()+ " "+t.getBitisTarihi());
		String statement = " insert into iym.iym_thread_log (id,thread_isim, thread_bas_tarih, thread_bit_tarih,thread_durum,aciklama) " +
				" values(iym_thread_log_seq.nextval,?,to_date(?,'dd.mm.YYYY HH24:MI:SS'),to_date(?,'dd.mm.YYYY HH24:MI:SS'),?,?)";
		Object[] obj = { t.getThreadName()
				,Utility.tarihFormati(t.getBaslamaTarihi(),true) 
				,Utility.tarihFormati(t.getBitisTarihi(),true)
				,t.getDurum()
				,t.getAciklama() };
		int[] i = { Types.VARCHAR, Types.VARCHAR, Types.VARCHAR, Types.VARCHAR,  Types.VARCHAR};
		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().ekle(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	public boolean iymThreadSonCalismaBul(String adi,int sure) {
		Integer sonuc  = (Integer) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						" select count(*) from iym_thread_log t where t.thread_isim = ? and t.thread_bas_tarih > sysdate - 1/24/60*?",
						new Object[] { adi,sure },
						new int[] { Types.VARCHAR,Types.BIGINT}, 
						Integer.class);
		if(sonuc.intValue()>0)
			return false;
		return true;
	}

	public class YaziDagitimListMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			IymYaziDagitimPojo dagitim = new IymYaziDagitimPojo();
			dagitim.setId(rs.getLong("id"));
			dagitim.setKurum(rs.getString("kurum"));
			dagitim.setYaziId(rs.getLong("yazi_id"));
			dagitim.setAdres(rs.getString("adres"));
			dagitim.setIlAdi(rs.getString("ilce_adi"));
			dagitim.setIlKodu(rs.getString("il_kod"));
			dagitim.setEldenTeslim(rs.getString("posta_durum"));
			try{
				dagitim.setDosyaYol(rs.getString("dosya_yol"));
			}catch(Exception ex){
				
			}
			return dagitim;
			
			
		}
	}

	public ArrayList<IymYaziDagitimPojo> dagitimBilgiListGetir(Long yaziId) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement("select y.id,y.kurum,y.yazi_id,y.adres,i.ilce_adi, i.il_kod,y.posta_durum, YF.PATH||YF.FILE_NAME  AS DOSYA_YOL  from yazi_dagitim y , iller i,YAZI_FILES YF where I.IL_KOD(+)=Y.IL_ILCE_KOD and silindi=0 and y.yazi_id=?    AND YF.DAGITIM_ID = Y.ID AND YF.Yazi_Id = Y.YAZI_ID and posta_durum='Elektronik Teslim' and y.dosya_yol is not null  order by tur,id");
		tjdbc.setData(new Object[] { yaziId });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new YaziDagitimListMapper());
		return (ArrayList<IymYaziDagitimPojo>) getDao().bulRowMapper(tjdbc);
	}

	public class EvrakGelenKurumRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			String  kurum = new String();
			kurum = rs.getString("KURUM");
			return kurum;

		}
	}
	
	public String kurumAdiGetir(String kurumKod) {
		String sql = " select t.kurum from evrak_gelen_kurumlar t WHERE t.kurum_kod = ? ";

		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(sql);
		tjdbc.setData(new Object[] { kurumKod });
		tjdbc.setType(new int[] { Types.VARCHAR});
		tjdbc.setRowMapper(new EvrakGelenKurumRowMapper());
		List<String> kurum = getDao().bulRowMapper(tjdbc);

		if (kurum != null) {
			if (kurum.size() > 0) {
				return kurum.get(0);
			}
		}
		return null;
	}
	
	public boolean evrakDahaOncedenGelmisMi(XmlEvrakPojo evr) {
		long sayi = 0;
		try {
			sayi = (Long) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForLong(
							"Select count(*) from evrak_kayit where  "
									+ " (durumu='ONAYLANDI' OR durumu='ARSIV' OR durumu='BEKLEMEDE' OR durumu is null) "
									+ " and evrak_no=?  and gel_il=? and  id != ? evrak_tarihi=to_date(?,'dd.mm.yyyy')",
							new Object[] { evr.getEvrakNo(),
									evr.getGelenIl(), evr.getId(),
									evr.getEvrakTarihi() },
							new int[] { Types.VARCHAR, Types.VARCHAR,
									Types.BIGINT, Types.VARCHAR });
		} catch (Exception e) {
			return false;
		}

		if (sayi > 0) {
			return true;
		}
		return false;
	}
	
	public XmlEvrakPojo evrakKaydet(XmlEvrakPojo evrak, Long personelId,String clientIp) {
		Connection con = null;
		try {
			con = getDao().getJdbcTemplate().getDataSource().getConnection();
			if (con == null) {
				throw new Exception("HATA  Kod : Evrak Kayıt Hatası");
			}
			
			Long evrakId = evrakIdAl();
			evrak.setId(evrakId);
		
			String evrakSiraNo = evrakSayiAl(null, evrak.getEvrakGeldigiKurum());
		
			if (evrakSiraNo == null) {
				throw new Exception("HATA  Kod : Evrak Kayıt Hatası");
			}
			
			evrak.setEvrakSiraNo(evrakSiraNo);
			con.setAutoCommit(false);
			Long islemId = Utility.xmlLogIlslemIdGetir();
			this.xmlEvrakKaydet(con, evrak, personelId, clientIp, islemId);
			con.commit();
		}catch (Exception e) {
			try {
				con.rollback();
			} catch (SQLException e11) {
			}
			e.printStackTrace();
		} finally {
			try {
				if (con != null) {
					con.setAutoCommit(true);
					con.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return evrak;

	}
	
	public List<XmlEvrakPojo> evrakListGetir(Long id) {

		String sql = "  SELECT E.ID,"
				+ "         E.EVRAK_SIRA_NO,"
				+ "         E.EVRAK_NO,"
				+ "         E.SORUSTURMA_NO,"
				+ "         K.KURUM_KOD AS GELDIGIKURUMKODU,"
				+ "         K.KURUM_ADI AS GELDIGIKURUMADI,"
				+ "         IL.IL_ADI AS GELDIGIIL, E.GEL_IL,"
				+ "         TO_CHAR (E.EVRAK_TARIHI, 'DD.MM.YYYY') AS GELENEVRAKTARIHI,"
				+ "         E.ACILMI, E.TEKITMI, E.EVRAK_KONUSU,TO_CHAR (E.GIRIS_TARIH, 'DD.MM.YYYY HH24:MM:SS') as GIRIS_TARIH ,"
				+ "         E.MAHKEME_KARAR_NO, E.ACIKLAMA, e.oncelik, e.asil_evrak, ROWNUM AS SNO"
				+ "    FROM EVRAK_KAYIT E, EVRAK_GELEN_KURUMLAR K, ILLER IL"
				+ "   WHERE E.KAY_KULLANICI = ? "
				+ "         AND E.DURUMU IS NULL "
				+ "         AND E.EVRAK_GELDIGI_KURUM = K.KURUM_KOD(+) "
				+ "         AND E.GEL_IL = IL.IL_KOD(+) "
				+ "ORDER BY ACILMI, E.GIRIS_TARIH ";

		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(sql);

		tjdbc.setData(new Object[] { id });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new GelenEvrakRowMapper());
		List<XmlEvrakPojo> list = null;
		try {
			list = getDao().bulRowMapper(tjdbc);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;

	}
	
	public class GelenEvrakRowMapper implements RowMapper {
		public XmlEvrakPojo mapRow(ResultSet rs, int arg1) throws SQLException {

			XmlEvrakPojo iym = new XmlEvrakPojo();
			iym.setSno(rs.getInt("SNO"));
			iym.setId(rs.getLong("ID"));
			iym.setEvrakSiraNo(rs.getString("EVRAK_SIRA_NO"));
			iym.setEvrakNo(rs.getString("EVRAK_NO"));
			iym.setSorusturmaNo(rs.getString("SORUSTURMA_NO"));
			iym.setEvrakGeldigiKurum(rs.getString("GELDIGIKURUMKODU"));
			iym.setEvrakGeldigiKurumAdi(rs.getString("GELDIGIKURUMADI"));
			iym.setGelenIl(rs.getString("GEL_IL"));
			iym.setGelenIlAdi(rs.getString("GELDIGIIL"));
			iym.setEvrakTarihi(rs.getString("GELENEVRAKTARIHI"));
			iym.setAcilMi(rs.getString("ACILMI"));
			iym.setEvrakKonusu(rs.getString("EVRAK_KONUSU"));
			iym.setMahkemeKararNo(rs.getString("MAHKEME_KARAR_NO"));
			iym.setAciklama(rs.getString("ACIKLAMA"));
			iym.setGirisTarih(rs.getString("GIRIS_TARIH"));
			return iym;
		}
	}
	
	
	public boolean evrakDegistir(XmlEvrakPojo evrak, Long personelId,String clientIp) {

		if (evrak == null) {
			return false;
		}

		TemelJDBC tjdbc = new TemelJDBC();

		String statement = "UPDATE evrak_kayit SET "
				+ "        evrak_no = ?, "
				+ "        evrak_tarihi = to_date(?,'dd.mm.yyyy'),     evrak_geldigi_kurum = ?,"
				+ "        kay_kullanici = ?,    aciklama = ?,"
				+ "        gel_il = ?,           evrak_konusu = ?,"
				+ "        acilmi = ?,           sorusturma_no = ?,"
				+ "        mahkeme_karar_no = ?, durumu = ?, tekitmi = ?, asil_evrak = ?, oncelik = ?"
				+ "        WHERE id = ?";

		Object[] obj = {
				evrak.getEvrakSiraNo(),
				evrak.getEvrakTarihi(), evrak.getEvrakGeldigiKurum(),
				personelId, evrak.getAciklama(),
				evrak.getGelenIl(), evrak.getEvrakKonusu(),
				evrak.getAcilMi(), evrak.getSorusturmaNo(),
				evrak.getMahkemeKararNo(), "",
				"H", "H",
				"", evrak.getId() };

		int[] i = { Types.VARCHAR, Types.VARCHAR, Types.VARCHAR, Types.BIGINT,
				Types.VARCHAR, Types.VARCHAR, Types.VARCHAR, Types.VARCHAR,
				Types.VARCHAR, Types.VARCHAR, Types.VARCHAR, Types.VARCHAR,
				Types.VARCHAR, Types.VARCHAR, Types.BIGINT };

		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			Long islemId = Utility.xmlLogIlslemIdGetir();
			Utility.xmlIslemLog(personelId, clientIp, "Evrak Degistir", "EVRAK_KAYIT", evrak.getId().toString(),islemId);
			getDao().degistir(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}
	
	public boolean evrakDosyaEkle(Long evrakId, String fileName, Long siraNo, Long personelId, String clientIp) {

		Long islemId = Utility.xmlLogIlslemIdGetir();
		Utility.xmlIslemLog(personelId, clientIp, "Evrak Dosya Ekle", "EVRAK_FILES_KAYIT", evrakId.toString(),islemId);
		
		TemelJDBC tjdbc = new TemelJDBC();
		String statement = "INSERT INTO evrak_files(ID, file_name, evrak_id, sira_no,silindi)"
				+ " VALUES (evrak_files_seq.NEXTVAL, ?, ?, ?, '0')";
		Object[] obj = { fileName, evrakId, siraNo };
		int[] i = { Types.VARCHAR, Types.BIGINT, Types.BIGINT };

		tjdbc.setStatement(statement);
		// System.out.println(tjdbc.getStatement());
		tjdbc.setData(obj);
		tjdbc.setType(i);
		getDao().ekle(tjdbc);
		return true;
	}
	
	public class EvrakEkMapper2 implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			IymFilePojo ek = new IymFilePojo();
			ek.setId(rs.getLong("id"));
			ek.setDosyaId(rs.getLong("evrak_id"));
			ek.setFileName(rs.getString("file_name"));
			ek.setSiraNo(rs.getInt("sira_no"));
			//ek.setSilindi(rs.getString("silindi"));
			return ek;
		}
	}
	
	public List<IymFilePojo> evrakDosyaGetir(Long evrakId) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(" select f.id, f.evrak_id,  replace(f.file_name,'.enc','') as file_name, f.sira_no from evrak_files f where f.evrak_id = ? and (f.SILINDI is null or f.SILINDI=0) order by f.id desc");
		tjdbc.setData(new Object[] { evrakId });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new EvrakEkMapper2());
		return getDao().bulRowMapper(tjdbc);
	}
	
	
	public IymFilePojo ekGetir(Long ekId) {
		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(" select f.id, f.evrak_id,  replace(f.file_name,'.enc','') as file_name, f.sira_no from evrak_files f where f.id = ? and (f.SILINDI is null or f.SILINDI=0) ");
		tjdbc.setData(new Object[] { ekId });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new EvrakEkMapper2());
		List<IymFilePojo> l = getDao().bulRowMapper(tjdbc);
		if(l.size()>0)
			return l.get(0);
		return null;
	}
	
	public boolean ekSil(IymFilePojo f,Long personelId,String clientIp) {
		Long islemId = Utility.xmlLogIlslemIdGetir();
		Utility.xmlIslemLog(personelId, clientIp, "Evrak Ek Sil", "EVRAK_FILES_DELETE", new Long(f.getId()).toString(),islemId);
		
		TemelJDBC tjdbc = new TemelJDBC();
		String sql = "update evrak_files f set f.silindi = '1' where id = ?";
		tjdbc.setStatement(sql);
		tjdbc.setData(new Object[] { f.getId() });
		tjdbc.setType(new int[] { Types.BIGINT });
		getDao().degistir(tjdbc);
		return true;
	}
	
	public Long evraktaKacDosyaVar(Long evrakId) {
		Long sayi  = (Long) ((TemelDAO) getDao())
				.getJdbcTemplate()
				.queryForObject(
						"SELECT nvl(max(f.sira_no),0) as sayi FROM evrak_files f  " +
                          " WHERE f.evrak_id = ?",
						new Object[] { evrakId},
						new int[] { Types.BIGINT }, Long.class);
		return sayi;
	}
	
	public void evrakTasnifeGonder(XmlEvrakPojo evrak, Long  personelId,String gorevKod,
			String aciklama) throws Exception {
		CallableStatement cs = null;
		String procedure = "{call evrak_onayla_yeni(?,?,?,?,?)}";

		try {
			cs = getDao().procedureCall(procedure);
			cs.setLong(1, evrak.getId());
			cs.setLong(2, personelId);
			cs.setString(3, gorevKod);
			cs.setString(4, aciklama);
			cs.registerOutParameter(5, Types.VARCHAR);
			cs.execute();
			String sonuc = cs.getString(5);
			if (!sonuc.equals("1")) {
				System.out
						.println("Evrak Tasnife gönderilmesi sırasında hata ile kaşıldı");
				throw new Exception(sonuc);
			}
		} catch (Exception e) {
			throw new Exception(e.getMessage());
		} finally {
			if (cs != null) {
				try {
					cs.close();
				} catch (Exception e) {
				}
			}
		}

	}
	
	public boolean evrakSil(Long evrakId) {
		TemelJDBC tjdbc = new TemelJDBC();

		String sql = "update evrak_kayit set durumu = 'SILINDI' where id = ?";
		tjdbc.setStatement(sql);
		tjdbc.setData(new Object[] { evrakId });
		tjdbc.setType(new int[] { Types.BIGINT });

		try {
			getDao().degistir(tjdbc);
		} catch (Exception e) {
			return false;
		}

		// sql = "update evrak_files f set f.silindi = '1' where evrak_id = ?";
		// tjdbc.setStatement(sql);
		// tjdbc.setData(new Object[] { evrakId });
		// tjdbc.setType(new int[] { Types.BIGINT });
		//
		// try {
		// getDao().degistir(tjdbc);
		// } catch (Exception e) {
		// return false;
		// }

		return true;

	}
	
	public class HedeflerTalepRowMapper implements RowMapper {

		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			
			HedeflerTalepPojo hedef = new HedeflerTalepPojo();
			
			hedef.setId(rs.getLong("id"));		
			hedef.setBaslamaTarihi(TemelIslemler.TurkZamanNullCheck(rs.getDate("baslama_tarihi")));
			hedef.setKayitTarihi(TemelIslemler.TurkZamanNullCheck(rs.getDate("kayit_Tarihi")));	
			hedef.setBirimKod(rs.getString("birim_kod"));	
			hedef.setKullaniciId(rs.getLong("kullanici_id"));	
			hedef.setTekMasaKulId(rs.getString("hedef_no"));
			hedef.setHedefNo(rs.getString("hedef_no"));
			hedef.setHedefTipi(rs.getString("hedef_tipi"));
			hedef.setHedefAdi(rs.getString("hedef_adi"));
			hedef.setHedefSoyadi(rs.getString("hedef_soyadi"));
			hedef.setSuresi(rs.getString("suresi"));
			hedef.setSureTipi(rs.getString("sure_tipi"));	
			hedef.setUzatmaSayisi(rs.getString("uzatma_sayisi"));	
			hedef.setDurumu(rs.getString("durumu"));
			hedef.setAciklama(rs.getString("aciklama"));
			hedef.setMahkemeKararId(rs.getLong("mahkeme_karar_id"));	
			hedef.setGrupKod(rs.getString("grup_kod"));
			hedef.setAidiyatKod(rs.getString("aidiyat_kod"));
			hedef.setUniqKod(rs.getString("uniq_kod"));
			hedef.setKapatmaKararId(rs.getString("kapatma_karar_id"));
			hedef.setUzatmaId(rs.getString("uzatma_id"));
			hedef.setHedef118Adi(rs.getString("hedef_118_adi"));
			hedef.setHedef118Soyadi(rs.getString("hedef_118_soyadi"));
			hedef.setHedef118Adres(rs.getString("hedef_118_adres"));

			return hedef;
		}

	}
	
	
	public ArrayList<HedeflerTalepPojo> hedeflerTalepGetirByMahkemeKararId(Long mahkemeKararId)
	{

		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(
				"SELECT  h.ID, "+
	            "h.BIRIM_KOD, "+
	            "h.KULLANICI_ID, "+
	            "h.TEK_MASA_KUL_ID, "+
	            "h.HEDEF_NO, "+
	            "h.HEDEF_TIPI, "+
	            "h.HEDEF_ADI, "+
	            "h.HEDEF_SOYADI, "+
	            "h.BASLAMA_TARIHI, "+
	            "h.SURESI, "+
	            "h.SURE_TIPI, "+
	            "h.UZATMA_SAYISI, "+
	            "h.DURUMU, "+
	            "h.ACIKLAMA, "+
	            "h.MAHKEME_KARAR_ID, "+
	            "h.HEDEF_AIDIYAT_ID, "+
	            "h.GRUP_KOD, "+
	            "h.AIDIYAT_KOD, "+
	            "h.UNIQ_KOD, "+
	            "h.KAYIT_TARIHI, "+
	            "h.TANIMLAMA_TARIHI, "+
	            "h.KAPATMA_KARAR_ID, "+
	            "h.KAPATMA_TARIHI, "+
	            "h.IMHA, "+
	            "h.IMHA_TARIHI, "+
	            "h.UZATMA_ID, "+
	            "h.DURUMU, "+
	            "h.ACILMI, "+
	            "h.HEDEF_118_ADI, "+
	            "h.HEDEF_118_SOYADI, "+
	            "h.HEDEF_118_ADRES "+
	        "FROM hedefler_talep h "+
	        "WHERE  h.mahkeme_karar_id = ?");
				
		tjdbc.setData(new Object[] { mahkemeKararId });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new HedeflerTalepRowMapper());
		return (ArrayList<HedeflerTalepPojo>)getDao().bulRowMapper(tjdbc);
			
	}

	public boolean hedeflerTalepGuncelle(Long hedefId, String adi, String soyadi,String adresi) {
		TemelJDBC tjdbc = new TemelJDBC();

		String sql = "update hedefler_talep set hedef_118_adi = ?,hedef_118_soyadi=?,hedef_118_adres = ? where id = ?";
		tjdbc.setStatement(sql);
		tjdbc.setData(new Object[] {adi,soyadi,adresi, hedefId });
		tjdbc.setType(new int[] { Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.BIGINT });

		try {
			getDao().degistir(tjdbc);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
	
	public boolean hedefler118CanliAlanGuncelle(Long hedefId, String adi, String soyadi,String adresi) {
		TemelJDBC tjdbc = new TemelJDBC();

		String sql = "update hedefler set hedef_118_adi = ?,hedef_118_soyadi=?,hedef_118_adres = ? where id = ?";
		tjdbc.setStatement(sql);
		tjdbc.setData(new Object[] {adi,soyadi,adresi, hedefId });
		tjdbc.setType(new int[] { Types.VARCHAR,Types.VARCHAR,Types.VARCHAR,Types.BIGINT });

		try {
			getDao().degistir(tjdbc);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
	
		public class NumaraImeiSorguLogGetirRowMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			NumaraImeiSorguLogPojo p = new NumaraImeiSorguLogPojo();
			p.setKullaniciId(rs.getLong("kullanici_id"));
			p.setIp(rs.getString("ip"));
			p.setAciklama(rs.getString("aciklama"));
			p.setIslemTarihi(rs.getTimestamp("sorgu_tarihi"));
			p.setBaslangicTarihi(rs.getTimestamp("baslangic_tarihi"));
			p.setBitisTarihi(rs.getTimestamp("bitis_tarihi"));
			p.setSorguTur(rs.getString("sorgu_turu"));
			p.setHedefNo(rs.getString("aranan"));
			return p;
		}
	}

	public List<NumaraImeiSorguLogPojo> numaraImeiSorguLogGetir(
			Long kullaniciId, Date baslangic, Date bitis, String tur) {

		String kosul = "";
		if ("IMEI-NUMARA-LOG".equals(tur)) {
			kosul = " and sorgu_turu = 'IMEI-NUMARA'";
		} else if ("NUMARA-IMEI-LOG".equals(tur)) {
			kosul = " and sorgu_turu = 'NUMARA-IMEI'";
		} else if ("IMEI-NUMARA-LOG-HEPSI".equals(tur)) {
			kosul = " and (sorgu_turu = 'NUMARA-IMEI' or sorgu_turu = 'IMEI-NUMARA')";
		} else {
			kosul = " and 1=0 ";
		}


		String sql = "select kullanici_id, ip, aciklama, sorgu_tarihi, baslangic_tarihi, bitis_tarihi, sorgu_turu, aranan from IMEI_SORGU_LOG "
				+ "      where kullanici_id = ? and sorgu_tarihi between ? and ? "
				+ kosul + " order by id ";

		TemelJDBC tjdbc = new TemelJDBC();


		tjdbc.setStatement(sql);
		tjdbc.setData(new Object[] { kullaniciId, baslangic, bitis });
		tjdbc.setType(new int[] { Types.BIGINT, Types.DATE, Types.DATE });
		tjdbc.setRowMapper(new NumaraImeiSorguLogGetirRowMapper());


		return getDao().bulRowMapper(tjdbc);




	}

	public String veritabaniZamaniGetir() {
		String zaman = "";
	
			 zaman  = (String) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							"select to_char(sysdate,'dd/mm/yyyy hh24:mi:ss') from dual",
							new Object[] { },
							new int[] { }, String.class);
	
		return zaman;
	}
	
	
	public boolean canli118LogEkle(Canli118LogPojo canli118) {
		TemelJDBC tjdbc = new TemelJDBC();
		
		String statement = "INSERT INTO IYM.CANLI_118_TEST_LOG(ID,HEDEF_NO, ESKI_HEDEF_118_ADI,ESKI_HEDEF_118_SOYADI,"
			   + " ESKI_HEDEF_118_ADRES, YENI_HEDEF_118_ADI, YENI_HEDEF_118_SOYADI,YENI_HEDEF_118_ADRES, ESKI_CALL_DATE, YENI_CALL_DATE)"
			   + "VALUES(CANLI_118_TEST_LOG_SEQ.nextval,?,?,?,?,?,?,?,?,?)";
		
		
		Object[] dataArr = { 
				canli118.getHedefNo(),
				canli118.getEskiHedef118Adi(), canli118.getEskiHedef118Soyadi(), canli118.getEskiHedef118Adres()
				,canli118.getYeniHedef118Adi(), canli118.getYeniHedef118Soyadi(), canli118.getYeniHedef118Adres()
				,canli118.getEskiCallDate()
				,canli118.getYeniCallDate()
				};
		
		int[] i = {Types.VARCHAR, Types.VARCHAR, Types.VARCHAR, Types.VARCHAR,  Types.VARCHAR, Types.VARCHAR,  Types.VARCHAR, Types.VARCHAR, Types.VARCHAR};
		tjdbc.setStatement(statement);
		tjdbc.setData(dataArr);
		tjdbc.setType(i);
		try {
			getDao().ekle(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}

		
	}
	
	
	//Rowmapper
	//Rowmapper
	public class EvrakDurumSorguMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			EvrakDurumSorguPojo evrak  = new EvrakDurumSorguPojo();
			evrak.setEvrakSiraNo(rs.getString("EVRAK_SIRA_NO"));
			evrak.setEvrakNo(rs.getString("EVRAK_NO"));
			evrak.setDurumu(rs.getString("DURUMU"));
			evrak.setGirisTarihi(rs.getString("KAYIT_TARIHI"));
			evrak.setAciklama(rs.getString("ACIKLAMA"));
			return evrak;
		}
	}	
	//İade durumunda evrakın durum sorgulaması yapilabilsin.
		public List<EvrakDurumSorguPojo> evrakDetayByKullaniciIdEvrakNo(String evrakNo,Long kullaniciId,String kullaniciAdi) {
			TemelJDBC tjdbc = new TemelJDBC();
			tjdbc.setStatement(" SELECT  ev.evrak_sira_no as EVRAK_SIRA_NO, ev.evrak_no as EVRAK_NO,"+
					"               CASE "+
					"                 WHEN (MK.KARAR_TIP IN (510, 520, 530) AND "+
					"                      (SELECT COUNT(*) "+
					"                          FROM MAHKEME_KARAR K "+
					"                         WHERE MK.ID = K.ID "+
					"                           AND K.DURUM = 'ONAYLANDI') > 0) THEN "+
					"                  'TANIMLANDI' "+
					" 				  WHEN (EV.DURUMU IS NULL and EV.ACIKLAMA like '%İADE%' ) THEN "+
					" 				    'İADE' "+
					"                 WHEN (SELECT COUNT(*) "+
					"                         FROM HEDEFLER H1, MAHKEME_KARAR MR "+
					"                        WHERE H1.MAHKEME_KARAR_ID = MR.ID "+
					"                          AND MR.ID = MK.ID "+
					"                          AND (H1.DURUMU IS NULL OR "+
					"                              H1.DURUMU = 'TANIMLANAMADI')) > 0 OR "+
					"                      (SELECT COUNT(*) "+
					"                         FROM HEDEFLER H1, MAHKEME_KARAR MR "+
					"                        WHERE H1.MAHKEME_KARAR_ID = MR.ID "+
					"                          AND MR.ID = MK.ID "+
					"                          AND H1.DURUMU = 'TANIMLANDI') = 0 THEN "+
					"                  'İŞLEMDE' "+
					"                 WHEN (SELECT COUNT(*) "+
					"                         FROM HEDEFLER H1, MAHKEME_KARAR MR "+
					"                        WHERE H1.MAHKEME_KARAR_ID = MR.ID "+
					"                          AND MR.ID = MK.ID "+
					"                          AND (H1.DURUMU IS NULL OR "+
					"                              H1.DURUMU = 'TANIMLANAMADI')) = 0 OR "+
					"                      (SELECT COUNT(*) "+
					"                         FROM HEDEFLER H1, MAHKEME_KARAR MR "+
					"                        WHERE H1.MAHKEME_KARAR_ID = MR.ID "+
					"                          AND MR.ID = MK.ID "+
					"                          AND H1.DURUMU = 'TANIMLANDI') > 0 THEN "+
					"                  'TANIMLANDI' "+
					"               END AS DURUMU," +
					"				EV.GIRIS_TARIH AS KAYIT_TARIHI, EV.ACIKLAMA  "+
					"          FROM EVRAK_KAYIT EV, MAHKEME_KARAR MK "+
					"         WHERE  "+
					"                 EV.ID = MK.EVRAK_ID(+) "+
					"           AND NVL(EV.DURUMU, 'X') != 'SILINDI' "+
					"           AND EV.EVRAK_GELDIGI_KURUM IN "+
					"               (SELECT EK.KURUM_KOD "+
					"                  FROM EVRAK_GELEN_KURUMLAR EK, KULLANICI_KURUM KK "+
					"                 WHERE EK.KURUM_KOD = KK.KURUM_KOD "+
					"                   AND KK.KULLANICI_ID = ? ) "+
					"           AND EV.KAY_KULLANICI IN "+
					"               (SELECT ID "+
					"                  FROM KULLANICILAR "+
					"                 WHERE TEMSIL_EDILEN_KURUM IS NOT NULL) "+
					"           AND EV.EVRAK_NO = ? "+
					"         ORDER BY EV.ID DESC ");
			System.out.println(tjdbc.getStatement());
			tjdbc.setData(new Object[] {kullaniciId, evrakNo});
			tjdbc.setType(new int[] {Types.BIGINT,Types.VARCHAR});
			tjdbc.setRowMapper(new EvrakDurumSorguMapper());
			List<EvrakDurumSorguPojo>  list =  getDao().bulRowMapper(tjdbc);
			if(list == null){
				list = new ArrayList<EvrakDurumSorguPojo>();
			}
			
			//Hukuk tarafindan onaylanmamis yeni gelen taleplerin durumunu goster.
			List<EvrakDurumSorguPojo> talepList = evrakDetayTalepByKullaniciIdEvrakNo(evrakNo,kullaniciId, kullaniciAdi);
			if(talepList != null  && talepList.size() > 0){
				list.addAll(talepList);
			}
			
			/*************LOG KAYDET*******************************/
			IymEvrakAramaPojo evrak = new IymEvrakAramaPojo();
			evrak.setEvrakNo(evrakNo);
			evrakAramaLogKaydet(evrak, null, null, kullaniciId, "EVRAK_SORGU", kullaniciAdi);
			/******************************************************/
			return list;
		}

		//Evrak : 000.01.01-2021.080452 Redmine #1593
		private List<EvrakDurumSorguPojo> evrakDetayTalepByKullaniciIdEvrakNo(String evrakNo,Long kullaniciId,String kullaniciAdi) {
			TemelJDBC tjdbc = new TemelJDBC();
			tjdbc.setStatement(" SELECT  ev.evrak_sira_no as EVRAK_SIRA_NO, ev.evrak_no as EVRAK_NO, 'TASNIF' AS DURUMU," +
					"			EV.GIRIS_TARIH AS KAYIT_TARIHI, EV.ACIKLAMA  "+
					"          FROM EVRAK_KAYIT EV, MAHKEME_KARAR_TALEP MK "+
					"         WHERE  "+
					"                 EV.ID = MK.EVRAK_ID(+) "+
					"           AND EV.EVRAK_GELDIGI_KURUM IN "+
					"               (SELECT EK.KURUM_KOD "+
					"                  FROM EVRAK_GELEN_KURUMLAR EK, KULLANICI_KURUM KK "+
					"                 WHERE EK.KURUM_KOD = KK.KURUM_KOD "+
					"                   AND KK.KULLANICI_ID = ? ) "+
					"           AND EV.KAY_KULLANICI IN "+
					"               (SELECT ID "+
					"                  FROM KULLANICILAR "+
					"                 WHERE TEMSIL_EDILEN_KURUM IS NOT NULL) "+
					"           AND EV.EVRAK_NO = ? "+
					" AND MK.EVRAK_ID NOT IN(SELECT EVRAK_ID FROM MAHKEME_KARAR)" +
					"         ORDER BY EV.ID DESC ");
			System.out.println(tjdbc.getStatement());
			tjdbc.setData(new Object[] {kullaniciId,evrakNo});
			tjdbc.setType(new int[] {Types.BIGINT,Types.VARCHAR});
			tjdbc.setRowMapper(new EvrakDurumSorguMapper());
			List<EvrakDurumSorguPojo>  list =  getDao().bulRowMapper(tjdbc);
			
			if(list == null){
				list = new ArrayList<EvrakDurumSorguPojo>();
			}
			
			/*************LOG KAYDET*******************************/
			IymEvrakAramaPojo evrak = new IymEvrakAramaPojo();
			evrak.setEvrakNo(evrakNo);
			evrakAramaLogKaydet(evrak, null, null, kullaniciId, "MAHKEME KARAR TALEP SORGU", kullaniciAdi);
			/******************************************************/
			return list;
		}		
		
	
	//Rowmapper
	public class HedefDurumDetayMapper implements RowMapper {
		public Object mapRow(ResultSet rs, int arg1) throws SQLException {
			HedefDurumPojo hedef  = new HedefDurumPojo();
			hedef.setHedefNo(rs.getString("HEDEF_NO"));
			hedef.setHedefTipi(rs.getString("HEDEF_TIPI"));
			hedef.setDurumu(rs.getString("DURUMU"));
			hedef.setAciklama(rs.getString("ACIKLAMA"));
			hedef.setMahkemeKararNo(rs.getString("MAHKEME_KARAR_NO"));
			hedef.setSorusturmaNo(rs.getString("SORUSTURMA_NO"));
			return hedef;
		}
	}	
	
	public List<HedefDurumPojo> hedefDurumSorguBy(String evrakNo, Long kullaniciId,String kullaniciAdi) {
		TemelJDBC tjdbc = new TemelJDBC();
		
		String sql = "Select  HDF.HEDEF_NO, HDF.HEDEF_TIPI, NVL(HDF.DURUMU, 'ISLEMDE') AS DURUMU, MK.MAHKEME_KARAR_NO, MK.SORUSTURMA_NO "+ 
		          " FROM EVRAK_KAYIT EV, MAHKEME_KARAR MK, HEDEFLER HDF  "+
		          " WHERE "+  
		          " Ev.Id = Mk.Evrak_Id "+
		          " AND MK.ID = HDF.MAHKEME_KARAR_ID "+
		          " AND NVL(EV.DURUMU, 'X') != 'SILINDI' "+ 
		          " AND EV.EVRAK_GELDIGI_KURUM IN  "+
		          "      (SELECT EK.KURUM_KOD  FROM EVRAK_GELEN_KURUMLAR EK, KULLANICI_KURUM KK  Where Ek.Kurum_Kod = Kk.Kurum_Kod "+ 
		          "      And Kk.Kullanici_Id = ? )  "+
		          " AND EV.Kay_Kullanici In (SELECT ID  FROM KULLANICILAR Where Temsil_Edilen_Kurum Is Not Null) "+ 
		          " AND EV.Evrak_No = ? ";
		         
		tjdbc.setStatement(sql);
		System.out.println(tjdbc.getStatement());
		tjdbc.setData(new Object[] {kullaniciId,evrakNo});
		tjdbc.setType(new int[] {Types.BIGINT,Types.VARCHAR});
		tjdbc.setRowMapper(new HedefDurumDetayMapper());
		
		List<HedefDurumPojo>  list =  getDao().bulRowMapper(tjdbc);
		
		/*************LOG KAYDET*******************************/
		IymEvrakAramaPojo evrak = new IymEvrakAramaPojo();
		evrak.setEvrakNo(evrakNo);
		evrakAramaLogKaydet(evrak, null, null, kullaniciId, "HEDEF_SORGU", kullaniciAdi);
		/******************************************************/
		return list;
		
		
	}
	
	
	public  boolean webServicekKullaniciYetkiKontrol(String kullaniciAdi, String serviceName){
		
		boolean result = false;
		
		String sql = "select count(*) from iym.kullanici_webservice_yetki kws " +
		" inner join iym.kullanicilar  kul on kul.kullanici_adi = kws.kullanici_adi " +
		" where " +
		" kul.durumu='A' " + 
		" and kws.aktif = 1 " +
		" and kws.kullanici_adi=? " +
		" and kws.servis_adi=? ";

		
		Integer count = 0;
		try{
			count = (Integer) ((TemelDAO) getDao())
					.getJdbcTemplate()
					.queryForObject(
							sql,
							new Object[] { kullaniciAdi, serviceName },
							new int[] { Types.VARCHAR, Types.VARCHAR }, Integer.class);
		}catch(Exception ex){
			count = 0;
			logger.error("webServicekKullaniciYetkiKontrol Hatasi ", ex);
		}
		
		result = count > 0;
		
		
		return result;
		
	}

	
	
	
	public ArrayList<HedeflerTalepPojo> kismiIadeHedefleriGetir(Long evrak_id)
	{

		TemelJDBC tjdbc = new TemelJDBC();
		tjdbc.setStatement(
				"SELECT h.* FROM hedefler_talep h, MAHKEME_KARAR_TALEP mk "+	        
	        "WHERE  mk.id=h.mahkeme_karar_id and  h.durumu='IADE' and mk.evrak_id = ?  ");
				
		tjdbc.setData(new Object[] { evrak_id });
		tjdbc.setType(new int[] { Types.BIGINT });
		tjdbc.setRowMapper(new HedeflerTalepRowMapper());
		return (ArrayList<HedeflerTalepPojo>)getDao().bulRowMapper(tjdbc);
			
	}
	
	
	public boolean kismiIadeEvrakListedenKaldir(Long evrakId, long personelIymId,String clientIp) {
		
		TemelJDBC tjdbc = new TemelJDBC();

		String statement = "Update EVRAK_KAYIT set durumu='ONAYLANDI' where id=? and DURUMU='KISMI_IADE'";
		Object[] obj = { evrakId };
		int[] i = { Types.BIGINT };

		tjdbc.setStatement(statement);
		tjdbc.setData(obj);
		tjdbc.setType(i);
		try {
			getDao().degistir(tjdbc);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		
	}
		         
	
}
