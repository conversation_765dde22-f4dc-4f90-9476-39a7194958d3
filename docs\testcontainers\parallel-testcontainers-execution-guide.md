# Parallel TestContainers Execution Guide

This guide explains how to run TestContainers-based integration tests in parallel for improved test execution performance.

## Overview

The parallel execution setup enables multiple TestContainers integration test classes to run concurrently, each with its own isolated Oracle container instance. This approach provides:

- **Complete Test Isolation**: Each test class gets its own container
- **Improved Performance**: Tests run in parallel instead of sequentially
- **Resource Efficiency**: Optimized container and connection pool settings
- **Thread Safety**: No shared state between test classes

## Configuration Files

### 1. JUnit Platform Configuration (`junit-platform.properties`)

```properties
# Enable parallel execution
junit.jupiter.execution.parallel.enabled=true
junit.jupiter.execution.parallel.mode.default=concurrent
junit.jupiter.execution.parallel.mode.classes.default=concurrent

# Configure thread pool
junit.jupiter.execution.parallel.config.strategy=dynamic
junit.jupiter.execution.parallel.config.dynamic.factor=0.5

# Timeouts for TestContainers
junit.jupiter.execution.timeout.default=10m
junit.jupiter.execution.timeout.testable.method.default=5m
```

### 2. Maven Surefire Plugin Configuration

```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-surefire-plugin</artifactId>
    <version>3.0.0</version>
    <configuration>
        <parallel>classes</parallel>
        <threadCount>2</threadCount>
        <perCoreThreadCount>true</perCoreThreadCount>
        
        <argLine>
            -Dtestcontainers.reuse.enable=false
            -Djunit.jupiter.execution.parallel.enabled=true
            -Xmx2g
            -XX:+UseG1GC
        </argLine>
    </configuration>
</plugin>
```

### 3. TestContainers Properties

```properties
# Disable container reuse for complete isolation
testcontainers.reuse.enable=false
testcontainers.parallel.enabled=true

# Optimized connection pool settings
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.keepalive-time=300000
```

## Implementation Details

### AbstractOracleTestContainer Changes

The base test container class has been modified to support parallel execution:

1. **Container Reuse Disabled**: Static containers with reuse disabled for complete isolation
2. **Per-Class Schema Loading**: Each test class loads its own schema
3. **Unique Connection Pools**: Each test class gets unique HikariCP pool names
4. **Thread-Safe Initialization**: Proper synchronization for parallel container access

### Test Class Annotations

Test classes must include the parallel execution annotation:

```java
@Execution(ExecutionMode.CONCURRENT)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
public class MyIntegrationTest extends AbstractOracleTestContainer {
    // Test implementation
}
```

## Running Parallel Tests

### Command Line Execution

```bash
# Run all tests with parallel execution
mvn test

# Run specific integration tests
mvn test -Dtest="*IntegrationTest"

# Run with custom thread count
mvn test -Djunit.jupiter.execution.parallel.config.fixed.parallelism=3
```

### IDE Execution

Most modern IDEs (IntelliJ IDEA, Eclipse) will automatically detect and use the JUnit platform configuration for parallel execution.

## Performance Considerations

### Resource Usage

- **Memory**: Each container requires ~512MB-1GB RAM
- **CPU**: Parallel execution benefits from multi-core systems
- **Disk**: Each container creates temporary files

### Recommended Settings

- **Thread Count**: 2-4 threads for most systems
- **Memory**: Minimum 4GB RAM for 2 parallel containers
- **Timeout**: 10 minutes for container startup

### Monitoring

Monitor system resources during parallel test execution:

```bash
# Monitor memory usage
docker stats

# Monitor container count
docker ps | grep oracle

# Check test execution logs
tail -f target/surefire-reports/*.txt
```

## Troubleshooting

### Common Issues

1. **Out of Memory**: Reduce thread count or increase JVM heap size
2. **Port Conflicts**: TestContainers automatically handles port allocation
3. **Container Startup Timeout**: Increase timeout values in configuration
4. **Database Connection Issues**: Check connection pool settings

### Debug Configuration

Add debug logging to troubleshoot issues:

```properties
# Enable debug logging
logging.level.org.testcontainers=DEBUG
logging.level.com.zaxxer.hikari=DEBUG
logging.level.iym.common.testcontainer=DEBUG
```

## Best Practices

1. **Test Isolation**: Ensure tests don't depend on shared state
2. **Resource Cleanup**: Use `@DirtiesContext` for proper cleanup
3. **Timeout Configuration**: Set appropriate timeouts for your environment
4. **Monitoring**: Monitor resource usage during test execution
5. **Gradual Rollout**: Start with 2 threads and increase gradually

## Migration from Sequential Execution

To migrate existing tests:

1. Add `@Execution(ExecutionMode.CONCURRENT)` to test classes
2. Ensure tests are stateless and don't share resources
3. Update any static test data or fixtures
4. Test thoroughly with parallel execution enabled

## Example Test Class

```java
@DataJpaTest
@Import(OracleTestContainerConfiguration.class)
@Testcontainers
@ActiveProfiles("testcontainers-oracle")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@Execution(ExecutionMode.CONCURRENT)
@DisplayName("My Integration Test")
public class MyIntegrationTest extends AbstractOracleTestContainer {
    
    @Test
    void shouldExecuteInParallel() {
        // Test implementation
    }
}
```

This setup provides a robust foundation for parallel TestContainers execution while maintaining test isolation and reliability.
