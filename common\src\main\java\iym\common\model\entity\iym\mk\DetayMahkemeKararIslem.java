package iym.common.model.entity.iym.mk;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Entity class for DMAHKEME_KARAR_ISLEM table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "DMahkemeKararIslem")
@Table(name = "DMAHKEME_KARAR_ISLEM")
public class DetayMahkemeKararIslem implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "DMAHKEME_KARAR_ISLEM_SEQ")
    @SequenceGenerator(name = "DMAHKEME_KARAR_ISLEM_SEQ", sequenceName = "DMAHKEME_KARAR_ISLEM_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "MAHKEME_KARAR_ID", nullable = false)
    @NotNull
    private Long mahkemeKararTalepId;

    @Column(name = "EVRAK_ID", nullable = false)
    @NotNull
    private Long evrakId;

    @Column(name = "KULLANICI_ID", nullable = false)
    @NotNull
    private Long kullaniciId;

    @Column(name = "KAYIT_TARIHI", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date kayitTarihi;

    @Column(name = "DURUM", length = 20)
    @Size(max = 20)
    private String durum;

    @Column(name = "KARAR_TIP_DETAY", length = 20)
    @Size(max = 20)
    private String kararTipDetay;

    @Column(name = "MAHKEME_KODU_DETAY", length = 10)
    @Size(max = 10)
    private String mahkemeKodu;

    @Column(name = "MAHKEME_ADI_DETAY", length = 250)
    @Size(max = 250)
    private String mahkemeAdiDetay;

    @Column(name = "MAHKEME_KARAR_NO_DETAY", length = 50)
    @Size(max = 50)
    private String mahkemeKararNo;

    @Column(name = "MAHKEME_ILI_DETAY", length = 50)
    @Size(max = 50)
    private String mahkemeIlIlceKodu;

    @Column(name = "SORUSTURMA_NO_DETAY", length = 50)
    @Size(max = 50)
    private String sorusturmaNo;

    @Column(name = "ILISKILI_MAHKEME_KARAR_ID")
    @NotNull
    private Long iliskiliMahkemeKararId;

    @Column(name = "ACIKLAMA_DETAY", length = 500)
    @Size(max = 500)
    private String aciklama;


}
