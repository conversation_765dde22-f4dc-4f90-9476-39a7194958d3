package iym.common.model.entity.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Entity class for EVRAK_KAYIT table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "EvrakSiraNo")
@Table(name = "EVRAK_SIRANO")
public class EvrakSiraNo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "EVRAK_SIRANO_SEQ")
    @SequenceGenerator(name = "EVRAK_SIRANO_SEQ", sequenceName = "EVRAK_SIRANO_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "YIL")
    private Long yil;

    @Column(name = "SIRA_NO")
    private Long siraNo;

}
