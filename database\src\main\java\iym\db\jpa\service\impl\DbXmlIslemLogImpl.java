package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.EvrakKayit;
import iym.common.model.entity.iym.XmlIslemLog;
import iym.common.service.db.DbEvrakKayitService;
import iym.common.service.db.DbXmlIslemLog;
import iym.db.jpa.dao.EvrakKayitRepo;
import iym.db.jpa.dao.XmlIslemLogRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * Service implementation for DbXmlIslemLog entity
 */
@Service
public class DbXmlIslemLogImpl extends GenericDbServiceImpl<XmlIslemLog, Long> implements DbXmlIslemLog {

    private final XmlIslemLogRepo xmlIslemLogRepo;

    @Autowired
    public DbXmlIslemLogImpl(XmlIslemLogRepo repository) {
        super(repository);
        this.xmlIslemLogRepo = repository;
    }
}
