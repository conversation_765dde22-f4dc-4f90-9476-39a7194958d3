import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';

// PrimeNG Imports
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextarea } from 'primeng/inputtextarea';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { ToastModule } from 'primeng/toast';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { FileUploadModule } from 'primeng/fileupload';
import { MessageService } from 'primeng/api';

// Models
import { 
  MahkemeKararRequest, 
  EvrakDetay, 
  MahkemeKararBilgisi,
  MahkemeKararDetay,
  KararTuru,
  EvrakTuru,
  MahkemeKararTip 
} from '../shared/models/iym.models';

// Services
import { TalepService } from '../shared/services/talep.service';

@Component({
  selector: 'app-genel-talep-gonder',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CardModule,
    ButtonModule,
    InputTextModule,
    InputTextarea,
    DropdownModule,
    CalendarModule,
    CheckboxModule,
    ToastModule,
    ProgressSpinnerModule,
    FileUploadModule
  ],
  providers: [MessageService],
  templateUrl: './genel-talep-gonder.component.html',
  styleUrls: ['./genel-talep-gonder.component.scss']
})
export class GenelTalepGonderComponent implements OnInit {
  
  talepForm: FormGroup;
  yukleniyor = false;
  seciliDosya: File | null = null;

  // Dropdown Options
  kararTuruOptions = [
    { label: 'İletişimin Denetlenmesi Yeni Karar', value: KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR },
    { label: 'İletişimin Denetlenmesi Uzatma Kararı', value: KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI },
    { label: 'İletişimin Denetlenmesi Sonlandırma Kararı', value: KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI },
    { label: 'İletişimin Tespiti', value: KararTuru.ILETISIMIN_TESPITI },
    { label: 'Genel Evrak', value: KararTuru.GENEL_EVRAK }
  ];

  evrakTuruOptions = [
    { label: 'İletişimin Denetlenmesi', value: EvrakTuru.ILETISIMIN_DENETLENMESI },
    { label: 'İletişimin Tespiti', value: EvrakTuru.ILETISIMIN_TESPITI }
  ];

  mahkemeKararTipOptions = [
    { label: 'Önleyici Hakim Kararı', value: MahkemeKararTip.ONLEYICI_HAKIM_KARARI },
    { label: 'Adli Hakim Kararı', value: MahkemeKararTip.ADLI_HAKIM_KARARI },
    { label: 'Adli Yazılı Emir', value: MahkemeKararTip.ADLI_YAZILI_EMIR },
    { label: 'Önleyici Sonlandırma', value: MahkemeKararTip.ONLEYICI_SONLANDIRMA },
    { label: 'Adli Sonlandırma', value: MahkemeKararTip.ADLI_SONLANDIRMA }
  ];

  constructor(
    private fb: FormBuilder,
    private talepService: TalepService,
    private messageService: MessageService,
    private router: Router
  ) {
    this.talepForm = this.createForm();
  }

  ngOnInit(): void {
    // Component initialization
  }

  private createForm(): FormGroup {
    return this.fb.group({
      // Base MahkemeKararRequest fields
      kararTuru: [null, Validators.required],
      
      // EvrakDetay fields
      evrakNo: ['', [Validators.required, Validators.maxLength(50)]],
      evrakTarihi: [new Date(), Validators.required],
      evrakKurumKodu: ['', Validators.required],
      evrakTuru: [null, Validators.required],
      havaleBirimi: [''],
      evrakAciklama: [''],
      geldigiIlIlceKodu: ['', Validators.required],
      acilmi: [false],
      evrakKonusu: [''],

      // MahkemeKararBilgisi fields
      mahkemeKararTipi: [null, Validators.required],
      
      // MahkemeKararDetay fields
      mahkemeKodu: ['', Validators.required],
      mahkemeKararNo: ['', Validators.required],
      mahkemeIlIlceKodu: ['', Validators.required],
      sorusturmaNo: [''],
      mahkemeAciklama: ['']
    });
  }

  onDosyaSecildi(event: any): void {
    const dosyalar = event.files;
    if (dosyalar && dosyalar.length > 0) {
      this.seciliDosya = dosyalar[0];
      this.messageService.add({
        severity: 'info',
        summary: 'Dosya Seçildi',
        detail: `${this.seciliDosya?.name} dosyası seçildi`
      });
    }
  }

  onSubmit(): void {
    if (this.talepForm.valid) {
      this.yukleniyor = true;
      
      const formData = this.talepForm.value;
      
      // Create request object
      const request: MahkemeKararRequest = {
        id: this.generateUUID(),
        kararTuru: formData.kararTuru,
        evrakDetay: {
          evrakNo: formData.evrakNo,
          evrakTarihi: formData.evrakTarihi.toISOString(),
          evrakKurumKodu: formData.evrakKurumKodu,
          evrakTuru: formData.evrakTuru,
          havaleBirimi: formData.havaleBirimi,
          aciklama: formData.evrakAciklama,
          geldigiIlIlceKodu: formData.geldigiIlIlceKodu,
          acilmi: formData.acilmi,
          evrakKonusu: formData.evrakKonusu
        },
        mahkemeKararBilgisi: {
          mahkemeKararTipi: formData.mahkemeKararTipi,
          mahkemeKararDetay: {
            mahkemeKodu: formData.mahkemeKodu,
            mahkemeKararNo: formData.mahkemeKararNo,
            mahkemeIlIlceKodu: formData.mahkemeIlIlceKodu,
            sorusturmaNo: formData.sorusturmaNo,
            aciklama: formData.mahkemeAciklama
          }
        }
      };

      // Send request
      this.talepService.genelTalepGonder(request, this.seciliDosya).subscribe({
        next: (response) => {
          this.yukleniyor = false;
          this.messageService.add({
            severity: 'success',
            summary: 'Başarılı',
            detail: 'Talep başarıyla gönderildi'
          });
          this.talepForm.reset();
          this.seciliDosya = null;
        },
        error: (error) => {
          this.yukleniyor = false;
          this.messageService.add({
            severity: 'error',
            summary: 'Hata',
            detail: 'Talep gönderilirken hata oluştu: ' + error.message
          });
        }
      });
    } else {
      this.messageService.add({
        severity: 'warn',
        summary: 'Uyarı',
        detail: 'Lütfen tüm zorunlu alanları doldurun'
      });
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.talepForm.controls).forEach(key => {
      const control = this.talepForm.get(key);
      control?.markAsTouched();
    });
  }

  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  onReset(): void {
    this.talepForm.reset();
    this.seciliDosya = null;
    this.messageService.add({
      severity: 'info',
      summary: 'Form Sıfırlandı',
      detail: 'Tüm alanlar temizlendi'
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.talepForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.talepForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) {
        return 'Bu alan zorunludur';
      }
      if (field.errors['maxlength']) {
        return `Maksimum ${field.errors['maxlength'].requiredLength} karakter olmalıdır`;
      }
    }
    return '';
  }
}
