package iym.backend.makosclient.controller.dto;

import iym.makos.api.client.gen.model.IDMahkemeKararGuncellemeRequest;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

/**
 * DTO for mahkeme kodu güncelleme endpoint
 * Spring best practice for multipart file uploads with complex objects
 */
@Setter
@Getter
public class MahkemeKoduGuncellemeRequest {

    // Getters and setters
    @NotNull(message = "Dosya zorunludur")
    private MultipartFile file;
    
    @NotBlank(message = "ID zorunludur")
    private String id;
    
    @NotBlank(message = "Mahkeme kodu zorunludur")
    private String mahkemeKodu;
    
    private String aciklama;

    /**
     * Convert to IDMahkemeKararGuncellemeRequest
     */
    public IDMahkemeKararGuncellemeRequest toMahkemeKararGuncellemeRequest() {
        IDMahkemeKararGuncellemeRequest request = new IDMahkemeKararGuncellemeRequest();
        request.setId(java.util.UUID.fromString(this.id));
        // Set other fields as needed
        return request;
    }
}