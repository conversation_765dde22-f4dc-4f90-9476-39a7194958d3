package iym.db.jpa.dao.sorgu.internal;

import iym.common.enums.IDKararTuru;
import iym.common.model.entity.iym.sorgu.MahkemeKararSorguInfo;
import iym.common.model.entity.iym.sorgu.MahkemeKararSorguParam;

import java.util.List;


public interface IDIslenecekEvrakListDynamicQueries {

    List<IdIslenecekEvrakSorguInfo> islenecekEvrakListesi(IDKararTuru kararTuru, Long atananKullaniciId, String gorevTipi
            , boolean tanimlama, boolean onaylama, boolean nobetci);


}