package iym.backend.kullanici.entity;

import iym.backend.kullanici.enums.enumKullaniciStatus;
import iym.backend.kullanicigrup.entity.KullaniciGrup;
import iym.backend.kullanicigrupyetki.entity.KullaniciGrupYetki;
import iym.backend.kullanicikullanicigrup.entity.KullaniciKullaniciGrup;
import iym.backend.yetki.entity.Yetki;
import iym.common.enums.KullaniciKurum;
import iym.common.enums.UserStatusType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for Kullanici entity
 */
class KullaniciTest {

    private Kullanici kullanici;

    @BeforeEach
    void setUp() {
        kullanici = new Kullanici();
        kullanici.setId(1L);
        kullanici.setKullaniciAdi("testuser");
        kullanici.setParola("testpassword");
        kullanici.setEmail("<EMAIL>");
        kullanici.setAd("Test");
        kullanici.setSoyad("User");
        kullanici.setStatus(enumKullaniciStatus.AKTIF);
        kullanici.setKurum(KullaniciKurum.BTK);
    }

    @Test
    void testUsernameCompatibility() {
        // Test username getter/setter compatibility with IymUser
        assertEquals("testuser", kullanici.getUsername());

        kullanici.setUsername("newuser");
        assertEquals("newuser", kullanici.getKullaniciAdi());
        assertEquals("newuser", kullanici.getUsername());
    }

    @Test
    void testPasswordCompatibility() {
        // Test password getter/setter compatibility with IymUser
        assertEquals("testpassword", kullanici.getPassword());

        kullanici.setPassword("newpassword");
        assertEquals("newpassword", kullanici.getParola());
        assertEquals("newpassword", kullanici.getPassword());
    }

    @Test
    void testIsActiveMethod() {
        // Test isActive method
        kullanici.setStatus(enumKullaniciStatus.AKTIF);
        assertTrue(kullanici.isActive());

        kullanici.setStatus(enumKullaniciStatus.PASIF);
        assertFalse(kullanici.isActive());

        kullanici.setStatus(enumKullaniciStatus.SIFRE_DEGISTIRMELI);
        assertFalse(kullanici.isActive());
    }

    @Test
    void testGetUserStatus() {
        // Test UserStatusType mapping
        kullanici.setStatus(enumKullaniciStatus.AKTIF);
        assertEquals(UserStatusType.ACTIVE, kullanici.getUserStatus());

        kullanici.setStatus(enumKullaniciStatus.PASIF);
        assertEquals(UserStatusType.PASSIVE, kullanici.getUserStatus());

        kullanici.setStatus(enumKullaniciStatus.SIFRE_DEGISTIRMELI);
        assertEquals(UserStatusType.LOCKED, kullanici.getUserStatus());
    }

    @Test
    void testGetRolesWithNoGroups() {
        // Test getRoles when user has no groups
        List<String> roles = kullanici.getRoles();
        assertTrue(roles.isEmpty());
    }

    @Test
    void testGetRolesWithGroups() {
        // Setup test data
        Yetki yetki1 = new Yetki();
        yetki1.setId(1L);
        yetki1.setAd("ROLE_ADMIN");

        Yetki yetki2 = new Yetki();
        yetki2.setId(2L);
        yetki2.setAd("ROLE_USER");

        KullaniciGrup grup = new KullaniciGrup();
        grup.setId(1L);
        grup.setAd("Admin Group");

        KullaniciGrupYetki kgy1 = new KullaniciGrupYetki();
        kgy1.setKullaniciGrup(grup);
        kgy1.setYetki(yetki1);

        KullaniciGrupYetki kgy2 = new KullaniciGrupYetki();
        kgy2.setKullaniciGrup(grup);
        kgy2.setYetki(yetki2);

        List<KullaniciGrupYetki> grupYetkiler = new ArrayList<>();
        grupYetkiler.add(kgy1);
        grupYetkiler.add(kgy2);
        grup.setKullaniciGrupYetkiler(grupYetkiler);

        KullaniciKullaniciGrup kkg = new KullaniciKullaniciGrup();
        kkg.setKullanici(kullanici);
        kkg.setKullaniciGrup(grup);

        List<KullaniciKullaniciGrup> kullaniciGruplar = new ArrayList<>();
        kullaniciGruplar.add(kkg);
        kullanici.setKullaniciKullaniciGruplar(kullaniciGruplar);

        // Test getRoles
        List<String> roles = kullanici.getRoles();
        assertEquals(2, roles.size());
        assertTrue(roles.contains("ROLE_ADMIN"));
        assertTrue(roles.contains("ROLE_USER"));
    }

    @Test
    void testHasAuthority() {
        // Test hasAuthority when user has no groups
        assertFalse(kullanici.hasAuthority("ROLE_ADMIN"));

        // Setup test data with roles
        Yetki yetki1 = new Yetki();
        yetki1.setId(1L);
        yetki1.setAd("ROLE_ADMIN");

        Yetki yetki2 = new Yetki();
        yetki2.setId(2L);
        yetki2.setAd("USER_VIEW");

        KullaniciGrup grup = new KullaniciGrup();
        grup.setId(1L);
        grup.setAd("Admin Group");

        KullaniciGrupYetki kgy1 = new KullaniciGrupYetki();
        kgy1.setKullaniciGrup(grup);
        kgy1.setYetki(yetki1);

        KullaniciGrupYetki kgy2 = new KullaniciGrupYetki();
        kgy2.setKullaniciGrup(grup);
        kgy2.setYetki(yetki2);

        List<KullaniciGrupYetki> grupYetkiler = new ArrayList<>();
        grupYetkiler.add(kgy1);
        grupYetkiler.add(kgy2);
        grup.setKullaniciGrupYetkiler(grupYetkiler);

        KullaniciKullaniciGrup kkg = new KullaniciKullaniciGrup();
        kkg.setKullanici(kullanici);
        kkg.setKullaniciGrup(grup);

        List<KullaniciKullaniciGrup> kullaniciGruplar = new ArrayList<>();
        kullaniciGruplar.add(kkg);
        kullanici.setKullaniciKullaniciGruplar(kullaniciGruplar);

        // Test hasAuthority
        assertTrue(kullanici.hasAuthority("ROLE_ADMIN"));
        assertTrue(kullanici.hasAuthority("USER_VIEW"));
        assertFalse(kullanici.hasAuthority("ROLE_SUPER_ADMIN"));
    }

    @Test
    void testHasAnyAuthority() {
        // Setup test data with roles
        Yetki yetki = new Yetki();
        yetki.setId(1L);
        yetki.setAd("USER_VIEW");

        KullaniciGrup grup = new KullaniciGrup();
        grup.setId(1L);
        grup.setAd("User Group");

        KullaniciGrupYetki kgy = new KullaniciGrupYetki();
        kgy.setKullaniciGrup(grup);
        kgy.setYetki(yetki);

        List<KullaniciGrupYetki> grupYetkiler = new ArrayList<>();
        grupYetkiler.add(kgy);
        grup.setKullaniciGrupYetkiler(grupYetkiler);

        KullaniciKullaniciGrup kkg = new KullaniciKullaniciGrup();
        kkg.setKullanici(kullanici);
        kkg.setKullaniciGrup(grup);

        List<KullaniciKullaniciGrup> kullaniciGruplar = new ArrayList<>();
        kullaniciGruplar.add(kkg);
        kullanici.setKullaniciKullaniciGruplar(kullaniciGruplar);

        // Test hasAnyAuthority
        assertTrue(kullanici.hasAnyAuthority("ROLE_ADMIN", "USER_VIEW"));
        assertTrue(kullanici.hasAnyAuthority("USER_VIEW", "ROLE_SUPER_ADMIN"));
        assertFalse(kullanici.hasAnyAuthority("ROLE_ADMIN", "ROLE_SUPER_ADMIN"));
    }
}
