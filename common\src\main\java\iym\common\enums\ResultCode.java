package iym.common.enums;

import lombok.Getter;

import java.io.Serializable;
import java.util.Optional;

@Getter
public enum ResultCode implements Serializable {

    SUCCESS(0),
    FAILED(-1),
    REJECTED(-2);

    private final int value;

    ResultCode(int value) {
        this.value = value;
    }

    public static Optional<ResultCode> convert(int resultCode){

        for (ResultCode type : ResultCode.values()){
            if (type.value == resultCode)
                return Optional.of(type);
        }

        return Optional.empty();
    }

    public boolean isSuccess(){
        return value == SUCCESS.value;
    }
    
    public boolean isFailed(){
        return value == FAILED.value || value == REJECTED.value;
    }
}
