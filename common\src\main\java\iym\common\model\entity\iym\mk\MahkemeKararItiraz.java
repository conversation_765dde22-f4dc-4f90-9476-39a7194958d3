package iym.common.model.entity.iym.mk;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MahkemeKararItiraz")
@Table(name = "MAHKEME_KARAR_ITIRAZ")
public class MahkemeKararItiraz implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MAHKEME_KARAR_ITIRAZ_SEQ")
    @SequenceGenerator(name = "MAHKEME_KARAR_ITIRAZ_SEQ", sequenceName = "MAHKEME_KARAR_ITIRAZ_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "TALEP_KISI_ID", nullable = false)
    @NotNull
    private Long talepKisiId;

    @Column(name = "TALEP_TARIHI", nullable = false)
    @NotNull
    @Temporal(TemporalType.TIMESTAMP)
    private Date talepTarihi;

    @Column(name = "TALEP_ACIKLAMA", length = 1000)
    @Size(max = 1000)
    private String talepAciklama;

    @Column(name = "ITIRAZ_EDILEN_EVRAK_ID", nullable = false)
    @NotNull
    private Long itirazEdilenEvrakId;

    @Column(name = "ITIRAZ_EDILEN_KURUM", length = 10, nullable = false)
    @Size(max = 10)
    private String itirazEdilenKurumKodu;

    @Column(name = "DURUM", length = 100, nullable = false)
    @Size(max = 100)
    private String durum;

    @Column(name = "ITIRAZ_NEDENI", length = 10, nullable = false)
    @Size(max = 10)
    private String itirazNedeni;

    @Column(name = "ISLEYEN_KISI_ID")
    private Long isleyenKisiId;

    @Column(name = "ISLEME_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date islemeTarihi;

    @Column(name = "ITIRAZ_YAZI_ID")
    private Long itirazYaziId;

    @Column(name = "ISLEYEN_ACIKLAMA", length = 10000)
    @Size(max = 1000)
    private String isleyenAciklama;

    @Column(name = "ITIRAZA_CEVAP_EVRAK_ID")
    private Long itirazaCevapEvrakId;

    @Column(name = "BEKLEMEDE", length = 1)
    @Size(max = 1)
    private String beklemede;

    @Column(name = "ARSIV", length = 1)
    @Size(max = 1)
    private String arsiv;

    @Column(name = "ITIRAZ_SONUC", length = 20)
    @Size(max = 20)
    private String itirazSonuc;

    @Column(name = "ITIRAZ_SONUC_EVRAK_ID")
    private Long itirazSonucEvrakId;

    @Column(name = "ITIRAZ_SONUC_TARIH")
    @Temporal(TemporalType.TIMESTAMP)
    private Date itirazSonucTarihi;

    @Column(name = "ITIRAZ_SONUC_KAYIT_KISI_ID")
    private Long itirazSonucKayitKisiId;

    @Column(name = "SILINME_NEDENI", length = 1000)
    @Size(max = 1000)
    private String silmeNedeni;

    @Column(name = "SILINME_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date silmeTarihi;

    @Column(name = "SILEN_KISI_ID")
    private Long silenKisiId;


}
