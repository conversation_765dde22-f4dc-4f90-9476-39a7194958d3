package iym.backend.makosclient.controller;

import iym.backend.makosclient.controller.dto.MahkemeKoduGuncellemeRequest;
import iym.backend.makosclient.service.MakosApiService;
import iym.makos.api.client.gen.model.*;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

/**
 * MAKOS Controller
 * MAKOS API işlemlerini expose eden REST controller
 * Spring best practices ile multipart file upload handling
 */
@RestController
@RequestMapping("/api/makos")
@RequiredArgsConstructor
@Slf4j
@Validated
public class MakosController {

    private static final String FILE_PART = "file";
    private static final String REQUEST_PART = "request";

    private final MakosApiService makosApiService;

    /**
     * Health check endpoint
     *
     * @return Health check response
     */
    @GetMapping("/health")
    public ResponseEntity<HealthCheckResponse> healthCheck() {
        try {
            HealthCheckResponse response = makosApiService.healthCheck();
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Health check failed", e);
            var apiResponse = new ModelApiResponse();
            apiResponse.setResponseMessage(e.getMessage());
            apiResponse.setResponseCode(ModelApiResponse.ResponseCodeEnum.FAILED);
            HealthCheckResponse failedResponse = new HealthCheckResponse();
            failedResponse.setResponse(apiResponse);
            return ResponseEntity.internalServerError().body(failedResponse);
        }
    }

    /**
     * Health check endpoint with basic authorization
     *
     * @return Health check response
     */
    @GetMapping("/healthCheckAuthorized")
    public ResponseEntity<HealthCheckResponse> healthCheckAuthorized() {
        try {
            HealthCheckResponse response = makosApiService.healthCheckAuthorized();
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Health check with basic authorization failed", e);
            HealthCheckResponse failedResponse = new HealthCheckResponse();
            ModelApiResponse failedApiResponse = new ModelApiResponse();
            failedApiResponse.setResponseCode(ModelApiResponse.ResponseCodeEnum.FAILED);
            failedApiResponse.setResponseMessage(e.getMessage());
            failedResponse.setResponse(failedApiResponse);
            return ResponseEntity.internalServerError().body(failedResponse);
        }
    }

    /**
     * Mahkeme bilgisi güncelleme endpoint
     * File upload ile mahkeme bilgisi güncelleme işlemi
     * Spring best practice: @RequestPart ile otomatik binding ve validasyon
     *
     * @param mahkemeKoduGuncellemeRequest Mahkeme bilgisi güncelleme isteği
     * @return Güncelleme sonucu
     */
    @PostMapping(value = "/mahkeme-bilgisi-guncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDMahkemeKararGuncellemeResponse> mahkemeBilgisiGuncelle(
            @Valid @RequestPart("request") MahkemeKoduGuncellemeRequest mahkemeKoduGuncellemeRequest,
            @RequestPart("file") MultipartFile file) {

        try {
            log.info("Mahkeme bilgisi güncelleme isteği alındı - ID: {}, Mahkeme Kodu: {}",
                    mahkemeKoduGuncellemeRequest.getId(),
                    mahkemeKoduGuncellemeRequest.getMahkemeKodu());

            // MultipartFile'ı File'a çevir
            File tempFile = convertMultipartFileToFile(file);

            // Request objesini oluştur
            IDMahkemeKararGuncellemeRequest request = mahkemeKoduGuncellemeRequest.toMahkemeKararGuncellemeRequest();
            request.setId(UUID.fromString(mahkemeKoduGuncellemeRequest.getId()));

            // MAKOS API'yi çağır
            IDMahkemeKararGuncellemeResponse response = makosApiService.mahkemeBilgisiGuncelle(tempFile, request);

            // Temp dosyayı sil
            tempFile.delete();

            log.info("Mahkeme bilgisi güncelleme başarılı - ID: {}", mahkemeKoduGuncellemeRequest.getId());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Mahkeme bilgisi güncelleme hatası - ID: {}",
                    mahkemeKoduGuncellemeRequest.getId(), e);
            IDMahkemeKararGuncellemeResponse errorResponse = new IDMahkemeKararGuncellemeResponse();
            MakosApiResponse apiResponse = new MakosApiResponse();
            apiResponse.setResponseCode(MakosApiResponse.ResponseCodeEnum.FAILED);
            apiResponse.setResponseMessage("Güncelleme hatası: " + e.getMessage());
            errorResponse.setResponse(apiResponse);
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * Yeni ID karar ekleme endpoint
     * File upload ile ID tipi yeni karar ekleme
     * Spring best practice: @RequestPart ile otomatik binding ve validasyon
     *
     * @param file               Mahkeme karar dosyası
     * @param idYeniKararRequest Karar bilgileri
     * @return Karar sonucu
     */
    @PostMapping(value = "/yeni-karar-id", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDYeniKararResponse> yeniKararID(
            @RequestPart(FILE_PART) MultipartFile file,
            @Valid @RequestPart(REQUEST_PART) IDYeniKararRequest idYeniKararRequest) {

        try {
            log.info("Yeni ID karar isteği alındı - Mahkeme Kodu: {}",
                    idYeniKararRequest.getMahkemeKararBilgisi().getMahkemeKararDetay().getMahkemeKodu());

            // MultipartFile'ı File'a çevir
            File tempFile = convertMultipartFileToFile(file);

            // MAKOS API'yi çağır
            IDYeniKararResponse response = makosApiService.kararGonderID(tempFile, idYeniKararRequest);

            // Temp dosyayı sil
            tempFile.delete();

            log.info("Yeni ID karar başarıyla eklendi");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Yeni ID karar hatası", e);
            return ResponseEntity.internalServerError().body(null);
        }
    }

    /**
     * Yeni Karar IT ekleme endpoint
     * File upload ile yeni IT kararı ekleme işlemi
     * Spring best practice: @RequestPart ile otomatik binding ve validasyon
     *
     * @param file    Mahkeme karar dosyası
     * @param request ITKararRequest objesi
     * @return Ekleme sonucu
     */
    @PostMapping(value = "/yeni-karar-it", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ITKararResponse> yeniKararIT(
            @RequestPart(FILE_PART) MultipartFile file,
            @Valid @RequestPart(REQUEST_PART) ITKararRequest request) {

        try {
            log.info("Yeni IT karar isteği alındı - Mahkeme Kodu: {}", request.getMahkemeKararBilgisi().getMahkemeKararDetay().getMahkemeKodu());

            // MultipartFile'ı File'a çevir
            File tempFile = convertMultipartFileToFile(file);

            // MAKOS API'yi çağır
            ITKararResponse response = makosApiService.kararGonderIT(tempFile, request);

            // Temp dosyayı sil
            tempFile.delete();

            log.info("Yeni IT karar başarıyla eklendi");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Yeni Karar IT ekleme hatası - ID: {}", request.getId(), e);
            return ResponseEntity.internalServerError().body(null);
        }
    }

    /**
     * MultipartFile'ı File'a çevirir
     *
     * @param multipartFile MultipartFile
     * @return File
     * @throws IOException IO hatası
     */
    private File convertMultipartFileToFile(MultipartFile multipartFile) throws IOException {
        // Temp dosya oluştur
        Path tempFile = Files.createTempFile("makos_upload_", "_" + multipartFile.getOriginalFilename());

        // MultipartFile içeriğini temp dosyaya kopyala
        Files.copy(multipartFile.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);

        return tempFile.toFile();
    }
}

