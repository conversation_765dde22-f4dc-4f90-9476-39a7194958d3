package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.talep.HedeflerAidiyatTalep;
import iym.common.service.db.DbHedeflerAidiyatTalepService;
import iym.db.jpa.dao.talep.HedeflerAidiyatTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service implementation for HedeflerAidiyatTalep entity
 */
@Service
public class DbHedeflerAidiyatTalepServiceImpl extends GenericDbServiceImpl<HedeflerAidiyatTalep, Long> implements DbHedeflerAidiyatTalepService {

    private final HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo;




    @Autowired
    public DbHedeflerAidiyatTalepServiceImpl(HedeflerAidiyatTalepRepo repository) {
        super(repository);
        this.hedeflerAidiyatTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<HedeflerAidiyatTalep> findById(Long id){
        return hedeflerAidiyatTalepRepo.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedeflerAidiyatTalep> findByHedefTalepId(Long hedefTalepId) {
        return hedeflerAidiyatTalepRepo.findByHedefTalepId(hedefTalepId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedeflerAidiyatTalep> findByTarihBetween(Date startDate, Date endDate) {
        return hedeflerAidiyatTalepRepo.findByTarihBetween(startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<HedeflerAidiyatTalep> findByHedefTalepIdAndAidiyatKod(Long hedefId, String aidiyatKod) {
        return hedeflerAidiyatTalepRepo.findByHedefTalepIdAndAidiyatKod(hedefId, aidiyatKod);
    }

}
