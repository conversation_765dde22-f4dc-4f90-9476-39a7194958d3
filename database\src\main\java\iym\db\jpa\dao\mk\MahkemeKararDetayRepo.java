package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.MahkemeKararDetay;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for DMahkemeKarar entity
 */
@Repository
public interface MahkemeKararDetayRepo extends JpaRepository<MahkemeKararDetay, Long> {

    List<MahkemeKararDetay> findByEvrakId(Long evrakId);

    List<MahkemeKararDetay> findByMahkemeKararTalepId(Long mahkemeKararTalepId);


}
