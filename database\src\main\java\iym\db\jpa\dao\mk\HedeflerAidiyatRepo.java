package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.HedeflerAidiyat;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for HedeflerAidiyat entity
 */
@Repository
public interface HedeflerAidiyatRepo extends JpaRepository<HedeflerAidiyat, Long> {

//    List<HedeflerAidiyat> findByHedefTalepId(Long hedefTalepId);

}
