import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { environment } from '../../../../enviroments/environment';
import {
  EvrakKayit,
  EvrakAramaFiltresi,
  EvrakAramaSonucu,
  ParametreAramaFiltresi,
  ParametreSonucu,
  XmlValidasyonSonucu,
  DosyaYuklemeResponse,
  SistemParametresi,
  IymIstatistik,
  RaporParametresi,
  RaporSonucu,
  IletisimTespitiFiltresi,
  IletisimTespitiSonucu
} from '../models/iym.models';

@Injectable({
  providedIn: 'root'
})
export class IymService {
  private apiUrl = environment.apiUrl + '/iym';

  constructor(private http: HttpClient) {}

  // Dinleme Evrak Araması İşlemleri
  evrakAra(filtre: EvrakAramaFiltresi): Observable<EvrakAramaSonucu[]> {
    // Şimdilik mockup data döndürüyoruz
    const mockData: EvrakAramaSonucu[] = [
      {
        evrakNo: 'I.D.B-2021-IG-20149',
        evrakTarihi: '23/09/2021',
        evrakTipi: 'İletişimin Denetlenmesi',
        mahkemeKodu: '06000204',
        kararTipi: 'Önleyici Hakim Kararı',
        durumu: 'İşlendi',
        aciklama: 'GSM dinleme kararı'
      },
      {
        evrakNo: 'ADB-202006241507571193706',
        evrakTarihi: '24/06/2020',
        evrakTipi: 'İletişimin Tespiti',
        mahkemeKodu: '06003405',
        kararTipi: 'Bilgi Alma',
        durumu: 'Bekliyor',
        aciklama: 'İletişim tespiti talebi'
      },
      {
        evrakNo: 'TEST20241405',
        evrakTarihi: '06/12/2024',
        evrakTipi: 'İletişimin Denetlenmesi',
        mahkemeKodu: '06000204',
        kararTipi: 'Önleyici Hakim Kararı',
        durumu: 'Hatalı',
        aciklama: 'Çanak ekleme işlemi'
      }
    ];

    return of(mockData);
  }

  evrakDetayGetir(evrakNo: string): Observable<EvrakKayit> {
    // Mockup evrak detayı
    const mockEvrak: EvrakKayit = {
      xmlVersiyon: '1',
      evrakNo: evrakNo,
      evrakTarihi: '23/09/2021',
      evrakGeldigiKurum: '02',
      evrakTipi: 'ILETISIMIN_DENETLENMESI',
      havaleBirim: '02',
      gelIl: '0600',
      acilmi: 'H',
      mahkemeKarar: {
        kararTip: '100',
        mahkemeKodu: '06000204',
        mahkemeKararNo: '2021/23022',
        mahkemeIli: '3300',
        hedefler: [
          {
            hedefNo: '905550001122',
            hedefTipi: '10',
            hedefAdi: '',
            hedefSoyadi: '',
            baslamaTarihi: '24/09/2021',
            suresi: '',
            sureTipi: '3',
            bimAidiyatKod: 'Y3320001'
          }
        ]
      }
    };

    return of(mockEvrak);
  }

  // Evrak Gönderme İşlemleri
  evrakGonder(dosya: File): Observable<DosyaYuklemeResponse> {
    // Mockup dosya yükleme response
    const mockResponse: DosyaYuklemeResponse = {
      basarili: true,
      dosyaAdi: dosya.name,
      dosyaBoyutu: dosya.size,
      yuklenmeTarihi: new Date(),
      validasyonSonucu: {
        gecerliMi: true,
        hatalar: [],
        uyarilar: []
      }
    };

    return of(mockResponse);
  }

  xmlValidasyonYap(xmlIcerik: string): Observable<XmlValidasyonSonucu> {
    // Mockup XML validasyon
    const mockValidasyon: XmlValidasyonSonucu = {
      gecerliMi: true,
      hatalar: [],
      uyarilar: [
        {
          satir: 5,
          sutun: 10,
          mesaj: 'Evrak konusu boş bırakılmış',
          oneri: 'Evrak konusu alanını doldurunuz'
        }
      ]
    };

    return of(mockValidasyon);
  }

  // XML Sorgulama İşlemleri
  xmlOrnekleriGetir(): Observable<string[]> {
    const ornekXmlIsimleri = [
      'İletişimin Denetlenmesi - Yeni Karar',
      'İletişimin Denetlenmesi - Uzatma Kararı',
      'İletişimin Denetlenmesi - Sonlandırma Kararı',
      'İletişimin Tespiti - Yeni Karar',
      'Hedef Çanak Ekleme',
      'Hedef Çanak Güncelleme',
      'Mahkeme Kodu Değiştirme'
    ];

    return of(ornekXmlIsimleri);
  }

  xmlOrnekIcerikGetir(ornekAdi: string): Observable<string> {
    // Mockup XML içeriği
    const mockXml = `<?xml version="1.0" encoding="ISO-8859-9"?>
<EVRAK_KAYIT>
    <XML_VERSIYON>1</XML_VERSIYON>
    <EVRAK_NO>I.D.B-2021-IG-20149</EVRAK_NO>
    <EVRAK_TARIHI>23/09/2021</EVRAK_TARIHI>
    <EVRAK_GELDIGI_KURUM>02</EVRAK_GELDIGI_KURUM>
    <EVRAK_TIPI>ILETISIMIN_DENETLENMESI</EVRAK_TIPI>
    <HAVALE_BIRIM>02</HAVALE_BIRIM>
    <GEL_IL>0600</GEL_IL>
    <ACILMI>H</ACILMI>
    <MAHKEME_KARAR>
        <KARAR_TIP>100</KARAR_TIP>
        <MAHKEME_KODU>06000204</MAHKEME_KODU>
        <MAHKEME_KARAR_NO>2021/23022</MAHKEME_KARAR_NO>
        <MAHKEME_ILI>3300</MAHKEME_ILI>
        <HEDEFLER>
            <HEDEF_NO>905550001122</HEDEF_NO>
            <HEDEF_TIPI>10</HEDEF_TIPI>
            <BASLAMA_TARIHI>24/09/2021</BASLAMA_TARIHI>
            <SURE_TIPI>3</SURE_TIPI>
            <BIM_AIDIYAT_KOD>Y3320001</BIM_AIDIYAT_KOD>
        </HEDEFLER>
    </MAHKEME_KARAR>
</EVRAK_KAYIT>`;

    return of(mockXml);
  }

  // Parametre Sorgulama İşlemleri
  parametreleriGetir(filtre: ParametreAramaFiltresi): Observable<ParametreSonucu[]> {
    const mockParametreler: ParametreSonucu[] = [
      {
        id: 1,
        parametreAdi: 'MAX_DOSYA_BOYUTU',
        parametreDegeri: '10485760',
        parametreGrubu: 'DOSYA_YUKLEME',
        aciklama: 'Maksimum dosya boyutu (byte)',
        aktifMi: true,
        olusturmaTarihi: new Date('2024-01-01')
      },
      {
        id: 2,
        parametreAdi: 'XML_VALIDASYON_AKTIF',
        parametreDegeri: 'true',
        parametreGrubu: 'XML_ISLEMLERI',
        aciklama: 'XML validasyon kontrolü aktif mi',
        aktifMi: true,
        olusturmaTarihi: new Date('2024-01-01')
      },
      {
        id: 3,
        parametreAdi: 'OTOMATIK_YEDEKLEME',
        parametreDegeri: 'false',
        parametreGrubu: 'SISTEM',
        aciklama: 'Otomatik yedekleme aktif mi',
        aktifMi: false,
        olusturmaTarihi: new Date('2024-01-01')
      }
    ];

    return of(mockParametreler);
  }

  parametreGuncelle(parametre: ParametreSonucu): Observable<boolean> {
    // Mockup güncelleme işlemi
    return of(true);
  }

  // İletişimin Tespiti İşlemleri
  tespitEvraklariGetir(): Observable<EvrakAramaSonucu[]> {
    const mockTespitEvraklari: EvrakAramaSonucu[] = [
      {
        evrakNo: 'ADB-202006241507571193706',
        evrakTarihi: '24/06/2020',
        evrakTipi: 'İletişimin Tespiti',
        mahkemeKodu: '06003405',
        kararTipi: 'Bilgi Alma',
        durumu: 'Tamamlandı',
        aciklama: 'GSM iletişim tespiti'
      },
      {
        evrakNo: 'IT-2024-001234',
        evrakTarihi: '15/03/2024',
        evrakTipi: 'İletişimin Tespiti',
        mahkemeKodu: '06003405',
        kararTipi: 'Bilgi Alma',
        durumu: 'İşleniyor',
        aciklama: 'E-posta iletişim tespiti'
      }
    ];

    return of(mockTespitEvraklari);
  }

  // İstatistik ve Rapor İşlemleri
  istatistikleriGetir(): Observable<IymIstatistik> {
    const mockIstatistik: IymIstatistik = {
      toplamEvrak: 1247,
      bekleyenEvrak: 23,
      islenenEvrak: 1198,
      hatalıEvrak: 26,
      bugunIslemler: 15,
      sonIslemTarihi: new Date()
    };

    return of(mockIstatistik);
  }

  raporOlustur(parametreler: RaporParametresi): Observable<RaporSonucu> {
    const mockRapor: RaporSonucu = {
      raporId: 'RPT-' + Date.now(),
      raporAdi: `IYM Raporu - ${parametreler.raporTipi}`,
      olusturmaTarihi: new Date(),
      dosyaYolu: '/raporlar/iym-raporu.pdf',
      dosyaBoyutu: 2048576,
      indirmeLinki: '/api/raporlar/indir/RPT-' + Date.now()
    };

    return of(mockRapor);
  }

  // Yardımcı Metodlar
  kararTipleriniGetir(): Observable<{kod: string, ad: string}[]> {
    const kararTipleri = [
      { kod: '100', ad: 'Önleyici Hakim Kararı' },
      { kod: '200', ad: 'Önleyici Yazılı Emir' },
      { kod: '300', ad: 'Adli Hakim Kararı' },
      { kod: '400', ad: 'Adli Yazılı Emir' },
      { kod: '500', ad: 'Bilgi Alma' },
      { kod: '600', ad: 'Önleyici Sonlandırma' },
      { kod: '700', ad: 'Adli Sonlandırma' }
    ];

    return of(kararTipleri);
  }

  hedefTipleriniGetir(): Observable<{kod: string, ad: string}[]> {
    const hedefTipleri = [
      { kod: '10', ad: 'GSM' },
      { kod: '20', ad: 'Sabit' },
      { kod: '30', ad: 'Uydu' },
      { kod: '40', ad: 'Yurt Dışı' },
      { kod: '50', ad: 'E-posta' },
      { kod: '60', ad: 'IMEI' },
      { kod: '70', ad: 'IMSI' }
    ];

    return of(hedefTipleri);
  }

  // İletişim Tespiti İşlemleri
  iletisimTespitleriniGetir(filtre: IletisimTespitiFiltresi): Observable<IletisimTespitiSonucu[]> {
    // Şimdilik mockup data döndürüyoruz
    const mockData: IletisimTespitiSonucu[] = [
      {
        id: 1,
        evrakNo: 'EVR-2024-001',
        mahkemeKodu: 'MAH-001',
        hedefBilgisi: '05551234567',
        tespitiTuru: 'GSM_ABONE',
        durum: 'TAMAMLANDI',
        talepTarihi: new Date('2024-01-15T10:30:00'),
        tamamlanmaTarihi: new Date('2024-01-15T14:45:00'),
        sonucBilgisi: 'Abone bilgileri başarıyla tespit edildi.\nAd Soyad: Ahmet YILMAZ\nAdres: İstanbul/Kadıköy',
        ilerlemeYuzdesi: 100
      },
      {
        id: 2,
        evrakNo: 'EVR-2024-002',
        mahkemeKodu: 'MAH-002',
        hedefBilgisi: '*************',
        tespitiTuru: 'IP_ADRESI',
        durum: 'ISLEMDE',
        talepTarihi: new Date('2024-01-16T09:15:00'),
        ilerlemeYuzdesi: 65
      },
      {
        id: 3,
        evrakNo: 'EVR-2024-003',
        mahkemeKodu: 'MAH-001',
        hedefBilgisi: '<EMAIL>',
        tespitiTuru: 'EPOSTA',
        durum: 'BEKLEMEDE',
        talepTarihi: new Date('2024-01-17T11:20:00'),
        ilerlemeYuzdesi: 25
      },
      {
        id: 4,
        evrakNo: 'EVR-2024-004',
        mahkemeKodu: 'MAH-003',
        hedefBilgisi: '02121234567',
        tespitiTuru: 'SABIT_HAT',
        durum: 'HATA',
        talepTarihi: new Date('2024-01-18T08:45:00'),
        hataMesaji: 'Hedef numara sistemde bulunamadı',
        ilerlemeYuzdesi: 0
      },
      {
        id: 5,
        evrakNo: 'EVR-2024-005',
        mahkemeKodu: 'MAH-002',
        hedefBilgisi: '05559876543',
        tespitiTuru: 'GSM_ABONE',
        durum: 'TAMAMLANDI',
        talepTarihi: new Date('2024-01-19T13:10:00'),
        tamamlanmaTarihi: new Date('2024-01-19T16:30:00'),
        sonucBilgisi: 'Abone bilgileri tespit edildi.\nAd Soyad: Fatma KAYA\nAdres: Ankara/Çankaya',
        ilerlemeYuzdesi: 100
      }
    ];

    return of(mockData);
  }
}
