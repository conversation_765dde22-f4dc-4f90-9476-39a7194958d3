package iym.db.jpa.dao.sorgu.internal;

import iym.common.enums.IDKararTuru;
import iym.common.model.entity.iym.sorgu.MahkemeKararSorguInfo;
import iym.common.model.entity.iym.sorgu.MahkemeKararSorguParam;
import iym.common.util.CommonUtils;
import iym.common.util.DateTimeUtils;
import iym.db.jpa.dao.sorgu.MahkemeKararRepoDynamicQueries;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

import java.util.*;

public class IDIslenecekEvrakListDynamicQueriesImpl implements IDIslenecekEvrakListDynamicQueries {

    @PersistenceContext
    private EntityManager entityManager;

    /*
    * Asagidaki sorgulardaki MAHKEME_KARAR_ATAMA'daki mahkeme_karar_id ile MAHKEME_KARAR_TALEP'teki id ayi degildir
    * Bu yuzden ikisi arasinda join islemi yapilmiyor.
    * Bu sebepten dolayi evrak_id uzerinden join islemleri yapilmaktadir.
    * Eski sistemdeki bir evraka ait birden fazla mahmeke_karar_talebi bu tur islemlerde hataya sebep olmaktadir.
    * */

    private static final String ISLENECEK_KARARLAR_SORGU_BASESQL_STR = """
            SELECT
                ek.ID,
                ek.EVRAK_SIRA_NO,
                ek.EVRAK_NO,
                ek.GIRIS_TARIH,
                ek.EVRAK_TARIHI,
                ek.GEL_IL,
                TRIM(NVL(i.ILCE_ADI, '') || ' ' || NVL(i.ILCE_ADI , '')) AS GEL_IL_ADI,
                ek.ACILMI,
                ek.ACIKLAMA,
                mkt.ID AS MAHKEME_KARAR_TALEP_ID,
                mkt.SORUSTURMA_NO ,
                mkt.MAHKEME_KARAR_NO,
                mkt.MAHKEME_KODU,
                ma.MAHKEME_ADI,
                k_gonderen.ID AS ATAYAN_ID,
                TRIM(NVL(k_gonderen.ADI, '') || ' ' || NVL(k_gonderen.SOYADI , '')) AS ATAYAN_ADI,
                k_gonderilen.ID AS ATANAN_ID,
                TRIM(NVL(k_gonderilen.ADI, '') || ' ' || NVL(k_gonderilen.SOYADI , '')) AS ATANAN_ADI
            FROM iym.EVRAK_KAYIT ek
            INNER JOIN iym.EVRAK_GELEN_KURUMLAR egk ON egk.KURUM_KOD = ek.EVRAK_GELDIGI_KURUM
            INNER JOIN iym.ILLER i ON i.IL_KOD  = ek.GEL_IL
            INNER JOIN iym.MAHKEME_KARAR_TALEP mkt ON mkt.EVRAK_ID = ek.ID
            INNER JOIN iym.MAHKEME_ADI ma ON ma.MAHKEME_KODU = mkt.MAHKEME_KODU
            LEFT OUTER JOIN iym.MAHKEME_KARAR_ATAMA mka ON mka.EVRAK_ID = ek.ID
            LEFT OUTER JOIN iym.KULLANICILAR k_gonderen ON k_gonderen.ID = mka.GONDEREN_ID
            LEFT OUTER JOIN iym.KULLANICILAR k_gonderilen ON k_gonderilen.ID = mka.GONDERILEN_ID
            """;

    @Override
    public List<IdIslenecekEvrakSorguInfo> islenecekEvrakListesi(IDKararTuru kararTuru, Long atananKullaniciId, String gorevTipi
            , boolean tanimlama, boolean onaylama, boolean nobetci){
        List<IdIslenecekEvrakSorguInfo> result = null;

        StringBuilder sql = new StringBuilder(ISLENECEK_KARARLAR_SORGU_BASESQL_STR);

        Map<String, Object> parameters = new HashMap<>();

        if (kararTuru != null) {
            sql.append(" AND mkt.SORUSTURMA_NO = :sorusturmaNo");
            parameters.put("sorusturmaNo", kararTuru.getKararTuru());
        }

        /*
        if (!CommonUtils.isNullOrEmpty(gorevTipi)) {
            sql.append(" AND mkt.MAHKEME_KARAR_NO = :mahkemeKararNo");
            parameters.put("mahkemeKararNo", param.getMahkemeKararNo());
        }

        if (param.getMahkemeKodu() != null && !param.getMahkemeKodu().isEmpty()) {
            sql.append(" AND mkt.MAHKEME_KODU = :mahkemeKodu");
            parameters.put("mahkemeKodu", param.getMahkemeKodu());
        }

        if (param.getDurum() != null && !param.getDurum().isEmpty()) {
            sql.append(" AND mkt.DURUM = :durum");
            parameters.put("durum", param.getDurum());
        }



        if (param.getEvrakSiraNo() != null && !param.getEvrakSiraNo().isEmpty()) {
            sql.append(" AND e.EVRAK_SIRA_NO = :evrakSiraNo");
            parameters.put("evrakSiraNo", param.getEvrakSiraNo());
        }
        */
        Query query = entityManager.createNativeQuery(sql.toString(), "MahkemeKararTalepSorguInfoMapping");

        parameters.forEach(query::setParameter);

        result = query.getResultList();

        if(result == null){
            result = new ArrayList<>();
        }
        return result;
    }


    private List<IdIslenecekEvrakSorguInfo> mahkemeKararSorgu(String kurumKodu, MahkemeKararSorguParam param) {

        StringBuilder sql = new StringBuilder(ISLENECEK_KARARLAR_SORGU_BASESQL_STR);

        Map<String, Object> parameters = new HashMap<>();

        if (param.getSorusturmaNo() != null && !param.getSorusturmaNo().isEmpty()) {
            sql.append(" AND mkt.SORUSTURMA_NO = :sorusturmaNo");
            parameters.put("sorusturmaNo", param.getSorusturmaNo());
        }

        if (param.getMahkemeKararNo() != null && !param.getMahkemeKararNo().isEmpty()) {
            sql.append(" AND mkt.MAHKEME_KARAR_NO = :mahkemeKararNo");
            parameters.put("mahkemeKararNo", param.getMahkemeKararNo());
        }

        if (param.getMahkemeKodu() != null && !param.getMahkemeKodu().isEmpty()) {
            sql.append(" AND mkt.MAHKEME_KODU = :mahkemeKodu");
            parameters.put("mahkemeKodu", param.getMahkemeKodu());
        }

        if (param.getDurum() != null && !param.getDurum().isEmpty()) {
            sql.append(" AND mkt.DURUM = :durum");
            parameters.put("durum", param.getDurum());
        }

        if (param.getAciklama() != null && !param.getAciklama().isEmpty()) {
            // Use UPPER() instead of LOWER() for better Turkish character handling
            // Oracle's UPPER() function handles Turkish characters (İ, Ğ, Ü, Ö, Ş, Ç) more reliably than LOWER()
            // This ensures proper case-insensitive search for Turkish text without character conversion issues
            // Note: CONCAT(CONCAT('%', :aciklama), '%') is used because Oracle CONCAT only accepts 2 parameters
            sql.append(" AND UPPER(mkt.ACIKLAMA) LIKE UPPER(CONCAT(CONCAT('%', :aciklama), '%'))");
            parameters.put("aciklama", param.getAciklama());
        }

        if (param.getKayitTarihi() != null) {
            Date startOfDay = DateTimeUtils.truncateTime(param.getKayitTarihi());
            Date nextDayStart = DateTimeUtils.addDays(startOfDay, 1);

            sql.append(" AND mkt.KAYIT_TARIHI >= :kayitTarihiStart");
            sql.append(" AND mkt.KAYIT_TARIHI < :kayitTarihiEnd");

            parameters.put("kayitTarihiStart", new java.sql.Timestamp(startOfDay.getTime()));
            parameters.put("kayitTarihiEnd", new java.sql.Timestamp(nextDayStart.getTime()));
        }

        if (param.getEvrakSiraNo() != null && !param.getEvrakSiraNo().isEmpty()) {
            sql.append(" AND e.EVRAK_SIRA_NO = :evrakSiraNo");
            parameters.put("evrakSiraNo", param.getEvrakSiraNo());
        }

        Query query = entityManager.createNativeQuery(sql.toString(), "MahkemeKararTalepSorguInfoMapping");

        parameters.forEach(query::setParameter);

        return query.getResultList();
    }

}
