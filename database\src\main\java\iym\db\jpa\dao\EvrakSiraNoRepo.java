package iym.db.jpa.dao;

import iym.common.model.entity.iym.EvrakSiraNo;
import iym.common.model.entity.iym.Gorevler2;
import iym.common.model.entity.iym.Gorevler2PK;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Gorevler2 entity
 */
@Repository
public interface EvrakSiraNoRepo extends JpaRepository<EvrakSiraNo, Long> {

    Optional<EvrakSiraNo> findByYilAndSiraNo(Long yil, Long siraNo);
    Optional<EvrakSiraNo> findByYil(Long yil);

}
