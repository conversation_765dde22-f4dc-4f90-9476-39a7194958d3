name: Docker Build

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-backend:
    name: Build Backend Docker Image
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          
      - name: Cache Maven dependencies
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-m2-
            
      - name: Build backend JAR
        run: mvn clean package -pl backend -am -DskipTests
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Log in to Container Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/backend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-
            
      - name: Create Dockerfile for Backend
        run: |
          cat > backend/Dockerfile << 'EOF'
          FROM eclipse-temurin:17-jre-jammy
          
          # Create app directory
          WORKDIR /app
          
          # Copy the JAR file
          COPY target/*.jar app.jar
          
          # Create non-root user
          RUN groupadd -r appuser && useradd -r -g appuser appuser
          RUN chown -R appuser:appuser /app
          USER appuser
          
          # Expose port
          EXPOSE 4000
          
          # Health check
          HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
            CMD curl -f http://localhost:4000/actuator/health || exit 1
          
          # Run the application
          ENTRYPOINT ["java", "-jar", "app.jar"]
          EOF
          
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  build-makos:
    name: Build Makos Docker Image
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          
      - name: Cache Maven dependencies
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-m2-
            
      - name: Build makos JAR
        run: mvn clean package -pl makos -am -DskipTests
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Log in to Container Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/makos
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-
            
      - name: Create Dockerfile for Makos
        run: |
          cat > makos/Dockerfile << 'EOF'
          FROM eclipse-temurin:17-jre-jammy
          
          # Create app directory
          WORKDIR /app
          
          # Copy the JAR file
          COPY target/*.jar app.jar
          
          # Create non-root user
          RUN groupadd -r appuser && useradd -r -g appuser appuser
          RUN chown -R appuser:appuser /app
          USER appuser
          
          # Expose port
          EXPOSE 5000
          
          # Health check
          HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
            CMD curl -f http://localhost:5000/actuator/health || exit 1
          
          # Run the application
          ENTRYPOINT ["java", "-jar", "app.jar"]
          EOF
          
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./makos
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  build-frontend:
    name: Build Frontend Docker Image
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Check if package.json exists
        id: check-package
        run: |
          if [ -f "frontend/package.json" ]; then
            echo "exists=true" >> $GITHUB_OUTPUT
          else
            echo "exists=false" >> $GITHUB_OUTPUT
            echo "⚠️ Frontend package.json not found, skipping frontend build"
          fi
          
      - name: Set up Docker Buildx
        if: steps.check-package.outputs.exists == 'true'
        uses: docker/setup-buildx-action@v3
        
      - name: Log in to Container Registry
        if: steps.check-package.outputs.exists == 'true' && github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Extract metadata
        if: steps.check-package.outputs.exists == 'true'
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-
            
      - name: Build and push Docker image
        if: steps.check-package.outputs.exists == 'true'
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
