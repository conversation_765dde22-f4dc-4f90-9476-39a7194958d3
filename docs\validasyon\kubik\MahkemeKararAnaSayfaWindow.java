package gov.tib.iym.mahkemekarar.view;

import gov.tib.iym.IYMSabitler;
import gov.tib.iym.IymIslemler;
import gov.tib.iym.events.EvrakGor;
import gov.tib.iym.hedefYonetim.model.KullaniciYetkiliKurumPojo;
import gov.tib.iym.hedefYonetim.model.MahkemeKararPojo;
import gov.tib.iym.hedefYonetim.model.MahkemeKararTalepPojo;
import gov.tib.iym.hedefYonetim.model.XmlEvrakPojo;
import gov.tib.iym.mahkemekarar.base.model.HedefTipleriPojo;
import gov.tib.iym.mahkemekarar.base.model.MAHKEME_KARAR_ISLEM_TURU;
import gov.tib.iym.mahkemekarar.base.model.MahkemeKodlariPojo;
import gov.tib.iym.mahkemekarar.model.*;
import gov.tib.iym.mahkemekarar.renderer.*;
import gov.tib.iym.model.EvrakOzet;
import gov.tib.iym.model.EvrakYaziPojo;
import gov.tib.iym.model.IllerPojo;
import gov.tib.iym.model.MahkemeKararAtamaPojo;
import gov.tib.iym.service.IymServiceMahkemeKarar;
import gov.tib.iym.service.Iymservice;
import gov.tib.kubik.view.zk.component.TibWindow;
import gov.tib.personel.model.LoginInfo;
import gov.tib.personel.service.PersonelService;
import gov.tib.util.TemelIslemler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.Executions;
import org.zkoss.zk.ui.Sessions;
import org.zkoss.zul.*;
import service.LoggerService;
import service.ServiceManager;
import util.zk.HttpUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.LogManager;

public class MahkemeKararAnaSayfaWindow extends TibWindow {

	private static final Logger logger = LoggerFactory.getLogger(MahkemeKararAnaSayfaWindow.class);

	private LoginInfo personel = (LoginInfo) Sessions.getCurrent()
			.getAttribute("personel");
	private boolean hukukNobetciMi = false;
	private boolean hukukDenetciMi = false;
	private Listbox kurumlarList;
	private Grid islenecekEvrakList;
	private Grid mahkemeKararList;
	private Grid itirazEvraklariList;
	private Label islenebilecekEvrakSayisiLbl;
	private Label islenenEvrakSayisiLbl;
	private Label evrakSayiLbl;

	private Groupbox evrakOnizlemeGroupbox;
	private Textbox cekilecekEvrakSayisiTxt;
	private Button evrakCekBtn;
	private Button sehvenImhaButton;
	private Column atamaYapanPersonelColumn;
	private Column islemColumn;
	private Checkbox hepsiCheckbox;
	private Checkbox evrakOnizlemeCheck;
	private Tree islenecekEvraklarTree;
	private Div evrakCekDiv;

	private Tabbox mahkemeKararTablar;
	private Tabpanel mahkemeKararAnaSayfaTabPanel;
	private Tabpanel tanimlamaTabPanel;
	private Tabpanel mahkemeKararAramaTabPanel;
	private Tabpanel itirazIslemleriTabPanel;
	private Tab itirazIslemleriTab;
	private Tab mahkemeKararAramaTab;
	private Tab mahkemeKararAnaSayfaTab;
	private Tab tanimlamaTab;
	private Tab uzatmaTab;
	private Tab sonlandirmaTab;
	private Tab aidiyatTab;
	private Tab adSoyadTab;
	private Tab mahkemeKoduTab;
	private Tab genelKararTab;

	private List<Long> onaylananKararlarIdList;
	private List<Long> acikEvrakIdList;
	private List<XmlEvrakPojo> islenebilecekEvraklar;
	private List<ItirazEvrakPojo> itirazEvraklari;

	private Textbox hedefNoText;
	private Textbox aidiyatNoText;
	private Listbox hedefTipiList;
	private Datebox evrakGirisTarihi1;
	private Datebox evrakGirisTarihi2;
	private Textbox evrakSiraNoText;
	private Textbox evrakNoText;
	private Textbox evrakAciklamaText;
	private Listbox mahkemeIliList;
	private Listbox mahkemeAdiList;
	private Textbox kararNoText;
	private Textbox sorusturmaNoText;
	private Textbox aramaAciklamaText;
	private Label aramaAciklamaLbl;
	private Toolbarbutton tanimlamaHavuzuGorButton;
	private Toolbarbutton islenecekEvraklarYenileButton;
	private Checkbox iadeAraCB;
	
	/*Nobetci tarafindan yapilan evraklarin tab paneli,*/
	private Tabpanel nobetciTanimliEvraklarTabPanel;
	private Tab nobetciTanimliEvraklarTab;
	private Grid nobetciTanimliEvraklarGrid;
	private Textbox nobetciTanimliEvrakSayisiTxt;

	private MahkemeKararKayitWindow mahkemeKararKayitWindow;
	private static EvrakAtamaQueue evrakAtamaQueue = new EvrakAtamaQueue(1);

	@Override
	protected void initWindow() {

		if (!IymIslemler.sayfaErisimHakkiVar(personel,
				"forms/iym/mahkemeKarar/MahkemeKararAnaSayfa.zul")) {
			this.mesajBoxGosterHata("Bu sayfaya girmeye yetkili değilsiniz.");
			Sessions.getCurrent().invalidate();
			Executions.sendRedirect("/dologin.zul");
		}

		this.hukukDenetciMi = hukukDenetciMi();

		if ((personel.getGorevKodu() == null || personel.getGorevKodu().equals("")) && !this.hukukDenetciMi) {
			Executions.getCurrent().sendRedirect("Ana2.zul");
			return;
		}

		PersonelService srv = ServiceManager.getPersonelService();
		mahkemeKararAramaTabPanel = (Tabpanel) getFellow("mahkemeKararAramaTabPanel");

		this.hukukNobetciMi = srv.hukukNobetciMi(personel.getPersonelId());
		if (hukukNobetciMi) {
			personel.setGorevTipi("HUKUK_NOBET");

			/*
			if(!mahkemeKararAramaVarMi())
				mahkemeKararAramaTabPanel.setVisible(false);
			else
				mahkemeKararAramaTabPanel.setVisible(true);
			*/
			//Redmine #4645 geregi  hukuk nobetcisi mahkeme karar arama tabini gorebilecektir.
			mahkemeKararAramaTabPanel.setVisible(true);
		}
		
		onaylananKararlarIdList = new ArrayList<Long>();
		acikEvrakIdList = new ArrayList<Long>();

		kurumlarList = (Listbox) getFellow("kurumlarList");
		itirazEvraklariList = (Grid) getFellow("itirazEvraklariList");
		mahkemeKararList = (Grid) getFellow("mahkemeKararList");
		islenecekEvrakList = (Grid) getFellow("islenecekEvrakList");
		islenecekEvraklarTree = (Tree) getFellow("islenecekEvraklarTree");
		hepsiCheckbox = (Checkbox) getFellow("hepsiCheckbox");
		evrakOnizlemeCheck = (Checkbox) getFellow("evrakOnizlemeCheck");
		evrakCekBtn = (Button) getFellow("evrakCekBtn");
		sehvenImhaButton = (Button) getFellow("sehvenImhaButton");
		cekilecekEvrakSayisiTxt = (Textbox) getFellow("cekilecekEvrakSayisiTxt");
		islenebilecekEvrakSayisiLbl = (Label) getFellow("islenebilecekEvrakSayisiLbl");
		islenenEvrakSayisiLbl = (Label) getFellow("islenenEvrakSayisiLbl");
		evrakSayiLbl = (Label) getFellow("evrakSayiLbl");
		atamaYapanPersonelColumn = (Column) getFellow("atamaYapanPersonelColumn");
		islemColumn = (Column) getFellow("islemColumn");
		mahkemeKararTablar = (Tabbox) getFellow("mahkemeKararTablar");
		mahkemeKararAnaSayfaTabPanel = (Tabpanel) getFellow("mahkemeKararAnaSayfaTabPanel");
		tanimlamaTabPanel = (Tabpanel) getFellow("tanimlamaTabPanel");
		mahkemeKararAramaTabPanel = (Tabpanel) getFellow("mahkemeKararAramaTabPanel");
		itirazIslemleriTabPanel = (Tabpanel) getFellow("itirazIslemleriTabPanel");
		itirazIslemleriTab = (Tab) getFellow("itirazIslemleriTab");
		mahkemeKararAnaSayfaTab = (Tab) getFellow("mahkemeKararAnaSayfaTab");
		aramaAciklamaText = (Textbox) getFellow("aramaAciklamaText");
		aramaAciklamaLbl = (Label) getFellow("aramaAciklamaLbl");
		iadeAraCB = (Checkbox)getFellow("iadeAraCB");
		mahkemeKararAramaTab = (Tab) getFellow("mahkemeKararAramaTab");
		tanimlamaTab = (Tab) getFellow("tanimlamaTab");
		uzatmaTab = (Tab) getFellow("uzatmaTab");
		sonlandirmaTab = (Tab) getFellow("sonlandirmaTab");
		aidiyatTab = (Tab) getFellow("aidiyatTab");
		adSoyadTab = (Tab) getFellow("adSoyadTab");
		mahkemeKoduTab = (Tab) getFellow("mahkemeKoduTab");
		genelKararTab = (Tab) getFellow("genelKararTab");


		hedefNoText = (Textbox) getFellow("hedefNoText");
		aidiyatNoText = (Textbox) getFellow("aidiyatNoText");
		hedefTipiList = (Listbox) getFellow("hedefTipiList");
		evrakGirisTarihi1 = (Datebox) getFellow("evrakGirisTarihi1");
		evrakGirisTarihi2 = (Datebox) getFellow("evrakGirisTarihi2");
		evrakSiraNoText = (Textbox) getFellow("evrakSiraNoText");
		evrakNoText = (Textbox) getFellow("evrakNoText");
		evrakAciklamaText = (Textbox) getFellow("evrakAciklamaText");
		mahkemeIliList = (Listbox) getFellow("mahkemeIliList");
		mahkemeAdiList = (Listbox) getFellow("mahkemeAdiList");
		kararNoText = (Textbox) getFellow("kararNoText");
		sorusturmaNoText = (Textbox) getFellow("sorusturmaNoText");
		evrakOnizlemeGroupbox = (Groupbox) getFellow("evrakOnizlemeGroupbox");
		evrakCekDiv = (Div) getFellow("evrakCekDiv");
		tanimlamaHavuzuGorButton = (Toolbarbutton) getFellow("tanimlamaHavuzuGorButton");
		islenecekEvraklarYenileButton = (Toolbarbutton) getFellow("islenecekEvraklarYenileButton");

		evrakGirisTarihi1
				.setValue(TemelIslemler.TariheGunEkle(new Date(), -90));
		evrakGirisTarihi2.setValue(new Date());
		hedefTipiListBind();
		mahkemeIliListBind();
		mahkemeAdiListBind();

		if (personel.getGorevTipi().equalsIgnoreCase("HUKUK_NOBET")) {
			atamaYapanPersonelColumn.setVisible(false);
			islemColumn.setWidth("30%");
			evrakSayiLbl.setVisible(true);
			cekilecekEvrakSayisiTxt.setVisible(true);
			evrakCekBtn.setVisible(true);
			itirazIslemleriTabPanel.setVisible(false);
			itirazIslemleriTab.setVisible(false);
			IymIslemler.IymLogger(personel, Sessions.getCurrent(),
					"Mahkeme Karar Ana Sayfa Girişi-Nöbetçi Girişi", "", this,
					null);
		} else if (hukukDenetciMi) {
			evrakCekDiv.setVisible(false);
			evrakOnizlemeGroupbox.setVisible(false);
			mahkemeKararAnaSayfaTabPanel.setVisible(false);
			mahkemeKararAnaSayfaTab.setVisible(false);
			itirazIslemleriTabPanel.setVisible(false);
			itirazIslemleriTab.setVisible(false);
			tanimlamaTab.setVisible(false);
			uzatmaTab.setVisible(false);
			sonlandirmaTab.setVisible(false);
			aidiyatTab.setVisible(false);
			adSoyadTab.setVisible(false);
			mahkemeKoduTab.setVisible(false);
			genelKararTab.setVisible(false);
			mahkemeKararTablar.setSelectedTab(mahkemeKararAramaTab);
			sehvenImhaButton.setVisible(false);
			islemColumn.setWidth("30%");
			IymIslemler.IymLogger(personel, Sessions.getCurrent(),
					"Mahkeme Karar Ana Sayfa Girişi-Hukuk Denetçi Girişi", "",
					this, null);
		} else if (personel.getGorevTipi().equalsIgnoreCase("KOORDINATOR")) {
			atamaYapanPersonelColumn.setVisible(true);
			islemColumn.setWidth("12%");
			evrakSayiLbl.setVisible(true);
			cekilecekEvrakSayisiTxt.setVisible(true);
			evrakCekBtn.setVisible(true);
			IymIslemler.IymLogger(personel, Sessions.getCurrent(),
					"Mahkeme Karar Ana Sayfa Girişi-Koordinatör Girişi", "",
					this, null);
		} else if (personel.getGorevTipi().equalsIgnoreCase("PERSONEL")
				|| personel.getGorevTipi().equalsIgnoreCase("BASKAN")
				|| personel.getGorevTipi().equalsIgnoreCase("DAIREBASKANI")) {
			atamaYapanPersonelColumn.setVisible(false);
			islemColumn.setWidth("12%");
			IymIslemler.IymLogger(personel, Sessions.getCurrent(),
					"Mahkeme Karar Ana Sayfa Girişi-Personel Girişi", "", this,
					null);
		} else {
			this.mesajBoxGosterHata("Bu sayfaya girmeye yetkili değilsiniz.");
			IymIslemler.IymLogger(personel, Sessions.getCurrent(),
					"Mahkeme Karar Ana Sayfa Girişi-Yetkisiz Giriş", "", this,
					null);
			Sessions.getCurrent().invalidate();
			Executions.getCurrent().sendRedirect("Ana2.zul");
		}

		islenecekEvrakList
				.setRowRenderer(new MahkemeKararIslenecekEvraklarRowRenderer(
						personel));
		islenecekEvraklarTree
				.setItemRenderer(new MahkemeKararIslenecekEvraklarTreeRenderer());
		mahkemeKararList.setRowRenderer(new MahkemeKararAramaRowRenderer());
		
		kurumlarListBind();

		if (!hukukDenetciMi) {
			islenecekEvraklarListBind();
		}
		if (!personel.getGorevTipi().equalsIgnoreCase("HUKUK_NOBET")) {
			itirazEvraklarListBind();
		}

		/*
		if (Sessions.getCurrent().getRemoteAddr().substring(0, 8)
				.equals("10.60.21.")) {
			mahkemeKararAramaTab.setVisible(false);
		}
		 */

		if (mahkemeKararOnaylamaMi()) {
			itirazIslemleriTab.setVisible(true);
			itirazIslemleriTabPanel.setVisible(true);
			mahkemeKararAramaTab.setVisible(false);
			tanimlamaHavuzuGorButton.setVisible(true);
			islenecekEvraklarYenileButton.setVisible(true);
		} 
		if (mahkemeKararTanimlamaMi()) {
			itirazIslemleriTab.setVisible(true);
			itirazIslemleriTabPanel.setVisible(true);
			mahkemeKararAramaTab.setVisible(true);
		}
		if (mahkemeKararAramaVarMi()) {
			mahkemeKararAramaTab.setVisible(true);
		}
				
		/*Nobetci tarafindan yapilan evraklar icin tab panel*/
		
		nobetciTanimliEvrakSayisiTxt = (Textbox) getFellow("nobetciTanimliEvrakSayisiTxt");
		nobetciTanimliEvraklarTabPanel = (Tabpanel) getFellow("nobetciTanimliEvraklarTabPanel");
		nobetciTanimliEvraklarTab = (Tab) getFellow("nobetciTanimliEvraklarTab");
		nobetciTanimliEvraklarGrid = (Grid) getFellow("nobetciTanimliEvraklarGrid");
		nobetciTanimliEvraklarGrid.setRowRenderer(new NobetciTanimliMahkemeKararRowRenderer(personel, this));
		
		
		
		//sadece hakimlerde bu tab acik olsun
		if(personel.getYetkiList().contains(IYMSabitler.MAHKEME_KARAR_TANIMLAMA)){
			nobetciTanimliEvraklarTabPanel.setVisible(true);
			nobetciTanimliEvraklarTab.setVisible(true);
		}
		else{
			nobetciTanimliEvraklarTabPanel.setVisible(false);
			nobetciTanimliEvraklarTab.setVisible(false);
		}
			
		
	}

	private void hedefTipiListBind() {
		IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
		List<HedefTipleriPojo> hedefTipiList = srv.hedefTipiListGetir();
		Listitem li = new Listitem();
		li.setLabel("Seçiniz");
		li.setValue(null);
		li.setParent(this.hedefTipiList);
		this.hedefTipiList.setSelectedIndex(0);
		for (int i = 0; i < hedefTipiList.size(); i++) {
			HedefTipleriPojo tip = hedefTipiList.get(i);
			li = new Listitem();
			li.setLabel(tip.getHedefTipi());
			li.setValue(tip.getHedefKodu());
			li.setParent(this.hedefTipiList);
		}

	}

	private void mahkemeIliListBind() {
		Iymservice srv = ServiceManager.getIymservice();
		List<IllerPojo> mahkemeIliList = srv.illerListGetir();
		Listitem li = new Listitem();
		li.setLabel("Seçiniz");
		li.setValue(null);
		li.setParent(this.mahkemeIliList);
		this.mahkemeIliList.setSelectedIndex(0);
		for (int i = 0; i < mahkemeIliList.size(); i++) {
			IllerPojo il = mahkemeIliList.get(i);
			li = new Listitem();
			li.setLabel(il.getIlAdi());
			li.setValue(il.getIlKod());
			li.setParent(this.mahkemeIliList);
		}

	}

	private void mahkemeAdiListBind() {

		IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
		List mahkemeAdiList = srv.mahkemeAdiListGetir();
		Listitem li = new Listitem();
		li.setLabel("Seçiniz");
		li.setValue(null);
		li.setParent(this.mahkemeAdiList);
		this.mahkemeAdiList.setSelectedIndex(0);
		for (int i = 0; i < mahkemeAdiList.size(); i++) {
			MahkemeKodlariPojo mahkeme = (MahkemeKodlariPojo) mahkemeAdiList
					.get(i);
			li = new Listitem();
			li.setLabel(mahkeme.getMahkemeAdi());
			li.setValue(mahkeme.getMahkemeAdi());
			li.setParent(this.mahkemeAdiList);

		}
	}

	public void itirazEvraklarListBind() {
		IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
		// if (kurumlarList.getSelectedItem().getValue() != null) {
		// if ((((String) kurumlarList.getSelectedItem().getValue())
		// .equalsIgnoreCase("C") && personel.getGorevKodu().indexOf(
		// "1131") >= 0)
		// || (((String) kurumlarList.getSelectedItem().getValue())
		// .equalsIgnoreCase("A") && personel.getGorevKodu()
		// .indexOf("1132") >= 0)
		// || (((String) kurumlarList.getSelectedItem().getValue())
		// .equalsIgnoreCase("B") && personel.getGorevKodu()
		// .indexOf("1133") >= 0)
		// || personel.getGorevKodu().equalsIgnoreCase("113") ) {

		if (kurumlarList.getSelectedItem().getValue() != null) {
			if (personel.getGorevKodu().equalsIgnoreCase("113")
					|| personel.getGorevKodu().equalsIgnoreCase("113711")
					|| personel.getGorevKodu().equalsIgnoreCase("11371")
					|| personel.getGorevKodu().equalsIgnoreCase("113511")
					|| personel.getGorevKodu().equalsIgnoreCase("11351")) {
				String kurum = kurumlarList.getSelectedItem().getValue();
				IymIslemler.IymLogger(personel, Sessions.getCurrent(), kurum
						+ " KURUM İtiraz Kararları Listelendi", "", this, null);
				itirazEvraklariList
						.setRowRenderer(new MahkemeKararItirazEvraklariRowRenderer(
								personel));
				itirazEvraklari = srv.itirazEvraklariListesiGetir(kurum,
						personel.getIymId(), false, false);
				ListModelList lm = new ListModelList(this.itirazEvraklari);
				itirazEvraklariList.setModel(lm);
			}
		}

	}

	private void kurumlarListBind() {
		IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
		List<KullaniciYetkiliKurumPojo> kurumList = null;
		if (!personel.getGorevTipi().equalsIgnoreCase("HUKUK_NOBET")) {
			kurumList = srv.kullaniciYetkiliKurumListGetir(personel.getIymId());
		} else {
			kurumList = srv.nobetciKurumListGetir();
		}
		Listitem li = new Listitem();
		li.setLabel("Seçiniz");
		li.setValue(null);
		li.setParent(this.kurumlarList);
		this.kurumlarList.setSelectedIndex(0);
		for (int i = 0; i < kurumList.size(); i++) {
			KullaniciYetkiliKurumPojo kurum = (KullaniciYetkiliKurumPojo) kurumList
					.get(i);
			li = new Listitem();
			li.setLabel(kurum.getKurum() + " KURUM");
			li.setValue(kurum.getKurum());
			li.setParent(this.kurumlarList);
			if (kurum.getDefaultKurum() != null
					&& !kurum.getDefaultKurum().equalsIgnoreCase("")
					&& kurum.getDefaultKurum().equalsIgnoreCase("E")) {
				this.kurumlarList.setSelectedItem(li);
			} else if (hukukNobetciMi) {// nobetciye default C kurumu
				if (kurum.getKurum().equalsIgnoreCase("C")) {
					this.kurumlarList.setSelectedItem(li);
				}
			}
		}

	}

	public void ekleAcikEvrakList(Long evrakId) {
		if(this.acikEvrakIdList == null){
			this.acikEvrakIdList = new ArrayList<Long>();
		}
		
		if(!this.acikEvrakIdList.contains(evrakId)){
			this.acikEvrakIdList.add(evrakId);
			logger.info("evrakId " + evrakId + " eklendi");
		}
		else{
			logger.info("evrakId " + evrakId + " oldugu icin eklenemedi");
		}
	}

	public void cikarAcikEvrakList(Long evrakId) {
		
		this.acikEvrakIdList.remove(evrakId);
	}

	public void ekleOnaylananKararList(Long kararId) {
		
		if(this.onaylananKararlarIdList == null){
			this.onaylananKararlarIdList = new ArrayList<Long>();
		}
		
		if(!this.onaylananKararlarIdList.contains(kararId)){
			this.onaylananKararlarIdList.add(kararId);
			logger.info("kararId " + kararId + " eklendi");
		}
		else{
			logger.info("kararId " + kararId + " oldugu icin eklenemedi");
		}
		
	}

	public void onaylananKararaOKIconEkle(Treerow row) {
		try{
			//Treerow tr = (Treerow) islenecekEvraklarTree.getSelectedItem().getChildren().get(0);
			//Treecell tc = (Treecell) tr.getChildren().get(0);
			if (row != null && row.getChildren() != null && row.getChildren().size() > 0) {
				Treecell tc = (Treecell) row.getChildren().get(0);
				Space s = new Space();
				s.setWidth("5px");
				s.setParent(tc);
				Image img2 = new Image("/images/tango/ok.png");
				img2.setWidth("15px");
				img2.setParent(tc);

			}
		}catch(Exception ex){
			logger.error("onaylananKararaOKIconEkle hata", ex);
		}
	}

	
	
	
	public void cikarOnaylananKararList(Long kararId) {
		this.onaylananKararlarIdList.remove(kararId);
	}

	public void tanimlamaHavuzuEvraklariListBind() {
		if (kurumlarList.getSelectedItem().getValue() != null) {
			String kurum = kurumlarList.getSelectedItem().getValue();
			IymIslemler.IymLogger(personel, Sessions.getCurrent(), kurum
					+ " KURUM Kararları Tanımlama Havuzu Listelendi", "", this,
					null);
			try {
				IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
				this.islenebilecekEvraklar = srv.mahkemeKararEvrakiListGetir(
						kurum, personel.getIymId(), personel.getGorevTipi(),
						true, false, hukukNobetciMi);
				for (XmlEvrakPojo e : this.islenebilecekEvraklar)
					e.setMahkemeKararTuru(srv.turBelirle(e.getId(), false));
				ListModelList lm = new ListModelList(this.islenebilecekEvraklar);
				islenecekEvrakList.setModel(lm);
			} catch (Exception e) {
				logger.error("", e);
			}
			islenecekEvraklarTreeBind();

		}
	}

	public void islenecekEvraklarListBind() {
		if (kurumlarList.getSelectedItem().getValue() != null) {
			String kurum = kurumlarList.getSelectedItem().getValue();
			IymIslemler.IymLogger(personel, Sessions.getCurrent(), kurum + " KURUM Kararları Listelendi", "", this, null);
			
			try {
				IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
				
				// EVRAK_MAHKEME_KARAR_ISLEM tablosundan cekiyor
				this.islenebilecekEvraklar = srv.mahkemeKararEvrakiListGetir(						
						kurum, personel.getIymId(), personel.getGorevTipi(),
						mahkemeKararTanimlamaMi(), mahkemeKararOnaylamaMi(),
						hukukNobetciMi);
				if (/*
				.
					 * !personel.getGorevTipi().equalsIgnoreCase("KOORDINATOR")
					 * &&
					 */!personel.getGorevTipi().equalsIgnoreCase("HUKUK_NOBET")) {
					List<XmlEvrakPojo> islenebilecekEvraklar = srv // atanmamış
																	// evraklar
							.islenebilecekMahkemeKararEvrakiListGetir(kurum,
									personel.getIymId(), false,
									mahkemeKararTanimlamaMi());
					List<XmlEvrakPojo> toplamEvraklar = srv // atanmış,atanmamış
															// butun EVRAKLAR
							.islenebilecekMahkemeKararEvrakiListGetir(kurum,
									personel.getIymId(), true,
									mahkemeKararTanimlamaMi());
					islenebilecekEvrakSayisiLbl.setValue(new Integer(
							islenebilecekEvraklar.size()).toString()
							+ "/"
							+ new Integer(toplamEvraklar.size()).toString());
				} else
					islenebilecekEvrakSayisiLbl.setValue(new Integer(
							this.islenebilecekEvraklar.size()).toString());

				this.islenenEvrakSayisiLbl
						.setValue(srv.getGunlukYapilanEvrakSayisi(kurum,
								personel.getIymId()).toString());
				for (XmlEvrakPojo e : this.islenebilecekEvraklar)
					e.setMahkemeKararTuru(srv.turBelirle(e.getId(), false));
				ListModelList lm = new ListModelList(this.islenebilecekEvraklar);
				islenecekEvrakList.setModel(lm);
			} catch (Exception e) {
				logger.error("", e);
			}
			islenecekEvraklarTreeBind();

		}
	}

	public void islenecekEvraklarTreeBind() {
		islenecekEvraklarTree
				.setModel(new DefaultTreeModel<MahkemeKararTreeData>(
						treeOlustur(this.islenebilecekEvraklar)));
	}

	private TreeNode<MahkemeKararTreeData> treeOlustur(
			List<XmlEvrakPojo> evraklar) {
		DirectoryTreeNode<MahkemeKararTreeData> root = new DirectoryTreeNode<MahkemeKararTreeData>(
				null, evrakTree(evraklar), true);
		return root;
	}

	private DirectoryTreeNodeCollection<MahkemeKararTreeData> evrakTree(
			List<XmlEvrakPojo> evraklar) {
		DirectoryTreeNodeCollection<MahkemeKararTreeData> agacDallari = new DirectoryTreeNodeCollection<MahkemeKararTreeData>();
		for (XmlEvrakPojo e : evraklar) {
			MahkemeKararTreeData mahkemeKararTreeData = new MahkemeKararTreeData(e, null, null,this.acikEvrakIdList, this.onaylananKararlarIdList);
			agacDallari .add(new DirectoryTreeNode<MahkemeKararTreeData>(mahkemeKararTreeData,mahkemeKarariTree(e), true));
		}
		return agacDallari;
	}

	private DirectoryTreeNodeCollection<MahkemeKararTreeData> mahkemeKarariTree(XmlEvrakPojo evrak) {
		DirectoryTreeNodeCollection<MahkemeKararTreeData> agacDallari = new DirectoryTreeNodeCollection<MahkemeKararTreeData>();
		IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
		List<MahkemeKararTalepPojo> kararlar = srv.mahkemeKararTalepListGetir(evrak.getId());
		for (MahkemeKararTalepPojo m : kararlar) {
			m.setMahkemeKararTuru(srv.turBelirle(m, false));
			MahkemeKararIslemPojo islem = srv.getMahkemeKararIslemBilgileri(m.getId());
			MahkemeKararTreeData mahkemeKararTreeData = new MahkemeKararTreeData(evrak, m, islem, this.acikEvrakIdList, this.onaylananKararlarIdList);
			agacDallari.add(new DirectoryTreeNode<MahkemeKararTreeData>(mahkemeKararTreeData, null, true));
		}
		return agacDallari;
	}

	public void kurumListSecildi() {
		islenecekEvraklarListBind();
		itirazEvraklarListBind();
	}

	public void yenileIslenecekEvrak() {
		islenecekEvraklarListBind();
		itirazEvraklarListBind();
	}

	public void sifirlaIslenecekEvrak() {
		acikEvrakIdList = new ArrayList<Long>();
		onaylananKararlarIdList = new ArrayList<Long>();
		islenecekEvraklarListBind();
		itirazEvraklarListBind();
	}

	public void tamaminiGoster() {
		islenecekEvrakList.setPageSize(100000);
	}

	public void itirazTamaminiGoster() {
		itirazEvraklariList.setPageSize(100000);
	}

	public void iadeEt() {

	}

	public void yeniSayfaAc() {

		try {

			mahkemeKararKayitWindow = (MahkemeKararKayitWindow) this
					.getDesktop().getAttribute("mahkemeKararKayitWindow");
			IymIslemler.IymLogger(personel, Sessions.getCurrent(),
					"Mahkeme Karar-Evrak Gör", "Evrak Id : "
							+ mahkemeKararKayitWindow.getXmlEvrak().getId(),
					this, null);
			if (evrakOnizlemeCheck.isChecked()) {
				mahkemeKararKayitWindow.getDosyaGorFrame().setVisible(false);
				EvrakOzet e = new EvrakOzet();
				e.setEvrakId(mahkemeKararKayitWindow.getXmlEvrak().getId());
				e.setEvrakHavaleId(new Long("1"));
				Rows rows = this.islenecekEvrakList.getRows();
				if (rows != null && rows.getChildren().size() > 0) {
					new EvrakGor((Row) rows.getFirstChild(), e.getEvrakId(), e.getEvrakHavaleId()).evrakGoster(
							this, "mahkemeKararAnaSayfaWindow");
				}
			} else {
				mahkemeKararKayitWindow.getDosyaGorFrame().setVisible(true);
			}
		} catch (Exception ex) {
			logger.error("", ex);
		}
	}

	public void tumunuSec() {
		int aktifPage = islenecekEvrakList.getActivePage();
		int size = islenecekEvrakList.getPageSize();
		Rows rows = islenecekEvrakList.getRows();
		List<Component> lst = rows.getChildren();
		Row row;
		Checkbox chk;
		for (int i = size * aktifPage; i < size * aktifPage + size
				&& i < lst.size(); i++) {
			row = (Row) lst.get(i);
			XmlEvrakPojo evrak = row.getValue();
			if (hepsiCheckbox.isChecked()) {
				row.setStyle("background-color:#a1a2e4");
			} else if (evrak.getAcilMi() != null
					&& !evrak.getAcilMi().equalsIgnoreCase("")
					&& evrak.getAcilMi().equalsIgnoreCase("E")) {
				row.setStyle("background-color:#ff8a8a");
			} else {
				row.setStyle("");
			}
			try {
				chk = (Checkbox) row.getChildren().get(6);
				chk.setChecked(hepsiCheckbox.isChecked());
			} catch (Exception e) {
				logger.error("", e);
				if (evrak.getAcilMi() != null
						&& !evrak.getAcilMi().equalsIgnoreCase("")
						&& evrak.getAcilMi().equalsIgnoreCase("E")) {
					row.setStyle("background-color:#ff8a8a");
				} else {
					row.setStyle("");
				}
			}
		}
	}

	public void evrakCek() {
		String kurum = kurumlarList.getSelectedItem().getValue();
		IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
		if (cekilecekEvrakSayisiTxt.getValue() == null || cekilecekEvrakSayisiTxt.getValue().trim().equalsIgnoreCase("")
				|| cekilecekEvrakSayisiTxt.getValue().trim()
						.equalsIgnoreCase("0")) {
			if (hukukNobetciMi) {
				try {
					List<Component> rowTemp = islenecekEvrakList.getRows().getChildren();


					Row r;
					XmlEvrakPojo evrak = null;
					int sayi = 0;
					for (Component c : rowTemp) {
						r = (Row) c;
						try {
							Checkbox chk = (Checkbox) r.getChildren().get(0).getChildren().get(3);
							if (chk.isChecked()) {
								evrak = r.getValue();

								srv.mahkemeKararTalepListGetir(evrak.getId());

								if (srv.mahkemeKararAta(evrak.getId(), personel.getIymId(), personel.getIymId(), false))
									sayi++;
							}
						} catch (Exception e) {
							logger.error("", e);
						}
					}
					IymIslemler.IymLogger(personel, Sessions.getCurrent(),
							"Mahkeme Karar-Evrak Çek", "Kurum : " + kurum
									+ " Sayı  : " + sayi, this, null);
					islenecekEvraklarListBind();
					this.mesajBoxGosterHata("Toplam " + sayi
							+ " adet evrak atandı!!!");
				} catch (Exception e) {
					logger.error("", e);
				}

			} else
				this.mesajBoxGosterHata("Çekilecek Evrak Sayısı Boş Olamaz!!!");
			return;
		} else {
			try {
				Long sayi = new Long(cekilecekEvrakSayisiTxt.getValue());
				evrakAtamaQueue.execute(new EvrakAtamaRunnable(kurum, personel
						.getIymId(), mahkemeKararTanimlamaMi(), sayi));
				Thread.sleep(500);
				IymIslemler.IymLogger(personel, Sessions.getCurrent(),
						"Mahkeme Karar-Evrak Çek", "Kurum : " + kurum
								+ " Sayı  : " + sayi, this, null);
				islenecekEvraklarListBind();
			} catch (Exception e) {
				logger.error("Çekilecek Evrak Sayısı Hatalı", e);
				this.mesajBoxGosterHata("Çekilecek Evrak Sayısı Hatalı!!!");
				return;
			}
		}

	}

	public void atamaKaldir() {
		try {
			List<Component> rowTemp = islenecekEvrakList.getRows()
					.getChildren();

			Row r;
			XmlEvrakPojo evrak = null;
			int sayi = 0;
			for (Component c : rowTemp) {
				r = (Row) c;
				try {
					Checkbox chk = (Checkbox) r.getChildren().get(6);

					if (chk.isChecked()) {
						evrak = r.getValue();
						IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
						if (srv.mahkemeKararAtamaKaldir(evrak.getId(),
								personel.getIymId(), mahkemeKararTanimlamaMi()))
							sayi++;
					}
				} catch (Exception e) {
					logger.error("", e);
				}
			}
			IymIslemler.IymLogger(personel, Sessions.getCurrent(),
					"Mahkeme Karar-Atama Kaldırıldı", "Sayı  : " + sayi, this,
					null);
			islenecekEvraklarListBind();
			this.mesajBoxGosterHata("Toplam " + sayi
					+ " adet atanan evrak kaldirildi!!!");
		} catch (Exception e) {
			logger.error("", e);
		}
	}
	
	private List<XmlEvrakPojo> getSeciliEvrakList(){
		
		List<XmlEvrakPojo> list = new ArrayList<XmlEvrakPojo>();
		
		try{
				
			List<Component> rowTemp = islenecekEvrakList.getRows().getChildren();
			Row r;
			XmlEvrakPojo evrak = null;
			for (Component c : rowTemp) {
				r = (Row) c;			
				if(r.getChildren() != null && r.getChildren().size() > 7){
					Checkbox chk = (Checkbox) r.getChildren().get(6);
					if (chk.isChecked()) {
						evrak = r.getValue();
						if(evrak != null){
							list.add(evrak);
						}
					}
				}
			}
		}catch(Exception ex){
			logger.error("getSeciliEvrakList hata", ex);
		}
		
		return list;
		
	}
	
	//Redmine#2640 geregi. Yazi : KT.2021-005170 Madde#2 talebi
	public void seciliEvrakAta(){
		
		try {
			String kurum = kurumlarList.getSelectedItem().getValue();
			
			List<XmlEvrakPojo> seciliEvraklar = getSeciliEvrakList();
			
			if(seciliEvraklar == null || seciliEvraklar.size() < 1){
				this.mesajBoxGosterHata("Atama yapmak için bir tane evrak seçiniz");
				return;
			}
			
			XmlEvrakPojo seciliEvrak = seciliEvraklar.get(0);
			IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();

			boolean evrakAta = srv.mahkemeKararAta(seciliEvrak.getId(), personel.getIymId(), personel.getIymId(), false);
			if (evrakAta){
				Messagebox.show("Evrak ataması yapılmıştır.");
				IymIslemler.IymLogger(personel, Sessions.getCurrent(), "Mahkeme Karar Evrak Ata", "Kullanıcı : " + personel.getKullaniciAd() + " Evrak Sayı  : " + seciliEvrak.getEvrakSiraNo(), this, "Başarılı");
				islenecekEvraklarListBind();
			}
			else{
				this.mesajBoxGosterHata("Evrak atama hatası. Yeniden deneyin");
			}
	
		} catch (Exception ex) {
			logger.error("Evrak Atama Hatasi ", ex);
			LoggerService.getIymKubikLog().error("Evrak Atama Hatasi " + ex.getMessage());
		}
	}
	
	

	public void mahkemeKararAra() {
		IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
		String clientIp = HttpUtils.getClientIpAddress(Executions.getCurrent());

		if (this.kurumlarList.getSelectedIndex() == 0) {
			Messagebox.show("Kurum seçiniz");
			return;
		}

		if (this.aramaAciklamaText.getValue().trim().equalsIgnoreCase("")) {
			Messagebox.show("Arama açıklaması giriniz");
			return;

		}
		if (this.hedefTipiList.getSelectedIndex() == 0
				&& this.mahkemeAdiList.getSelectedIndex() == 0
				&& this.mahkemeIliList.getSelectedIndex() == 0
				&& this.hedefNoText.getValue().trim().equalsIgnoreCase("")
				&& this.aidiyatNoText.getValue().trim().equalsIgnoreCase("")
				&& this.evrakGirisTarihi1.getValue() == null
				&& this.evrakGirisTarihi2.getValue() == null
				&& this.evrakSiraNoText.getValue().trim().equalsIgnoreCase("")
				&& this.evrakNoText.getValue().trim().equalsIgnoreCase("")
				&& this.evrakAciklamaText.getValue().trim().equalsIgnoreCase("")
				&& this.kararNoText.getValue().trim().equalsIgnoreCase("")
				&& this.sorusturmaNoText.getValue().trim().equalsIgnoreCase("")) {
			Messagebox.show("En az bir alan doldurulmalıdır");
		} else {

			List<MahkemeKararAramaPojo> aramaSonucList = null;
			if(iadeAraCB.isChecked()){
				aramaSonucList = srv.mahkemeKararTalepIadeAra(
						(String) this.hedefTipiList.getSelectedItem().getValue(),
						(String) this.mahkemeAdiList.getSelectedItem().getValue(),
						(String) this.mahkemeIliList.getSelectedItem().getValue(),
						this.hedefNoText.getValue(), this.aidiyatNoText.getValue(),
						this.evrakGirisTarihi1.getValue(),
						this.evrakGirisTarihi2.getValue(),
						this.evrakSiraNoText.getValue(),
						this.evrakNoText.getValue(),
						this.evrakAciklamaText.getValue(),
						this.kararNoText.getValue(),
						this.sorusturmaNoText.getValue(),
						(String) this.kurumlarList.getSelectedItem().getValue(),
						personel, clientIp, aramaAciklamaText.getValue());

				mahkemeKararList.setRowRenderer(new MahkemeKararIadeAramaRowRenderer());

			}
			else {

				aramaSonucList = srv.mahkemeKararAra(
						(String) this.hedefTipiList.getSelectedItem().getValue(),
						(String) this.mahkemeAdiList.getSelectedItem().getValue(),
						(String) this.mahkemeIliList.getSelectedItem().getValue(),
						this.hedefNoText.getValue(), this.aidiyatNoText.getValue(),
						this.evrakGirisTarihi1.getValue(),
						this.evrakGirisTarihi2.getValue(),
						this.evrakSiraNoText.getValue(),
						this.evrakNoText.getValue(),
						this.evrakAciklamaText.getValue(),
						this.kararNoText.getValue(),
						this.sorusturmaNoText.getValue(),
						(String) this.kurumlarList.getSelectedItem().getValue(),
						personel, clientIp, aramaAciklamaText.getValue());

				mahkemeKararList.setRowRenderer(new MahkemeKararAramaRowRenderer());
			}

			if(aramaSonucList == null){
				aramaSonucList = new ArrayList<MahkemeKararAramaPojo>();
			}

			TemelIslemler.renderTime(aramaSonucList.size());
			String arananText = "";
			if (this.kurumlarList.getSelectedItem().getValue() != null)
				arananText += " KurumKod : "
						+ this.kurumlarList.getSelectedItem().getValue();
			if (this.hedefNoText.getValue() != null)
				arananText += " HedefNo : " + this.hedefNoText.getValue();
			if (this.hedefTipiList.getSelectedItem().getValue() != null)
				arananText += " HedefTipi : "
						+ this.hedefTipiList.getSelectedItem().getValue();
			if (this.mahkemeAdiList.getSelectedItem().getValue() != null)
				arananText += " MahkemeAdi : "
						+ this.mahkemeAdiList.getSelectedItem().getValue();
			if (this.mahkemeIliList.getSelectedItem().getValue() != null)
				arananText += " MahkemeIli : "
						+ this.mahkemeIliList.getSelectedItem().getValue();
			if (this.evrakSiraNoText.getValue() != null)
				arananText += " EvrakSiraNo : "
						+ this.evrakSiraNoText.getValue();
			if (this.evrakSiraNoText.getValue() != null)
				arananText += " EvrakNoText : " + this.evrakNoText.getValue();
			if (this.aidiyatNoText.getValue() != null)
				arananText += " AidiyatNoText : "
						+ this.aidiyatNoText.getValue();
			if (this.evrakGirisTarihi1.getValue() != null)
				arananText += " Tarih1 : "
						+ TemelIslemler.TurkTarih(this.evrakGirisTarihi1
								.getValue());
			if (this.evrakGirisTarihi2.getValue() != null)
				arananText += " Tarih2 : "
						+ TemelIslemler.TurkTarih(this.evrakGirisTarihi2
								.getValue());
			if (this.evrakAciklamaText.getValue() != null)
				arananText += " EvrakAciklama : "
						+ this.evrakAciklamaText.getValue();
			if (this.kararNoText.getValue() != null)
				arananText += " MahkemeKararNo : "
						+ this.kararNoText.getValue();
			if (this.evrakAciklamaText.getValue() != null)
				arananText += " SorusturmaNo : "
						+ this.sorusturmaNoText.getValue();
			if (aramaSonucList.size() == 0)
				arananText += " SONUÇ : Herhangi bir sonuç bulunamadı!";
			else
				arananText += " SONUÇ : " + aramaSonucList.size()
						+ " adet sonuç bulundu.";

			IymIslemler.IymLogger(personel, Sessions.getCurrent(),
					"Mahkeme Karar Arandı", arananText, this, null);
			if (aramaSonucList.size() == 0)
				Messagebox.show("Herhangi bir sonuç bulunamadı!");
			for (MahkemeKararAramaPojo e : aramaSonucList)
				e.setMahkemeKararTuru(srv.turBelirle(e.getEvrakId(), false));

			ListModelList lm = new ListModelList(aramaSonucList);
			mahkemeKararList.setModel(lm);
		}

	}

	public void aramaTemizle() {
		this.hedefTipiList.setSelectedIndex(0);
		this.mahkemeAdiList.setSelectedIndex(0);
		this.mahkemeIliList.setSelectedIndex(0);
		this.hedefNoText.setValue("");
		this.aidiyatNoText.setValue("");
		this.evrakGirisTarihi1.setValue(null);
		this.evrakGirisTarihi2.setValue(null);
		this.evrakSiraNoText.setValue("");
		this.evrakNoText.setValue("");
		this.evrakAciklamaText.setValue("");
		this.kararNoText.setValue("");
		this.sorusturmaNoText.setValue("");
		this.aramaAciklamaText.setValue("");
		this.mahkemeKararList.setModel(new ListModelList());

	}

	public void sehvenImhaAra() {
		String hedefNo = this.hedefNoText.getValue();

		String kurumKod = (String) this.kurumlarList.getSelectedItem()
				.getValue();

		if (hedefNo.trim().equalsIgnoreCase("") || kurumKod == null
				|| kurumKod.trim().equalsIgnoreCase(""))
			Messagebox.show("İşlem Yapılacak Kurum ve Hedef No Bilgisi Boş Olmamalı!");
		else {
			String parametre = "hedefNo=" + hedefNo + ";kurumKod=" + kurumKod
					+ ";aciklama=" + aramaAciklamaText.getValue();
			String encParametre = IymIslemler.encodeString(parametre);

			IymIslemler.IymLogger(personel, Sessions.getCurrent(),
					"Sehven imha Kaldırma Aramasi Yapildi", " Hedef No : "
							+ hedefNo + " KurumKod : " + kurumKod, this, null);

			Window win = new Window();
			win.setParent(this);

			Include inc = new Include();
			inc.setParent(win);
			inc.setSrc("/forms/iym/mahkemeKarar/SehvenImhaKaldirModal.zul?param="
					+ encParametre);

			win.setTitle(hedefNo
					+ " Nolu Hedefin Sehven İmha Kaldırılabilir Kararlar Listesi");
			win.setWidth("95%");
			win.setHeight("75%");
			win.setClosable(true);
			win.doModal();
		}
	}

	@Override
	public void temizle() {
		// TODO Auto-generated method stub

	}

	public Checkbox getEvrakOnizlemeCheck() {
		return evrakOnizlemeCheck;
	}

	public void setEvrakOnizlemeCheck(Checkbox evrakOnizlemeCheck) {
		this.evrakOnizlemeCheck = evrakOnizlemeCheck;
	}

	public Listbox getKurumlarList() {
		return kurumlarList;
	}

	public void setKurumlarList(Listbox kurumlarList) {
		this.kurumlarList = kurumlarList;
	}

	private boolean hukukDenetciMi() {
		try {
			if (personel.getYetkiList().contains(
					IYMSabitler.MAHKEME_KARAR_DENETCI))
				return true;
		} catch (Exception ex) {
			logger.error("", ex);
		}
		return false;
	}

	private boolean mahkemeKararOnaylamaMi() {
		try {
			if (personel.getYetkiList().contains(IYMSabitler.MAHKEME_KARAR_ONAYLAMA))
				return true;
		} catch (Exception ex) {
			logger.error("", ex);
		}
		return false;
	}

	private boolean mahkemeKararTanimlamaMi() {
		try {
			if (personel.getYetkiList().contains(
					IYMSabitler.MAHKEME_KARAR_TANIMLAMA ))
				return true;
		} catch (Exception ex) {
			logger.error("", ex);
		}
		return false;
	}
	private boolean mahkemeKararAramaVarMi() {
		try {
			if (personel.getYetkiList().contains( 
					IYMSabitler.MAHKEME_KARAR_ARAMA))
				return true;
		} catch (Exception ex) {
			logger.error("", ex);
		}
		return false;
	}
	
	
	public void nobetciTanimliEvrakCek() {
		
		if (kurumlarList.getSelectedItem() == null  || kurumlarList.getSelectedItem().getValue() == null) {
			this.mesajBoxGosterHata("Kurum seçiniz.");
			return;
		}
		
		String kurum = kurumlarList.getSelectedItem().getValue();
		IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();

		int nobetciTanimliEvrakSayisi = 0;
		String nobetciTanimliEvrakSayisiStr = nobetciTanimliEvrakSayisiTxt.getValue();

		if (TemelIslemler.isNullOrEmpty(nobetciTanimliEvrakSayisiStr)) {
			Messagebox.show("Çekilecek evrak sayısını seçiniz");
			return;
		}

		try {
			nobetciTanimliEvrakSayisi = Integer.parseInt(nobetciTanimliEvrakSayisiStr);
		} catch (Exception ex) {
			logger.error("", ex);
			nobetciTanimliEvrakSayisi = 0;
		}

		if (nobetciTanimliEvrakSayisi < 1) {
			Messagebox.show("Çekilecek evrak sayısını rakam olarak giriniz.");
			return;
		}
		
		try {
			List<Component> rowTemp = nobetciTanimliEvraklarGrid.getRows().getChildren();

			Row r;
			XmlEvrakPojo evrak = null;
			int sayi = 0;
			for (Component c : rowTemp) {
				r = (Row) c;
				evrak = r.getValue();
				if (srv.nobetciTanimliMahkemeKararCek(evrak.getId(), personel.getIymId())) {
					sayi++;
					if (sayi >= nobetciTanimliEvrakSayisi) {
						break;
					}
				}

			}
			IymIslemler.IymLogger(personel, Sessions.getCurrent(), "Noönetçi Tanımlı Mahkeme Karar-Evrak Çek", "Kurum : " + kurum + " Sayı  : " + sayi, this, null);
			nobetciTanimliEvraklariGoster();
			this.mesajBoxGosterHata("Toplam " + sayi + " adet evrak atandı!!!");			
		} catch (Exception e) {
			logger.error("", e);
		}

		nobetciTanimliEvraklariGoster();
		
	}

	//Redmine: 4242 Geregi.
	//Task #4242: Gece makos'u güündüz makosu gibi hakim tarafından çekilen bir evrak diğer hakimler tarafından görülebilmeli ve çekilebilmeli.
	public void nobetciTanimliEvrakAtamaDegistir(XmlEvrakPojo evrak) {

		IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();

		try {
			if (evrak == null) {
				Messagebox.show("Atanacak evrak boş");
				return;
			}

			if (Messagebox.show(evrak.getEvrakSiraNo() + " sıra numaralı evrakı çekmek istiyor musunuz?", "Dikkat !!", Messagebox.YES | Messagebox.NO, "") != Messagebox.YES) {
				return;
			}
			//bu evrak nobetci tanimli oldugu evrak
			MahkemeKararAtamaPojo atama = new MahkemeKararAtamaPojo();
			atama.setEvrakId(evrak.getId());
			atama.setKullaniciId((int) personel.getIymId());
			atama.setDurum("A");
			atama.setSeviye(2);
			atama.setAciklama("");
			atama.setSebebi(2);
			atama.setGonderilenId((int) personel.getIymId());

			if (!srv.nobetEvrakAtamaDegistir(atama)) {
				throw new Exception("Hata: Nöbetçi Tanımlı Evrak Atandı Atama Ypılamadı!");
			} else {
				nobetciTanimliEvraklariGoster();
			}

			IymIslemler.IymLogger(personel, Sessions.getCurrent(), "Noönetçi Tanımlı Mahkeme Karar-Evrak Atama", " Sayı  : " + evrak.getEvrakSiraNo(), this, null);
		} catch (Exception e) {
			logger.error("", e);
		}

	}


	public void nobetciTanimliEvraklariGoster() {
		if (kurumlarList.getSelectedItem() != null && kurumlarList.getSelectedItem().getValue() != null) {
			String kurum = (String)kurumlarList.getSelectedItem().getValue() ;
			IymIslemler.IymLogger(personel, Sessions.getCurrent(), kurum + " KURUM nobetOnayli kararları listelendi", "", this, null);
			try {
				IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
				
				boolean tanimlama = mahkemeKararTanimlamaMi();
				if(!tanimlama){
					this.mesajBoxGosterHata("Bu işlemi yapma yetkiniz yoktur.");
					return;
				}
				
				List<XmlEvrakPojo> nobetciIslenebilecekEvraklar = srv.nobetciOnayliMahkemeKararEvrakiListGetir(kurum, personel.getIymId());

				for (XmlEvrakPojo e : nobetciIslenebilecekEvraklar){
					
					List<MahkemeKararPojo> kararlar = srv.mahkemeKararListesiGetir(e.getId());
					if(kararlar != null && kararlar.size() > 0){
						e.setMahkemeKararTuru(srv.turBelirle(kararlar.get(0),true));
					}
					else{
						MAHKEME_KARAR_ISLEM_TURU tur = srv.turBelirle(e.getId(), true);
						e.setMahkemeKararTuru(tur);
					}
				}
				
				ListModelList lm = new ListModelList(nobetciIslenebilecekEvraklar);
				nobetciTanimliEvraklarGrid.setModel(lm);
			} catch (Exception e) {
				logger.error("", e);
			}
			islenecekEvraklarTreeBind();

		}else{
			this.mesajBoxGosterHata("Kurum seçiniz.");
		}
	}
	
}
