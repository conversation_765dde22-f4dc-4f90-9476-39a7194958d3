# Dinamik API URL Konfigürasyonu

Frontend uygulaması artık API URL'ini dinamik olarak belirleyebilir. Environment variable'lar static değerleri ezecek şekilde priority-based bir sistem kurulmuştur.

## Priority Sırası

1. **Runtime Environment Variable** (En yüksek <PERSON>)
   - `window.APP_CONFIG.API_URL`
   - Docker/Kubernetes deployment için

2. **Build-time Environment Variable**
   - `process.env.NG_APP_API_URL`
   - Angular build sırasında

3. **Server Injected Environment**
   - `window.ENV.API_URL`
   - Nginx vb. tarafından inject edilebilir

4. **Dynamic URL** (Location-based)
   - `window.location` bazlı dinamik URL
   - Development: `http://localhost:8080`
   - Production: `https://yourdomain.com:8080`

5. **Static Fallback** (En düşük öncelik)
   - `http://localhost:8080`

## Kullanım Örnekleri

### 1. Docker ile Deployment

```bash
# Docker container'ı environment variable ile çalıştır
docker run -e API_URL=https://api.yourdomain.com:8080 iym-frontend

# Docker Compose ile
API_URL=https://api.yourdomain.com:8080 docker-compose up
```

### 2. Build-time Environment Variable

```bash
# Angular build sırasında
export NG_APP_API_URL=https://api.yourdomain.com:8080
ng build --configuration=production

# Veya tek seferde
NG_APP_API_URL=https://api.yourdomain.com:8080 ng build --configuration=production
```

### 3. Runtime JavaScript Injection

```html
<!-- index.html içinde -->
<script>
    window.APP_CONFIG = {
        API_URL: 'https://api.yourdomain.com:8080'
    };
</script>
```

### 4. Nginx ile Server-side Injection

```nginx
# nginx.conf
location / {
    sub_filter '${API_URL}' 'https://api.yourdomain.com:8080';
    sub_filter_once off;
    try_files $uri $uri/ /index.html;
}
```

## Development vs Production

### Development
- Otomatik olarak `http://localhost:8080` kullanır
- Environment variable ile override edilebilir

### Production
- Otomatik olarak current domain + port 8080 kullanır
- Environment variable ile override edilebilir

## Debug

Console'da hangi API URL'in kullanıldığını görmek için:

```javascript
console.log('Current API URL:', environment.apiUrl);
```

Veya browser console'da:

```javascript
// Runtime config'i kontrol et
console.log(window.APP_CONFIG);
console.log(window.ENV);
```
