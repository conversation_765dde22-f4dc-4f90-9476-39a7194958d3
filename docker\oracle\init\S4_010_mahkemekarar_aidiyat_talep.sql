-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MAHKEME_AIDIYAT_TALEP_SEQ if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = '<PERSON><PERSON><PERSON><PERSON>_AIDIYAT_TALEP_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAHKEME_AIDIYAT_TALEP_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create MAHKEME_AIDIYAT_TALEP table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_AIDIYAT_TALEP';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_AIDIYAT_TALEP (
      ID NUMBER NOT NULL,
      <PERSON>HKEME_ID NUMBER NOT NULL,
      AIDIYAT_KOD VARCHAR2(25 BYTE) NOT NULL,
      DURUMU VARCHAR2(10 BYTE),
      CONSTRAINT MAHKEME_AIDIYAT_TALEP_ID_IDX PRIMARY KEY (ID) ENABLE
    )';

    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX iym.MAHKEME_AIDIYAT_TALEP_ID ON iym.MAHKEME_AIDIYAT_TALEP (MAHKEME_ID ASC, AIDIYAT_KOD ASC)';
  END IF;
END;
/



COMMIT;
