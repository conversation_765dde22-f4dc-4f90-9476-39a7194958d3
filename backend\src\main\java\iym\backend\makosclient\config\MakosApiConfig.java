package iym.backend.makosclient.config;

import iym.backend.util.RestUtils;
import iym.makos.api.client.gen.api.HealthCheckControllerApi;
import iym.makos.api.client.gen.api.MahkemeKararTalepControllerApi;
import iym.makos.api.client.gen.handler.ApiClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * Configuration class for MAKOS API client
 * Configures the generated API client as Spring Beans
 */
@Configuration
public class MakosApiConfig {

    @Value("${makos.api.base-url:http://localhost:5000/makosapi}")
    private String baseUrl;

    @Value("${makos.api.username:}")
    private String username;

    @Value("${makos.api.password:}")
    private String password;

    @Value("${makos.api.connect-timeout:5000}")
    private int connectTimeout;

    @Value("${makos.api.read-timeout:30000}")
    private int readTimeout;

    /**
     * Creates the ApiClient bean using RestUtils
     *
     * @return ApiClient instance
     */
    @Bean
    public ApiClient makosApiClient() {
        // Create RestTemplate using RestUtils
        RestTemplate restTemplate = RestUtils.getRestTemplate(baseUrl, connectTimeout, readTimeout);

        // Create ApiClient with RestTemplate
        ApiClient apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(baseUrl);

        // Configure authentication if username and password are provided
        if (username != null && !username.trim().isEmpty() &&
                password != null && !password.trim().isEmpty()) {
            apiClient.setUsername(username);
            apiClient.setPassword(password);
        }

        return apiClient;
    }

    /**
     * Creates the MahkemeKararControllerApi bean
     *
     * @param apiClient ApiClient instance
     * @return MahkemeKararTalepControllerApi instance
     */
    @Bean
    public MahkemeKararTalepControllerApi mahkemeKararTalepControllerApi(ApiClient apiClient) {
        return new MahkemeKararTalepControllerApi(apiClient);
    }

    /**
     * Creates the HealthCheckControllerApi bean
     *
     * @param apiClient ApiClient instance
     * @return HealthCheckControllerApi instance
     */
    @Bean
    public HealthCheckControllerApi healthCheckControllerApi(ApiClient apiClient) {
        return new HealthCheckControllerApi(apiClient);
    }
}

