<div class="p-4">
  <!-- Başlık -->
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-2xl font-bold text-gray-800">
      <i class="pi pi-search mr-2"></i>
      İletişimin Tespiti Evrak Sorgulama
    </h2>
    <div class="flex gap-2">
      <p-button 
        icon="pi pi-file-excel" 
        label="Excel" 
        severity="success"
        size="small"
        (onClick)="excelAktar()">
      </p-button>
      <p-button 
        icon="pi pi-file-pdf" 
        label="PDF" 
        severity="danger"
        size="small"
        (onClick)="pdfAktar()">
      </p-button>
    </div>
  </div>

  <!-- İstatistik Kartları -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4" *ngIf="sonuclar.length > 0">
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-center">
        <i class="pi pi-list text-blue-500 text-2xl mr-3"></i>
        <div>
          <p class="text-sm text-blue-600 font-medium">Toplam Kayıt</p>
          <p class="text-2xl font-bold text-blue-800">{{ sonucSayisiniGetir().toplam }}</p>
        </div>
      </div>
    </div>
    
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
      <div class="flex items-center">
        <i class="pi pi-check-circle text-green-500 text-2xl mr-3"></i>
        <div>
          <p class="text-sm text-green-600 font-medium">Tamamlanan</p>
          <p class="text-2xl font-bold text-green-800">{{ sonucSayisiniGetir().tamamlanan }}</p>
        </div>
      </div>
    </div>
    
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div class="flex items-center">
        <i class="pi pi-clock text-yellow-500 text-2xl mr-3"></i>
        <div>
          <p class="text-sm text-yellow-600 font-medium">Bekleyen</p>
          <p class="text-2xl font-bold text-yellow-800">{{ sonucSayisiniGetir().bekleyen }}</p>
        </div>
      </div>
    </div>
    
    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
      <div class="flex items-center">
        <i class="pi pi-times-circle text-red-500 text-2xl mr-3"></i>
        <div>
          <p class="text-sm text-red-600 font-medium">Hata</p>
          <p class="text-2xl font-bold text-red-800">{{ sonucSayisiniGetir().hata }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Arama ve Filtreleme -->
  <p-card header="Arama ve Filtreleme" class="mb-4">
    <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-4">
      
      <!-- Evrak No -->
      <div class="flex flex-col">
        <label class="text-sm font-medium text-gray-700 mb-2">Evrak No</label>
        <input 
          pInputText 
          [(ngModel)]="aramaFiltresi.evrakNo"
          placeholder="Evrak numarası..."
          class="w-full">
      </div>

      <!-- Hedef Bilgisi -->
      <div class="flex flex-col">
        <label class="text-sm font-medium text-gray-700 mb-2">Hedef Bilgisi</label>
        <input 
          pInputText 
          [(ngModel)]="aramaFiltresi.hedefBilgisi"
          placeholder="Telefon, IP, email..."
          class="w-full">
      </div>

      <!-- Tespit Türü -->
      <div class="flex flex-col">
        <label class="text-sm font-medium text-gray-700 mb-2">Tespit Türü</label>
        <p-dropdown 
          [(ngModel)]="aramaFiltresi.tespitiTuru"
          [options]="tespitiTurleri"
          optionLabel="label"
          optionValue="value"
          placeholder="Tür seçiniz"
          class="w-full">
        </p-dropdown>
      </div>

      <!-- Durum -->
      <div class="flex flex-col">
        <label class="text-sm font-medium text-gray-700 mb-2">Durum</label>
        <p-dropdown 
          [(ngModel)]="aramaFiltresi.durum"
          [options]="durumSecenekleri"
          optionLabel="label"
          optionValue="value"
          placeholder="Durum seçiniz"
          class="w-full">
        </p-dropdown>
      </div>

      <!-- Başlangıç Tarihi -->
      <div class="flex flex-col">
        <label class="text-sm font-medium text-gray-700 mb-2">Başlangıç Tarihi</label>
        <p-calendar 
          [(ngModel)]="baslangicTarihi"
          dateFormat="dd/mm/yy"
          placeholder="Başlangıç tarihi"
          [showIcon]="true"
          class="w-full">
        </p-calendar>
      </div>

      <!-- Bitiş Tarihi -->
      <div class="flex flex-col">
        <label class="text-sm font-medium text-gray-700 mb-2">Bitiş Tarihi</label>
        <p-calendar 
          [(ngModel)]="bitisTarihi"
          dateFormat="dd/mm/yy"
          placeholder="Bitiş tarihi"
          [showIcon]="true"
          class="w-full">
        </p-calendar>
      </div>
    </div>

    <!-- Arama Butonları -->
    <div class="flex gap-2">
      <p-button 
        icon="pi pi-search" 
        label="Ara"
        (onClick)="aramaYap()"
        [loading]="yukleniyor">
      </p-button>
      <p-button 
        icon="pi pi-times" 
        label="Temizle"
        severity="secondary"
        (onClick)="filtreleriTemizle()">
      </p-button>
    </div>
  </p-card>

  <!-- Sonuçlar Tablosu -->
  <p-card header="İletişim Tespiti Sonuçları">
    
    <!-- Tablo Araç Çubuğu -->
    <p-toolbar class="mb-4">
      <div class="p-toolbar-group-start">
        <span class="p-input-icon-left">
          <i class="pi pi-search"></i>
          <input 
            pInputText 
            type="text" 
            [(ngModel)]="globalAramaMetni"
            (input)="globalArama()"
            placeholder="Tabloda ara..." 
            class="w-80">
        </span>
      </div>
      <div class="p-toolbar-group-end">
        <p-button 
          icon="pi pi-refresh" 
          severity="secondary"
          size="small"
          (onClick)="kayitlariYenile()"
          pTooltip="Yenile"
          tooltipPosition="top">
        </p-button>
      </div>
    </p-toolbar>

    <!-- Tablo -->
    <p-table 
      [value]="filtrelenmissonuclar" 
      [loading]="yukleniyor"
      [paginator]="true" 
      [rows]="20"
      [rowsPerPageOptions]="[10, 20, 50]"
      [showCurrentPageReport]="true"
      currentPageReportTemplate="{first} - {last} / {totalRecords} kayıt"
      [globalFilterFields]="['evrakNo', 'hedefBilgisi', 'tespitiTuru', 'mahkemeKodu']"
      responsiveLayout="scroll"
      styleClass="p-datatable-sm">
      
      <!-- Tablo Başlığı -->
      <ng-template pTemplate="caption">
        <div class="flex justify-between items-center">
          <span class="text-lg font-semibold">
            Toplam {{ filtrelenmissonuclar.length }} kayıt
          </span>
          <span class="text-sm text-gray-600">
            Son güncelleme: {{ getCurrentDateTime() }}
          </span>
        </div>
      </ng-template>

      <!-- Tablo Kolonları -->
      <ng-template pTemplate="header">
        <tr>
          <th pSortableColumn="evrakNo">
            Evrak No
            <p-sortIcon field="evrakNo"></p-sortIcon>
          </th>
          <th pSortableColumn="mahkemeKodu">
            Mahkeme
            <p-sortIcon field="mahkemeKodu"></p-sortIcon>
          </th>
          <th>Hedef Bilgisi</th>
          <th pSortableColumn="tespitiTuru">
            Tespit Türü
            <p-sortIcon field="tespitiTuru"></p-sortIcon>
          </th>
          <th pSortableColumn="durum" style="width: 10rem">
            Durum
            <p-sortIcon field="durum"></p-sortIcon>
          </th>
          <th style="width: 12rem">İlerleme</th>
          <th pSortableColumn="talepTarihi" style="width: 12rem">
            Talep Tarihi
            <p-sortIcon field="talepTarihi"></p-sortIcon>
          </th>
          <th style="width: 8rem">İşlemler</th>
        </tr>
      </ng-template>

      <!-- Tablo Satırları -->
      <ng-template pTemplate="body" let-kayit>
        <tr>
          <td>
            <span class="font-semibold text-blue-800">{{ kayit.evrakNo }}</span>
          </td>
          <td>
            <span class="text-sm text-gray-600">{{ kayit.mahkemeKodu }}</span>
          </td>
          <td>
            <span class="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
              {{ kayit.hedefBilgisi }}
            </span>
          </td>
          <td>
            <p-tag 
              [value]="tespitiTuruMetniGetir(kayit.tespitiTuru)" 
              [severity]="tespitiTuruRengiGetir(kayit.tespitiTuru)">
            </p-tag>
          </td>
          <td>
            <p-tag 
              [value]="durumMetniGetir(kayit.durum)" 
              [severity]="durumSeviyesiGetir(kayit.durum)">
            </p-tag>
          </td>
          <td>
            <div class="flex items-center">
              <p-progressBar 
                [value]="ilerlemeDurumunuGetir(kayit.durum)"
                [style]="{'width': '80px', 'height': '8px'}"
                class="mr-2">
              </p-progressBar>
              <span class="text-xs text-gray-600">
                {{ ilerlemeDurumunuGetir(kayit.durum) }}%
              </span>
            </div>
          </td>
          <td>
            <span class="text-sm text-gray-600">
              {{ tarihFormatiDuzelt(kayit.talepTarihi) }}
            </span>
          </td>
          <td>
            <p-button 
              icon="pi pi-eye" 
              severity="info"
              size="small"
              (onClick)="detayGoster(kayit)"
              pTooltip="Detay"
              tooltipPosition="top">
            </p-button>
          </td>
        </tr>
      </ng-template>

      <!-- Boş Durum -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="8" class="text-center py-8">
            <div class="flex flex-col items-center">
              <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
              <p class="text-gray-600">İletişim tespiti kaydı bulunamadı</p>
              <p class="text-sm text-gray-500">Arama kriterlerinizi değiştirmeyi deneyin</p>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </p-card>

  <!-- Toast Mesajları -->
  <p-toast></p-toast>
</div>

<!-- Detay Dialog -->
<p-dialog 
  header="İletişim Tespiti Detayları" 
  [(visible)]="detayDialogGoruntule"
  [modal]="true"
  [style]="{width: '800px'}"
  [closable]="true"
  (onHide)="detayDialogKapat()">
  
  <div *ngIf="seciliKayit" class="space-y-4">
    
    <!-- Genel Bilgiler -->
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
      <h4 class="text-lg font-semibold text-gray-800 mb-3">
        <i class="pi pi-info-circle mr-2"></i>
        Genel Bilgiler
      </h4>
      
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="text-sm font-medium text-gray-600">Evrak No:</label>
          <p class="text-sm font-semibold text-gray-800">{{ seciliKayit.evrakNo }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-600">Mahkeme Kodu:</label>
          <p class="text-sm font-semibold text-gray-800">{{ seciliKayit.mahkemeKodu }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-600">Hedef Bilgisi:</label>
          <p class="text-sm font-mono bg-white px-2 py-1 rounded border">{{ seciliKayit.hedefBilgisi }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-600">Tespit Türü:</label>
          <p class="text-sm">
            <p-tag 
              [value]="tespitiTuruMetniGetir(seciliKayit.tespitiTuru)" 
              [severity]="tespitiTuruRengiGetir(seciliKayit.tespitiTuru)">
            </p-tag>
          </p>
        </div>
      </div>
    </div>

    <!-- Durum ve İlerleme -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <h4 class="text-lg font-semibold text-blue-800 mb-3">
        <i class="pi pi-chart-line mr-2"></i>
        Durum ve İlerleme
      </h4>
      
      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-blue-700">Mevcut Durum:</span>
          <p-tag 
            [value]="durumMetniGetir(seciliKayit.durum)" 
            [severity]="durumSeviyesiGetir(seciliKayit.durum)">
          </p-tag>
        </div>
        
        <div>
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-blue-700">İlerleme:</span>
            <span class="text-sm text-blue-600">{{ ilerlemeDurumunuGetir(seciliKayit.durum) }}%</span>
          </div>
          <p-progressBar 
            [value]="ilerlemeDurumunuGetir(seciliKayit.durum)"
            [style]="{'height': '12px'}">
          </p-progressBar>
        </div>
      </div>
    </div>

    <!-- Tarih Bilgileri -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
      <h4 class="text-lg font-semibold text-green-800 mb-3">
        <i class="pi pi-calendar mr-2"></i>
        Tarih Bilgileri
      </h4>
      
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="text-sm font-medium text-green-600">Talep Tarihi:</label>
          <p class="text-sm font-semibold text-green-800">{{ tarihFormatiDuzelt(seciliKayit.talepTarihi) }}</p>
        </div>
        <div *ngIf="seciliKayit.tamamlanmaTarihi">
          <label class="text-sm font-medium text-green-600">Tamamlanma Tarihi:</label>
          <p class="text-sm font-semibold text-green-800">{{ tarihFormatiDuzelt(seciliKayit.tamamlanmaTarihi) }}</p>
        </div>
      </div>
    </div>

    <!-- Sonuç Bilgisi -->
    <div *ngIf="seciliKayit.sonucBilgisi" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <h4 class="text-lg font-semibold text-yellow-800 mb-3">
        <i class="pi pi-file-o mr-2"></i>
        Sonuç Bilgisi
      </h4>
      <p class="text-sm text-yellow-800 whitespace-pre-wrap">{{ seciliKayit.sonucBilgisi }}</p>
    </div>

    <!-- Hata Mesajı -->
    <div *ngIf="seciliKayit.hataMesaji" class="bg-red-50 border border-red-200 rounded-lg p-4">
      <h4 class="text-lg font-semibold text-red-800 mb-3">
        <i class="pi pi-exclamation-triangle mr-2"></i>
        Hata Mesajı
      </h4>
      <p class="text-sm text-red-800">{{ seciliKayit.hataMesaji }}</p>
    </div>
  </div>

  <ng-template pTemplate="footer">
    <p-button 
      label="Kapat" 
      icon="pi pi-times" 
      (onClick)="detayDialogKapat()"
      severity="secondary">
    </p-button>
  </ng-template>
</p-dialog>
