// Aidiyet Güncelle Component Styles

.p-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  
  .p-card-header {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
    font-weight: 600;
    border-radius: 8px 8px 0 0;
  }
}

// Form field styling
.p-inputtext,
.p-dropdown,
.p-calendar,
.p-inputtextarea,
.p-multiselect {
  border-radius: 6px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
  
  &:focus {
    border-color: #8b5cf6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  }
  
  &.ng-invalid.ng-dirty {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  }
}

// Dropdown specific styling
.p-dropdown {
  .p-dropdown-label {
    padding: 0.75rem;
  }
  
  &.p-focus {
    border-color: #8b5cf6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  }
  
  &:disabled {
    background-color: #f3f4f6;
    opacity: 0.7;
  }
}

// MultiSelect specific styling
.p-multiselect {
  .p-multiselect-label {
    padding: 0.75rem;
  }
  
  &.p-focus {
    border-color: #8b5cf6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  }
  
  .p-multiselect-token {
    background: #8b5cf6;
    color: white;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
  }
}

// Calendar specific styling
.p-calendar {
  .p-inputtext {
    border: none;
    box-shadow: none;
  }
  
  &.p-focus {
    border-color: #8b5cf6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  }
}

// Checkbox styling
.p-checkbox {
  .p-checkbox-box {
    border-radius: 4px;
    border: 2px solid #d1d5db;
    transition: all 0.2s ease;
    
    &.p-highlight {
      background: #8b5cf6;
      border-color: #8b5cf6;
    }
  }
}

// File upload styling
.p-fileupload {
  .p-fileupload-choose {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    border: none;
    border-radius: 6px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
    }
  }
}

// Button styling
.p-button {
  border-radius: 6px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  
  &.p-button-secondary {
    background: #6b7280;
    border-color: #6b7280;
    
    &:hover {
      background: #4b5563;
      border-color: #4b5563;
    }
  }
  
  &.p-button-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    
    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }
  }
  
  &.p-button-info {
    background: #3b82f6;
    border-color: #3b82f6;
    
    &:hover:not(:disabled) {
      background: #2563eb;
      border-color: #2563eb;
    }
  }
  
  &.p-button-danger {
    background: #ef4444;
    border-color: #ef4444;
    
    &:hover:not(:disabled) {
      background: #dc2626;
      border-color: #dc2626;
    }
  }
  
  &:not(.p-button-secondary):not(.p-button-success):not(.p-button-info):not(.p-button-danger) {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    border: none;
    
    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
    }
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &.p-button-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

// Error message styling
small.text-red-500 {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: block;
}

// Required field indicator
.text-red-500 {
  color: #ef4444;
}

// Grid responsive adjustments
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }
  
  .md\:col-span-2 {
    grid-column: span 1;
  }
  
  .lg\:grid-cols-3 {
    grid-template-columns: 1fr;
  }
}

// Loading state
.p-progressspinner {
  .p-progressspinner-circle {
    stroke: #8b5cf6;
  }
}

// Toast positioning
:host ::ng-deep .p-toast {
  z-index: 9999;
}

// Card hover effects
.p-card {
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

// Form section spacing
.mb-4 {
  margin-bottom: 1rem;
}

// File upload area styling
.border-dashed {
  border-style: dashed;
  border-width: 2px;
  border-color: #d1d5db;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #8b5cf6;
    background-color: #f5f3ff;
  }
}

// Selected file display
.bg-blue-50 {
  background-color: #eff6ff;
}

.border-blue-200 {
  border-color: #bfdbfe;
}

.text-blue-500 {
  color: #3b82f6;
}

.text-blue-600 {
  color: #2563eb;
}

.text-blue-800 {
  color: #1e40af;
}

// Form validation states
.ng-invalid.ng-dirty {
  border-color: #ef4444 !important;
}

.ng-valid.ng-dirty {
  border-color: #8b5cf6 !important;
}

// Responsive text sizing
@media (max-width: 640px) {
  h2 {
    font-size: 1.5rem;
  }
  
  .text-2xl {
    font-size: 1.5rem;
  }
}

// Focus states for accessibility
.p-inputtext:focus,
.p-dropdown:focus,
.p-calendar:focus,
.p-inputtextarea:focus,
.p-multiselect:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

// Animation for form submission
.yukleniyor {
  .p-button {
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

// Purple color variations for update theme
.text-purple-500 {
  color: #8b5cf6;
}

.text-purple-600 {
  color: #7c3aed;
}

.text-purple-800 {
  color: #5b21b6;
}

.bg-purple-50 {
  background-color: #f5f3ff;
}

.border-purple-200 {
  border-color: #e9d5ff;
}

// Special styling for update-related elements
.aidiyet-highlight {
  background: linear-gradient(135deg, #f5f3ff 0%, #e9d5ff 100%);
  border: 1px solid #8b5cf6;
  border-radius: 6px;
  padding: 0.5rem;
}

// Pencil icon styling for update theme
.pi-pencil {
  color: #8b5cf6;
}

// Update form specific styling
.update-section {
  background: #faf5ff;
  border: 1px solid #e9d5ff;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  
  .section-title {
    color: #5b21b6;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
}

// Comparison styling for old vs new values
.comparison-container {
  display: flex;
  gap: 1rem;
  margin: 1rem 0;
  
  .old-value {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 6px;
    padding: 0.75rem;
    flex: 1;
    
    .label {
      font-weight: 600;
      color: #991b1b;
      margin-bottom: 0.25rem;
    }
    
    .value {
      color: #7f1d1d;
    }
  }
  
  .new-value {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    border-radius: 6px;
    padding: 0.75rem;
    flex: 1;
    
    .label {
      font-weight: 600;
      color: #166534;
      margin-bottom: 0.25rem;
    }
    
    .value {
      color: #14532d;
    }
  }
}

// Arrow icon for comparison
.comparison-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8b5cf6;
  font-size: 1.5rem;
  margin: 0 0.5rem;
}
