<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <title>BTK Demo</title>
        <base href="/" />
        <meta name="viewport" content="width=device-width, initial-scale=1.o" />
        <link rel="preconnect" href="https://primefaces.org" />
        <link rel="icon" type="image/x-icon" href="https://primefaces.org/cdn/primeng/images/favicon.png" />
        <link href="https://fonts.cdnfonts.com/css/lato" rel="stylesheet" />
    </head>

    <body>
        <!-- Environment Configuration Script -->
        <script>
            // Bu script Docker/Kubernetes deployment sırasında environment variable'lar ile doldurulabilir
            window.APP_CONFIG = {
                API_URL: '${API_URL}' // Docker deployment sırasında replace edilecek
            };

            // Alternatif olarak ENV object'i de kullanılabilir
            window.ENV = {
                API_URL: '${ENV_API_URL}' // Nginx vb. tarafından inject edilebilir
            };
        </script>
        <app-root></app-root>
    </body>
</html>
