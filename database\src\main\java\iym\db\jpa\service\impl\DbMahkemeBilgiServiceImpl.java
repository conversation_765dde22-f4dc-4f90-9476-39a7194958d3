package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.db.jpa.dao.MahkemeBilgisiRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Service implementation for MahkemeBilgi entity
 */
@Service
public class DbMahkemeBilgiServiceImpl extends GenericDbServiceImpl<MahkemeBilgi, Long> implements DbMahkemeBilgiService {

    private final MahkemeBilgisiRepo mahkemeBilgisiRepo;

    @Autowired
    public DbMahkemeBilgiServiceImpl(MahkemeBilgisiRepo repository) {
        super(repository);
        this.mahkemeBilgisiRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeBilgi> findByMahkemeKodu(String mahkemeKodu){
        return mahkemeBilgisiRepo.findByMahkemeKodu(mahkemeKodu);
    }


}
