package gov.tib.iym.mahkemekarar.xmlparse;


import gov.tib.iym.mahkemekarar.base.model.MAHKEME_KARAR_ISLEM_TURU;
import gov.tib.iym.mahkemekarar.base.model.MAHKEME_KARAR_TIPLERI;
import gov.tib.iym.mahkemekarar.renderer.DirectoryTreeNode;
import gov.tib.iym.mahkemekarar.renderer.DirectoryTreeNodeCollection;
import gov.tib.iym.mahkemekarar.renderer.XmlTagData;
import gov.tib.iym.mahkemekarar.webservice.XmlSonuc;

import java.util.ArrayList;
import java.util.List;

import org.zkoss.zul.DefaultTreeModel;
import org.zkoss.zul.DefaultTreeNode;
import org.zkoss.zul.TreeModel;
import org.zkoss.zul.TreeNode;

public class EVRAK_KAYIT extends XMLEVRAKBASE {

	public Long evrakId;
	public Long mukerrerNo;
	public String XML_VERSIYON;
	public String EVRAK_NO;
	public String EVRAK_TARIHI;
	public String EVRAK_GELDIGI_KURUM;
	public String EVRAK_TIPI;
	public String HAVALE_BIRIM;
	public String ACIKLAMA;
	public String EVRAK_KONUSU;
	public String GEL_IL;
	public String ACILMI;
	public List<MAHKEME_KARAR> MAHKEME_KARAR;
	
	public  DirectoryTreeNode<XmlTagData> root;
	


	
	// aşağıda görünen 1 ifadeleri o elementin zorunlu olduğunu gösteriyor

	public EVRAK_KAYIT(Long islemId,Long kullaniciId,String clientIp) {
		super(islemId,kullaniciId,clientIp);
		MAHKEME_KARAR = new ArrayList<MAHKEME_KARAR>();
		basitAlanlar.put("XML_VERSIYON", null);
		basitAlanlar.put("EVRAK_NO", "1");
		basitAlanlar.put("EVRAK_TARIHI", "1");
		basitAlanlar.put("EVRAK_GELDIGI_KURUM", "1");
		basitAlanlar.put("EVRAK_TIPI", "1");
		basitAlanlar.put("HAVALE_BIRIM", "1");
		basitAlanlar.put("ACIKLAMA", null);
		basitAlanlar.put("EVRAK_KONUSU", null);
		basitAlanlar.put("GEL_IL", "1");
		basitAlanlar.put("ACILMI", null);
		listeAlanlar.put("MAHKEME_KARAR", MAHKEME_KARAR);
	}

	@Override
	protected XMLEVRAKBASE addEntity(String entityName) {
		XMLEVRAKBASE entity = null;
		if (entityName == "MAHKEME_KARAR") {
			entity = new MAHKEME_KARAR(islemId,kullaniciId,clientIp);
			
			MAHKEME_KARAR.add((MAHKEME_KARAR) entity);
		}
		return entity;
	}

	@Override
	public ArrayList<XMLHATA> dogrula(MAHKEME_KARAR_ISLEM_TURU mahkemeKararTuru) {
		this.hatalar2.addAll(super.dogrula(null));
		if(this.XML_VERSIYON == null || this.XML_VERSIYON.isEmpty()){
			if (!(EVRAK_TIPI.equals("402")||EVRAK_TIPI.equals("401"))) {
				hatalar2.add(new XMLHATA("EVRAK_TIPI","EVRAK_TIPI 402 OLARAK GİRİLMELİ !",kullaniciId,clientIp,islemId));
			}
		}else if(this.XML_VERSIYON.equalsIgnoreCase("1")){
			if(this.EVRAK_TIPI.equalsIgnoreCase(EVRAK_TIPLERI.ILETISIMIN_TESPITI.getTip())){
				if(this.EVRAK_KONUSU == null || this.EVRAK_KONUSU.equalsIgnoreCase("")){
					hatalar2.add(new XMLHATA("EVRAK_KONUSU","EVRAK KONUSU İLETİŞİMİN TESPİTİ GİRİLMELİDİR!",kullaniciId,clientIp,islemId));
				}
			}else if(this.EVRAK_TIPI.equalsIgnoreCase(EVRAK_TIPLERI.ILETISIMIN_DENETLENMESI.getTip())){
				
			}else if(this.EVRAK_TIPI.equalsIgnoreCase(EVRAK_TIPLERI.GENEL_EVRAK.getTip())){
				if(this.EVRAK_KONUSU == null || this.EVRAK_KONUSU.equalsIgnoreCase("")){
					hatalar2.add(new XMLHATA("EVRAK_KONUSU","EVRAK KONUSU GİRİLMELİDİR!",kullaniciId,clientIp,islemId));
				}
			}else{
				hatalar2.add(new XMLHATA("EVRAK_TIPI","EVRAK_TIPI <ILETISIMIN_TESPITI>,<ILETISIMIN_DENETLENMESI>,<GENEL_EVRAK> ŞEKLİNDE GİRİLMELİDİR!",kullaniciId,clientIp,islemId));
			}
		}
		if (!Utility.isNullOrEmpty(EVRAK_NO)) {
			String[] evrakNo = EVRAK_NO.split(" ");
			if (evrakNo.length > 1) {
				hatalar2.add(new XMLHATA("EVRAK_NO","EVRAK NO ALANINDA BOŞLUK KARAKTERİ KULLANILAMAZ!",kullaniciId,clientIp,islemId));
			}
		}
		
		for (MAHKEME_KARAR mahkemeKarar : MAHKEME_KARAR) {
			this.hatalar2.addAll(mahkemeKarar.dogrula(null));
		}
		
		return hatalar2;
	}
	
	public String evrakTipiBelirle(String evrakKurum){
		String evrakTipi = "";

		if( !(this.XML_VERSIYON == null || this.XML_VERSIYON.equalsIgnoreCase("")) ){
			if(this.EVRAK_TIPI.equalsIgnoreCase(EVRAK_TIPLERI.ILETISIMIN_TESPITI.getTip())){
				if(evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMKOM.getKurumKodu())
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMTEM.getKurumKodu())
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMBSM.getKurumKodu())
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMIDB.getKurumKodu())
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMUMD.getKurumKodu())){
					evrakTipi = "401.04.01";
				}else if(evrakKurum.equals(EVRAK_GELEN_KURUMLAR.JANDARMA.getKurumKodu())){
					evrakTipi = "401.05.01";
				}else if(evrakKurum.equals(EVRAK_GELEN_KURUMLAR.MIT.getKurumKodu())){
					evrakTipi = "401.03.01";
				}else {
					evrakTipi = "";
				}
			}else if(this.EVRAK_TIPI.equalsIgnoreCase(EVRAK_TIPLERI.ILETISIMIN_DENETLENMESI.getTip())){
				String kararTip = this.MAHKEME_KARAR.get(0).KARAR_TIP;
				if(kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_HAKIM_KARARI.getKararKodu()) 
						|| kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_SONLANDIRMA.getKararKodu()) 
						|| kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_KHK_SONLANDIRMA.getKararKodu())
						|| kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_SAVCILIK_SONLANDIRMA.getKararKodu())
						|| kararTip.equals(MAHKEME_KARAR_TIPLERI.HEDEF_AD_SOYAD_DEGISTIRME.getKararKodu())
						|| kararTip.equals(MAHKEME_KARAR_TIPLERI.MAHKEME_AIDIYAT_DEGISTIRME.getKararKodu())
						|| kararTip.equals(MAHKEME_KARAR_TIPLERI.MAHKEME_KODU_DEGISTIRME.getKararKodu())){
					evrakTipi = "402.01.01";
				}else if(kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_ASKERI_HAKIM_KARARI.getKararKodu()) || kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_ASKERI_SONLANDIRMA.getKararKodu())){
					evrakTipi = "402.05.03";
				}else if(kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_YAZILI_EMIR.getKararKodu()) ||
						kararTip.equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_YAZILI_EMIR.getKararKodu()) ||
						kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_KHK_YAZILI_EMIR.getKararKodu())){
					evrakTipi = "402.02.01";
				}else if(kararTip.equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_HAKIM_KARARI.getKararKodu()) || kararTip.equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_SONLANDIRMA.getKararKodu())||
						kararTip.equals(MAHKEME_KARAR_TIPLERI.ABONE_KUTUK_BILGILERI_KARARI.getKararKodu())|| kararTip.equals(MAHKEME_KARAR_TIPLERI.SINYAL_BILGI_DEGERLENDIRME_KARARI.getKararKodu())){
					if(evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMIDB.getKurumKodu())
					  ||evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMSBR.getKurumKodu())
					 ){
						evrakTipi = "402.04.01";
					}else if(evrakKurum.equals(EVRAK_GELEN_KURUMLAR.MIT.getKurumKodu())){
						evrakTipi = "402.03.01";
					}else if(evrakKurum.equals(EVRAK_GELEN_KURUMLAR.JANDARMA.getKurumKodu())){
						evrakTipi = "402.05.01";
					}
				}else
					evrakTipi="";
			}else if(this.EVRAK_TIPI.equalsIgnoreCase(EVRAK_TIPLERI.GENEL_EVRAK.getTip())){
				if(evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMKOM.getKurumKodu())
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMTEM.getKurumKodu())
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMBSM.getKurumKodu())
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMIDB.getKurumKodu())
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMUMD.getKurumKodu())
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMSBR.getKurumKodu())
						){
					evrakTipi = "000.01.01";
				}else if(evrakKurum.equals(EVRAK_GELEN_KURUMLAR.JANDARMA.getKurumKodu())){
					evrakTipi = "000.01.01";
				}else if(evrakKurum.equals(EVRAK_GELEN_KURUMLAR.MIT.getKurumKodu())){
					evrakTipi = "000.01.01";
				}else {
					evrakTipi = "";
				}
			}
		}else{
			String kararTip = this.MAHKEME_KARAR.get(0).KARAR_TIP;
			if(kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_HAKIM_KARARI.getKararKodu()) 
					|| kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_SONLANDIRMA.getKararKodu()) 
					|| kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_SAVCILIK_SONLANDIRMA.getKararKodu())
					|| kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_KHK_SONLANDIRMA.getKararKodu())
					|| kararTip.equals(MAHKEME_KARAR_TIPLERI.HEDEF_AD_SOYAD_DEGISTIRME.getKararKodu())
					|| kararTip.equals(MAHKEME_KARAR_TIPLERI.MAHKEME_AIDIYAT_DEGISTIRME.getKararKodu())
					|| kararTip.equals(MAHKEME_KARAR_TIPLERI.MAHKEME_KODU_DEGISTIRME.getKararKodu())
			){
				evrakTipi = "402.01.01";
			}else if(kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_ASKERI_HAKIM_KARARI.getKararKodu()) || kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_ASKERI_SONLANDIRMA.getKararKodu())){
				evrakTipi = "402.05.03";
			}else if(kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_YAZILI_EMIR.getKararKodu()) || kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_KHK_YAZILI_EMIR.getKararKodu()) || kararTip.equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_YAZILI_EMIR.getKararKodu())){
				evrakTipi = "402.02.01";
			}else if(kararTip.equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_HAKIM_KARARI.getKararKodu()) || kararTip.equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_SONLANDIRMA.getKararKodu())||
					kararTip.equals(MAHKEME_KARAR_TIPLERI.ABONE_KUTUK_BILGILERI_KARARI.getKararKodu())|| kararTip.equals(MAHKEME_KARAR_TIPLERI.SINYAL_BILGI_DEGERLENDIRME_KARARI.getKararKodu())){
				if(evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMIDB.getKurumKodu())
					||evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMSBR.getKurumKodu())
					){
					evrakTipi = "402.04.01";
				}else if(evrakKurum.equals(EVRAK_GELEN_KURUMLAR.MIT.getKurumKodu())){
					evrakTipi = "402.03.01";
				}else if(evrakKurum.equals(EVRAK_GELEN_KURUMLAR.JANDARMA.getKurumKodu())){
					evrakTipi = "402.05.01";
				}
			}else
				evrakTipi="";
		}
		return evrakTipi;
	}
	
	public void agacYapisiOlustur() throws Exception{
		this.root = new DirectoryTreeNode<XmlTagData>(null,agacinAnaDaliniOlustur(),true);
		
	}
	
	public DirectoryTreeNodeCollection<XmlTagData> agacinDallariniOlustur() throws Exception{
		DirectoryTreeNodeCollection<XmlTagData> agacDallari = new DirectoryTreeNodeCollection<XmlTagData>();
		agacDallari.add(new DefaultTreeNode<XmlTagData>(new XmlTagData("XML_VERSIYON", this.XML_VERSIYON , hataliTagBul("XML_VERSIYON"))));
		agacDallari.add(new DefaultTreeNode<XmlTagData>(new XmlTagData("EVRAK_NO", this.EVRAK_NO , hataliTagBul("EVRAK_NO"))));
		agacDallari.add(new DefaultTreeNode<XmlTagData>(new XmlTagData("EVRAK_TARIHI", this.EVRAK_TARIHI , hataliTagBul("EVRAK_TARIHI"))));
		agacDallari.add(new DefaultTreeNode<XmlTagData>(new XmlTagData("EVRAK_GELDIGI_KURUM", this.EVRAK_GELDIGI_KURUM , hataliTagBul("EVRAK_GELDIGI_KURUM"))));
		agacDallari.add(new DefaultTreeNode<XmlTagData>(new XmlTagData("EVRAK_TIPI", this.EVRAK_TIPI ,  hataliTagBul("EVRAK_TIPI"))));
		agacDallari.add(new DefaultTreeNode<XmlTagData>(new XmlTagData("HAVALE_BIRIM", this.HAVALE_BIRIM , hataliTagBul("HAVALE_BIRIM"))));
		agacDallari.add(new DefaultTreeNode<XmlTagData>(new XmlTagData("ACIKLAMA", this.ACIKLAMA , hataliTagBul("ACIKLAMA"))));
		agacDallari.add(new DefaultTreeNode<XmlTagData>(new XmlTagData("EVRAK_KONUSU", this.EVRAK_KONUSU , hataliTagBul("EVRAK_KONUSU"))));
		agacDallari.add(new DefaultTreeNode<XmlTagData>(new XmlTagData("GEL_IL", this.GEL_IL , hataliTagBul("GEL_IL"))));
		agacDallari.add(new DefaultTreeNode<XmlTagData>(new XmlTagData("ACILMI", this.ACILMI , hataliTagBul("ACILMI"))));
		for(MAHKEME_KARAR k : this.MAHKEME_KARAR)
			agacDallari.add(new DirectoryTreeNode<XmlTagData>(null,k.agacinAnaDaliniOlustur(),true));
		return agacDallari;
	}
	
	public DirectoryTreeNodeCollection<XmlTagData> agacinAnaDaliniOlustur() throws Exception{
		DirectoryTreeNodeCollection<XmlTagData> anaDal = new DirectoryTreeNodeCollection<XmlTagData>();
		anaDal.add(new DirectoryTreeNode<XmlTagData>(new XmlTagData("EVRAK_KAYIT", "",hataliTagBul("EVRAK_KAYIT")),agacinDallariniOlustur()));
		return anaDal;
		
	}
	
	public TreeModel<TreeNode<XmlTagData>> getTreeModel() {
		return new DefaultTreeModel<XmlTagData>(this.root);
	}

	public boolean hataVarMi() {
		if(this.hatalar2.size() != 0){
			return true;
		}else{
			for(MAHKEME_KARAR k: this.MAHKEME_KARAR){
				if(k.hatalar2.size() != 0){
					return true;
				
				}else{
					for(MAHKEME_KARAR_DETAY dty : k.MAHKEME_KARAR_DETAY){
						if(dty.hatalar2.size() != 0){
							return true;
						}else{
							if(dty.MAHKEME_AIDIYAT_DETAY.hatalar2.size() != 0){
									return true;
							}
							for(HEDEFLER_DETAY hdty : dty.HEDEFLER_DETAY){
								if(hdty.hatalar2.size() != 0){
									return true;
								}
							}
						}
					}
					for(HEDEFLER h : k.HEDEFLER){
						if(h.hatalar2.size() != 0){
							return true;
						}else{
							for(MAHKEME_KARAR_DETAY dty : h.MAHKEME_KARAR_DETAY){
								if(dty.hatalar2.size() != 0){
									return true;
								}
							}
							
						}
					}
					if(k.MAHKEME_AIDIYAT !=null && k.MAHKEME_AIDIYAT.AIDIYAT_KOD.size() !=0 && k.MAHKEME_AIDIYAT.hatalar2.size() != 0){
						return true;
					}
					if(k.MAHKEME_SUC_TIPI !=null && k.MAHKEME_SUC_TIPI.SUC_TIP_KOD.size() !=0  && k.MAHKEME_SUC_TIPI.hatalar2.size() != 0){
						return true;
					}
				}
				
			}
		}
		return false;
	}

	public List<XmlSonuc> sonucGoster() {
		ArrayList<XmlSonuc> xmlSonuc = new ArrayList<XmlSonuc>(); 
		for(MAHKEME_KARAR k: this.MAHKEME_KARAR){
			if(k.hatalar2.size() != 0){
				xmlSonuc.addAll(xmlHataBul(k.hatalar2));
			
			}else{
				for(MAHKEME_KARAR_DETAY dty : k.MAHKEME_KARAR_DETAY){
					if(dty.hatalar2.size() != 0){
						xmlSonuc.addAll(xmlHataBul(dty.hatalar2));
					}else{
						if(dty.MAHKEME_AIDIYAT_DETAY.hatalar2.size() != 0){
							xmlSonuc.addAll(xmlHataBul(dty.MAHKEME_AIDIYAT_DETAY.hatalar2));
						}
						for(HEDEFLER_DETAY hdty : dty.HEDEFLER_DETAY){
							if(hdty.hatalar2.size() != 0){
								xmlSonuc.addAll(xmlHataBul(hdty.hatalar2));
							}
						}
					}
				}
				for(HEDEFLER h : k.HEDEFLER){
					if(h.hatalar2.size() != 0){
						xmlSonuc.addAll(xmlHataBul(h.hatalar2));
					}else{
						for(MAHKEME_KARAR_DETAY dty : h.MAHKEME_KARAR_DETAY){
							if(dty.hatalar2.size() != 0){
								xmlSonuc.addAll(xmlHataBul(dty.hatalar2));
							}
						}
						
					}
				}
				if(k.MAHKEME_AIDIYAT !=null && k.MAHKEME_AIDIYAT.AIDIYAT_KOD.size() !=0 && k.MAHKEME_AIDIYAT.hatalar2.size() != 0){
					xmlSonuc.addAll(xmlHataBul(k.MAHKEME_AIDIYAT.hatalar2));
				}
				if(k.MAHKEME_SUC_TIPI !=null && k.MAHKEME_SUC_TIPI.SUC_TIP_KOD.size() !=0  && k.MAHKEME_SUC_TIPI.hatalar2.size() != 0){
					xmlSonuc.addAll(xmlHataBul(k.MAHKEME_SUC_TIPI.hatalar2));
				}
			}
			
		}
		if(xmlSonuc.size() == 0)
			xmlSonuc.addAll(xmlHataBul(this.hatalar2));
		
		return xmlSonuc;
	}
	private ArrayList<XmlSonuc> xmlHataBul(ArrayList<XMLHATA> hatalar2) {
		ArrayList<XmlSonuc> sonuc = new ArrayList<XmlSonuc>();
		for(XMLHATA hata : hatalar2){
			if(hata.getXmlTag()!=null && !hata.getXmlTag().isEmpty())
				sonuc.add(new XmlSonuc(hata.getXmlTag(),hata.getHata()));
			else 
				break;
		}
		return sonuc;
	}

	public Long getEvrakId() {
		return evrakId;
	}

	public void setEvrakId(Long evrakId) {
		this.evrakId = evrakId;
	}

	public Long getMukerrerNo() {
		return mukerrerNo;
	}

	public void setMukerrerNo(Long mukerrerNo) {
		this.mukerrerNo = mukerrerNo;
	}	
}