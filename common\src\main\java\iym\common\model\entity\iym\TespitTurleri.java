package iym.common.model.entity.iym;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * Entity class for EVRAK_KAYIT table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "TespitTuru")
@Table(name = "TESPIT_TURLERI")
public class TespitTurleri implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "TESPIT_TURU")
    private Long tespitTuru;

    @Column(name = "TESPIT_ACIKLAMA")
    private String aciklama;


}
