package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.HedeflerAidiyat;
import iym.common.service.db.mk.DbHedeflerAidiyatService;
import iym.db.jpa.dao.mk.HedeflerAidiyatRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service implementation for HedeflerAidiyat entity
 */
@Service
public class DbHedeflerAidiyatServiceImpl extends GenericDbServiceImpl<HedeflerAidiyat, Long> implements DbHedeflerAidiyatService {

    private final HedeflerAidiyatRepo hedeflerAidiyatRepo;

    @Autowired
    public DbHedeflerAidiyatServiceImpl(HedeflerAidiyatRepo repository) {
        super(repository);
        this.hedeflerAidiyatRepo = repository;
    }


}
