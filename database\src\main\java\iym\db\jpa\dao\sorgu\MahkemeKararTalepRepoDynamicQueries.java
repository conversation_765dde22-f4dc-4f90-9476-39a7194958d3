package iym.db.jpa.dao.sorgu;

import iym.common.enums.MakosUserAuditType;
import iym.common.model.entity.iym.sorgu.MahkemeKararTalepSorguInfo;
import iym.common.model.entity.iym.sorgu.MahkemeKararTalepSorguParam;
import java.util.List;

public interface MahkemeKararTalepRepoDynamicQueries {

    List<MahkemeKararTalepSorguInfo> islenecekMahkemeKararTalepListesi(String kurumKodu);



    List<MahkemeKararTalepSorguInfo> mahkemeKararTalepSorgu(String kurumKodu, MahkemeKararTalepSorguParam sorguParam);

    /**
     * TODO
     * KEEP THIS TEST METHOD UNTIL any "...DynamicQueris.java" class has a DB WRITE(CREATE/UPDATE) method
     * Custom insert method for testing transaction behavior
     * Inserts a log entry for MahkemeKararTalep operations
     *
     * @return The ID of the inserted log entry
     */
    Long testInsertUserAuditLog(MakosUserAuditType userAuditType);
}