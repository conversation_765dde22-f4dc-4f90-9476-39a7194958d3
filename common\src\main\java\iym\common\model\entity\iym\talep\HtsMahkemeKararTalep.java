package iym.common.model.entity.iym.talep;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Entity class for HTS_MAHKEME_KARAR_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "HtsMahkemeKararTalep")
@Table(name = "HTS_MAHKEME_KARAR_TALEP")
public class HtsMahkemeKararTalep implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "HTS_MAHKEME_KARAR_TALEP_SEQ")
    @SequenceGenerator(name = "HTS_MAHKEME_KARAR_TALEP_SEQ", sequenceName = "HTS_MAHKEME_KARAR_TALEP_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "EVRAK_ID", nullable = false)
    @NotNull
    private Long evrakId;

    @Column(name = "KULLANICI_ID", nullable = false)
    @NotNull
    private Long kullaniciId;

    @Column(name = "KAYIT_TARIHI", nullable = false)
    @NotNull
    @Temporal(TemporalType.TIMESTAMP)
    private Date kayitTarihi;

    @Column(name = "DURUM", length = 100)
    @Size(max = 100)
    private String durum;

    @Column(name = "KARAR_TIP", nullable = false, length = 100)
    @NotNull
    @Size(max = 100)
    private String kararTip;

    @Column(name = "HUKUK_BIRIM", nullable = false, length = 100)
    @NotNull
    @Size(max = 100)
    private String hukukBirim;

    @Column(name = "MAHKEME_ILI", nullable = false, length = 100)
    @NotNull
    @Size(max = 100)
    private String mahkemeIli;

    @Column(name = "MAHKEME_KODU", nullable = false, length = 100)
    @NotNull
    @Size(max = 100)
    private String mahkemeKodu;

    @Column(name = "MAHKEME_ADI", nullable = false, length = 1000)
    @NotNull
    @Size(max = 1000)
    private String mahkemeAdi;

    @Column(name = "ACIKLAMA", length = 1000)
    @Size(max = 1000)
    private String aciklama;

    @Column(name = "MAHKEME_KARAR_NO", length = 100)
    @Size(max = 100)
    private String mahkemeKararNo;

    @Column(name = "SORUSTURMA_NO", length = 100)
    @Size(max = 100)
    private String sorusturmaNo;
}
