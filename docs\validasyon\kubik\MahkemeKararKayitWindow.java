package gov.tib.iym.mahkemekarar.view;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import gov.tib.iym.events.KismiIadeBilgileriEvent;
import gov.tib.iym.events.KismiIadeEventEvent;
import gov.tib.iym.hedefYonetim.model.*;
import gov.tib.iym.mahkemekarar.renderer.*;
import gov.tib.iym.model.EvrakKayitPojo;
import gov.tib.iym.model.EvrakTipiPojo;
import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.zkoss.util.media.AMedia;
import org.zkoss.zhtml.Br;
import org.zkoss.zhtml.Messagebox;
import org.zkoss.zk.ui.Executions;
import org.zkoss.zk.ui.Sessions;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zk.ui.event.EventListener;
import org.zkoss.zkex.zul.Columnchildren;
import org.zkoss.zkmax.zul.Tablechildren;
import org.zkoss.zkmax.zul.Tablelayout;
import org.zkoss.zul.*;

import com.lowagie.text.DocumentException;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.PdfWriter;
import com.lowagie.text.pdf.RandomAccessFileOrArray;
import com.lowagie.text.pdf.codec.TiffImage;

import gov.tib.fide.iym.AboneBilgi;
import gov.tib.fide.iym.IYMFideEndpoint;
import gov.tib.iym.IYMSabitler;
import gov.tib.iym.IymIslemler;
import gov.tib.iym.mahkemekarar.base.model.HEDEF_TIPLERI;
import gov.tib.iym.mahkemekarar.base.model.MAHKEME_KARAR_ISLEM_TURU;
import gov.tib.iym.mahkemekarar.base.model.MahkemeSucTipleriPojo;
import gov.tib.iym.mahkemekarar.model.GenelKararPojo;
import gov.tib.iym.mahkemekarar.model.HatalarPojo;
import gov.tib.iym.mahkemekarar.model.HedeflerDetayIslemPojo;
import gov.tib.iym.mahkemekarar.model.HedeflerIslemPojo;
import gov.tib.iym.mahkemekarar.model.ItirazEvrakPojo;
import gov.tib.iym.mahkemekarar.model.MahkemeAidiyatDetayIslemPojo;
import gov.tib.iym.mahkemekarar.model.MahkemeKararIslemPojo;
import gov.tib.iym.mahkemekarar.model.MahkemeKoduDegistirPojo;
import gov.tib.iym.model.IymFilePojo;
import gov.tib.iym.model.MahkemeKararAtamaPojo;
import gov.tib.iym.service.IymServiceMahkemeKarar;
import gov.tib.iym.service.Iymservice;
import gov.tib.kubik.view.zk.component.TibWindow;
import gov.tib.personel.model.LoginInfo;
import gov.tib.personel.service.PersonelService;
import gov.tib.util.Hatalar;
import gov.tib.util.PropertyHelper;
import gov.tib.util.TemelIslemler;
import service.LoggerService;
import service.ServiceManager;

public class MahkemeKararKayitWindow extends TibWindow {
    private static final Logger logger = LoggerFactory.getLogger(MahkemeKararKayitWindow.class);

    private LoginInfo personel = (LoginInfo) Sessions.getCurrent()
            .getAttribute("personel");

    private final static String aboneEndPointURLKey = "gov.tib.fide.abone.ws.AboneEndPoint.URL";

    Long evrakId;

    private Tree islenecekEvraklarTree;
    private Groupbox buttonGroupBox;
    private Groupbox kararGroupbox;
    private Label evrakOnaylayanLbl;
    private Label evrakAciklamaLbl;
    private Label evrakAciklamaLbl2;
    private Label kurumAdiLbl;
    private Label evrakTarihiLbl;
    private Label evrakIliLbl;
    private Label evrakSiraNoLbl;
    private Label evrakNoLbl;
    private Label kararTipLbl;
    private Label mahkemeIliLbl;
    private Label kararNoLbl;
    private Label sorusturmaNoLbl;
    private Label mahkemeAdiLbl;
    private Label baslangicTarihiLbl;
    private Label bitisTarihiLbl;
    private Label toplamHedefSayiLbl;
    private Label toplamAidiyatSayiLbl;
    private Grid genelKararList;
    private Grid mahkemeKoduDegistirList;
    private Grid hedefAdSoyadDegistirList;
    private Grid aidiyatEkleCikarList;
    private Grid hedeflerList;
    private Grid aidiyatList;
    private Iframe dosyaGorFrame;
    private XmlEvrakPojo xmlEvrak;
    private MahkemeKararIslemPojo mahkemeKararIslem;
    private MahkemeKararPojo mahkemeKarar;
    private Combobox sucTipleriCombobox;
    private Hlayout sucTipiLayout;
    private Div sucTipleriDiv;
    private Button sucTipiEkleButton;
    private Label sucTipiLbl;
    private Label itirazNedeniLbl;
    private Label itirazTalepLbl;
    private Label itirazIsleyenTarLbl;
    private Label itirazIsleyenLbl;
    private Label itirazYaziLbl;
    private Layout itirazLayout;
    private Button onaylaButton;
    private Button tanimlaButton;
    private Button temizleButton;
    private Button iadeButton;
    private Button itirazEtButton;
    private Div uyariMesajiDiv;
    private Tab hedefTab;
    private Tab aidiyatTab;
    private Tab aidiyatEkleCikarTab;
    private Tab hedefAdSoyadDegistirTab;
    private Tab mahkemeKoduDegistirTab;
    private Tab genelKararTab;

    private Tabbox hedefAidiyatTablar;

    private Tabpanel hedefTabPanel;
    private Tabpanel aidiyatTabPanel;
    private Tabpanel aidiyatEkleCikarTabPanel;
    private Tabpanel hedefAdSoyadDegistirTabPanel;
    private Tabpanel mahkemeKoduDegistirTabPanel;
    private Tabpanel genelKararTabPanel;
    private Columnchildren mahkemeKararChildren;
    private Columnchildren dosyaGorChildren;
    private Textbox aciklamaTxt;
    private MahkemeKararAnaSayfaWindow mahkemeKararAnaSayfa;

    boolean nobetOnayliEvrak = false;

    private Button nobetciTanimliKontrolEdildiBtn;
    private Toolbarbutton hedefIadeEtBtn;

    IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
    Iymservice iymsrv = ServiceManager.getIymservice();

    private boolean detayMi = false;

    private Row r = null;
    private Treerow tr = null;
    KismiIadeBilgileriEvent kısmiIadeBilgileriEvent;
    KismiIadeEventEvent kısmiIadeEventEvent;

    private Button oncekiKismiIadeBilgiButton;
    @Override
    protected void initWindow() {
        if (!IymIslemler.sayfaErisimHakkiVar(personel,
                "forms/iym/mahkemeKarar/MahkemeKararAnaSayfa.zul")) {
            this.mesajBoxGosterHata("Bu sayfaya girmeye yetkili değilsiniz.");
            Sessions.getCurrent().invalidate();
            Executions.sendRedirect("/dologin.zul");
        }

        kısmiIadeBilgileriEvent = new KismiIadeBilgileriEvent(this);
        kısmiIadeEventEvent = new KismiIadeEventEvent(this);

        if (personel.getGorevKodu() == null
                || personel.getGorevKodu().equals("")) {
            Executions.getCurrent().sendRedirect("Ana2.zul");
            return;
        }

        String encParametre = Executions.getCurrent().getParameter("param");
        String decParametre = IymIslemler.decodeString(encParametre);
        String xmlEvrakId = null;
        String mahkemeKararIslemId = null;
        String[] s1 = decParametre.split(";");
        for (int i = 0; i < s1.length; i++) {
            String[] s2 = s1[i].split("=");
            if (s2.length != 2) {
                continue;
            }

            if ("evrakId".equals(s2[0])) {
                try {
                    xmlEvrakId = s2[1];
                } catch (Exception e) {
                    logger.error("", e);
                }
            }

            if ("mahkemeKararId".equals(s2[0])) {
                try {
                    mahkemeKararIslemId = s2[1];
                } catch (Exception e) {
                    logger.error("", e);
                }
            }

            if ("detay".equals(s2[0])) {
                try {
                    if (s2[1].equalsIgnoreCase("1"))
                        this.detayMi = true;
                } catch (Exception e) {
                    logger.error("", e);
                }
            }
        }

        if (xmlEvrakId == null || mahkemeKararIslemId == null) {
            this.mesajBoxGosterHata("Mahkeme Karar ya da Evrak Bulunamadı!!!");
            return;
        }

        rowBul();
        aidiyatList = (Grid) getFellow("aidiyatList");
        hedeflerList = (Grid) getFellow("hedeflerList");
        aidiyatEkleCikarList = (Grid) getFellow("aidiyatEkleCikarList");
        mahkemeKoduDegistirList = (Grid) getFellow("mahkemeKoduDegistirList");
        hedefAdSoyadDegistirList = (Grid) getFellow("hedefAdSoyadDegistirList");
        genelKararList = (Grid) getFellow("genelKararList");
        dosyaGorFrame = (Iframe) getFellow("dosyaGorFrame");

        buttonGroupBox = (Groupbox) getFellow("buttonGroupBox");
        kararGroupbox = (Groupbox) getFellow("kararGroupBox");
        evrakOnaylayanLbl = (Label) getFellow("evrakOnaylayanLbl");
        evrakAciklamaLbl = (Label) getFellow("evrakAciklamaLbl");
        evrakAciklamaLbl2 = (Label) getFellow("evrakAciklamaLbl2");
        kurumAdiLbl = (Label) getFellow("kurumAdiLbl");
        evrakTarihiLbl = (Label) getFellow("evrakTarihiLbl");
        evrakIliLbl = (Label) getFellow("evrakIliLbl");
        evrakNoLbl = (Label) getFellow("evrakNoLbl");
        evrakSiraNoLbl = (Label) getFellow("evrakSiraNoLbl");
        kararTipLbl = (Label) getFellow("kararTipLbl");
        kararNoLbl = (Label) getFellow("kararNoLbl");
        sorusturmaNoLbl = (Label) getFellow("sorusturmaNoLbl");
        mahkemeIliLbl = (Label) getFellow("mahkemeIliLbl");
        mahkemeAdiLbl = (Label) getFellow("mahkemeAdiLbl");
        baslangicTarihiLbl = (Label) getFellow("baslangicTarihiLbl");
        bitisTarihiLbl = (Label) getFellow("bitisTarihiLbl");
        sucTipleriCombobox = (Combobox) getFellow("sucTipleriCombobox");
        sucTipiLayout = (Hlayout) getFellow("sucTipiLayout");
        itirazEtButton = (Button) getFellow("itirazEtButton");
        iadeButton = (Button) getFellow("iadeButton");
        temizleButton = (Button) getFellow("temizleButton");
        tanimlaButton = (Button) getFellow("tanimlaButton");
        onaylaButton = (Button) getFellow("onaylaButton");
        nobetciTanimliKontrolEdildiBtn = (Button) getFellow("nobetciTanimliKontrolEdildiBtn");
        sucTipiEkleButton = (Button) getFellow("sucTipiEkleButton");
        sucTipiLbl = (Label) getFellow("sucTipiLbl");
        itirazNedeniLbl = (Label) getFellow("itirazNedeniLbl");
        itirazIsleyenTarLbl = (Label) getFellow("itirazIsleyenTarLbl");
        itirazTalepLbl = (Label) getFellow("itirazTalepLbl");
        itirazIsleyenLbl = (Label) getFellow("itirazIsleyenLbl");
        itirazYaziLbl = (Label) getFellow("itirazYaziLbl");
        toplamHedefSayiLbl = (Label) getFellow("toplamHedefSayiLbl");
        toplamAidiyatSayiLbl = (Label) getFellow("toplamAidiyatSayiLbl");
        sucTipleriDiv = (Div) getFellow("sucTipleriDiv");
        sucTipiLayout.setVisible(false);
        hedefAidiyatTablar = (Tabbox) getFellow("hedefAidiyatTablar");
        genelKararTab = (Tab) getFellow("genelKararTab");
        genelKararTabPanel = (Tabpanel) getFellow("genelKararTabPanel");
        aidiyatEkleCikarTab = (Tab) getFellow("aidiyatEkleCikarTab");
        aidiyatEkleCikarTabPanel = (Tabpanel) getFellow("aidiyatEkleCikarTabPanel");
        mahkemeKoduDegistirTab = (Tab) getFellow("mahkemeKoduDegistirTab");
        mahkemeKoduDegistirTabPanel = (Tabpanel) getFellow("mahkemeKoduDegistirTabPanel");
        hedefAdSoyadDegistirTab = (Tab) getFellow("hedefAdSoyadDegistirTab");
        hedefAdSoyadDegistirTabPanel = (Tabpanel) getFellow("hedefAdSoyadDegistirTabPanel");
        hedefTab = (Tab) getFellow("hedefTab");
        hedefTabPanel = (Tabpanel) getFellow("hedefTabPanel");
        aidiyatTab = (Tab) getFellow("aidiyatTab");
        aidiyatTabPanel = (Tabpanel) getFellow("aidiyatTabPanel");
        mahkemeKararChildren = (Columnchildren) getFellow("mahkemeKararChildren");
        dosyaGorChildren = (Columnchildren) getFellow("dosyaGorChildren");
        itirazLayout = (Layout) getFellow("itirazLayout");
        aciklamaTxt = (Textbox) getFellow("aciklamaTxt");
        uyariMesajiDiv = (Div) getFellow("uyariMesajiDiv");

        oncekiKismiIadeBilgiButton = (Button) getFellow("oncekiKismiIadeBilgiButton");

                Toolbarbutton hedefIadeEtBtn = (Toolbarbutton) getFellow("hedefIadeEtBtn");;

        if (!detayMi) {
            Include inc2 = (Include) this.getDesktop().getPage("ANA_PENCERE")
                    .getFellow("contents").getFellow("xcontents");
            mahkemeKararAnaSayfa = (MahkemeKararAnaSayfaWindow) inc2
                    .getFellow("mahkemeKararAnaSayfa");
            islenecekEvraklarTree = (Tree) mahkemeKararAnaSayfa
                    .getFellow("islenecekEvraklarTree");
        }

        aidiyatList.setRowRenderer(new AidiyatListRowRenderer());
        hedeflerList.setRowRenderer(new HedeflerListRowRenderer(this, new Long(xmlEvrakId), false));
        hedefAdSoyadDegistirList
                .setRowRenderer(new HedefAdSoyadDegistirListRowRenderer());
        mahkemeKoduDegistirList
                .setRowRenderer(new MahkemeKoduDegistirListRowRenderer());
        aidiyatEkleCikarList
                .setRowRenderer(new AidiyatEkleCikarListRowRenderer());
        genelKararList.setRowRenderer(new GenelKararListRowRenderer());

        this.genelKararTab.setVisible(false);
        this.genelKararTabPanel.setVisible(false);
        this.aidiyatEkleCikarTab.setVisible(false);
        this.aidiyatEkleCikarTabPanel.setVisible(false);
        this.mahkemeKoduDegistirTab.setVisible(false);
        this.mahkemeKoduDegistirTabPanel.setVisible(false);
        this.hedefAdSoyadDegistirTab.setVisible(false);
        this.hedefAdSoyadDegistirTabPanel.setVisible(false);
        this.hedefTab.setVisible(false);
        this.hedefTabPanel.setVisible(false);
        this.aidiyatTab.setVisible(false);
        this.aidiyatTabPanel.setVisible(false);




        this.xmlEvrak = srv.getEvrakBilgileri(new Long(xmlEvrakId));

        if (srv.kararaItirazEdilmisMi(this.xmlEvrak.getId())) {

            List<ItirazEvrakPojo> lst1 = srv.itirazListesiGetir(xmlEvrak.getId());
            ItirazEvrakPojo itirazEvrakPojo = lst1.get(0);
            String itirazAciklama = "";
            if(!TemelIslemler.isNullOrEmpty(itirazEvrakPojo.getDurum()) && itirazEvrakPojo.getDurum().equals("SILINDI")){
                    itirazAciklama = "SİLİNMİŞ İTİRAZ : " +  itirazEvrakPojo.getSilinmeNedeni();
            }
            else{
                if(itirazEvrakPojo.getIsleyenId() == null ||itirazEvrakPojo.getIsleyenId() == 0L){
                    itirazAciklama = "İŞLENMEMİŞ İTİRAZ";
                }
            }


            if(lst1.size() <= 0){
                itirazLayout.setVisible(false);
            }
            else{

                String adiSoyadi = itirazEvrakPojo.getIsteyenAdSoyad();
                String itirazNedeni = itirazEvrakPojo.getItirazNedeni() + " / " + itirazEvrakPojo.getItirazNedeniKodu();

                /*
                itirazNedeniLbl.setValue(itirazNedeni);
                itirazTalepLbl.setValue(adiSoyadi + " - " + itirazEvrakPojo.getTalepTarihi() );

                if(itirazEvrakPojo.getIsleyenId() == null ||itirazEvrakPojo.getIsleyenId() == 0L) {
                    itirazIsleyenLbl.setValue(itirazAciklama);
                }
                else{
                    itirazIsleyenLbl.setValue(itirazEvrakPojo.getIsleyenAdSoyad() + " / " + itirazEvrakPojo.getIslemeTarihi() );
                }*/

                itirazNedeniLbl.setValue(itirazNedeni);
                itirazTalepLbl.setValue(adiSoyadi + " - " + itirazEvrakPojo.getTalepTarihi() );

                boolean itirazSilinmisMi = !TemelIslemler.isNullOrEmpty(itirazEvrakPojo.getDurum())  && itirazEvrakPojo.getDurum().equals("SILINDI") ? true : false;
                if (itirazSilinmisMi) {
                    itirazIsleyenTarLbl.setValue("SİLEN:");
                    String ad = itirazEvrakPojo.getSilenAdSoyad();
                    String tarih = TemelIslemler.nullTemizle(itirazEvrakPojo.getSilinmeTarihi()) != null ? TemelIslemler.nullTemizle(itirazEvrakPojo.getSilinmeTarihi()) : "Tarih yok";
                    itirazIsleyenLbl.setValue(ad + "-" + tarih + "-" + itirazEvrakPojo.getSilinmeNedeni());
                }
                else{

                    if(itirazEvrakPojo.getIsleyenId() == null ||itirazEvrakPojo.getIsleyenId() == 0L) {
                        itirazIsleyenTarLbl.setValue("İTİRAZ:");
                        itirazIsleyenLbl.setValue("İşlenmemiş İtiraz");
                        itirazIsleyenLbl.setStyle("background-color:black;  padding:3px; border-radius:5px;color:white; font-weight:bold; font-size:13px;");
                    }
                    else{
                        itirazIsleyenTarLbl.setValue("İŞLEYEN:");
                        String islenmeTarihi = TemelIslemler.nullTemizle(itirazEvrakPojo.getIslemeTarihi());
                        itirazIsleyenLbl.setValue(itirazEvrakPojo.getIsleyenAdSoyad() + " Tarih:" +islenmeTarihi);
                    }
                }

                if(!TemelIslemler.isNullOrEmpty( itirazEvrakPojo.getItirazYaziSayi())){
                    itirazYaziLbl.setVisible(true);
                    itirazYaziLbl.setValue("İtiraz Yazı : " + itirazEvrakPojo.getItirazYaziSayi() + " - " + itirazEvrakPojo.getItirazYaziTarih());
                }
                else{
                    itirazYaziLbl.setVisible(false);
                    itirazYaziLbl.setValue("");
                }


                   /*
                itirazIsleyenTarLbl.setValue("SİLEN:");
                String ad = (lst.get(0).getSilenAdSoyad().split(" "))[0].substring(0, 1) + "." + (lst.get(0).getSilenAdSoyad().split(" "))[1];
                String tarih = TemelIslemler.nullTemizle(lst.get(0).getSilinmeTarihi()) != null ? TemelIslemler.nullTemizle(lst.get(0).getSilinmeTarihi()).substring(0, 10) : "Tarihyok";
                itirazIsleyenLbl.setValue(ad + "-" + tarih + "-" + itirazEvrakPojo.getSilinmeNedeni());
                */
            }


            /*
            List<ItirazEvrakPojo> lst = srv.itirazBilgiGetir(xmlEvrak.getId());

            if (lst.size() > 0) {
                //Redmine #5558 Geregi
                itirazLayout.setVisible(true);

                ItirazEvrakPojo itirazEvrakPojo = lst.get(0);
                boolean itirazSilinmisMi = !TemelIslemler.isNullOrEmpty(itirazEvrakPojo.getDurum())  && itirazEvrakPojo.getDurum().equals("SILINDI") ? true : false;
                if (itirazSilinmisMi) {
                    itirazIsleyenTarLbl.setValue("SİLEN:");
                    String ad = (lst.get(0).getSilenAdSoyad().split(" "))[0].substring(0, 1) + "." + (lst.get(0).getSilenAdSoyad().split(" "))[1];
                    String tarih = TemelIslemler.nullTemizle(lst.get(0).getSilinmeTarihi()) != null ? TemelIslemler.nullTemizle(lst.get(0).getSilinmeTarihi()).substring(0, 10) : "Tarihyok";
                    itirazIsleyenLbl.setValue(ad + "-" + tarih + "-" + itirazEvrakPojo.getSilinmeNedeni());

                } else {
                    String islenmeTarihi = TemelIslemler.nullTemizle(lst.get(0).getIslemeTarihi());

                    if(TemelIslemler.isNullOrEmpty(islenmeTarihi)){
                        itirazIsleyenLbl.setValue(lst.get(0).getIsleyenAdSoyad() + " !!!! İşlenmemiş İtiraz   ");
                        itirazIsleyenLbl.setStyle("background-color:black;  padding:3px; border-radius:5px;color:white; font-weight:bold; font-size:13px;");
                    }
                    else{
                        itirazIsleyenLbl.setValue(lst.get(0).getIsleyenAdSoyad() + " Tarih:" +islenmeTarihi);
                    }

                }

                itirazEtButton.setVisible(false);
                itirazNedeniLbl.setValue(TemelIslemler.nullTemizle(lst.get(0).getItirazYaziSayi()) + " / " + lst.get(0).getItirazNedeni());


            } else
                itirazLayout.setVisible(false);
            */
        }
        else {
            itirazLayout.setVisible(false);
        }


        if (personel.getYetkiList().contains(IYMSabitler.MAHKEME_KARAR_TANIMLAMA)) { //Hakim icin
            ArrayList<MahkemeKararAtamaPojo> mahkemeKararAtamaListesi = srv.mahkemeKararAtamaGetir(xmlEvrak.getId(), 2);
            if (mahkemeKararAtamaListesi != null && mahkemeKararAtamaListesi.size() > 0) {
                MahkemeKararAtamaPojo atama = mahkemeKararAtamaListesi.get(0);
                if (atama.getDurum().equals("A") && atama.getGonderilenId().intValue() == (int) personel.getIymId()) {
                    nobetOnayliEvrak = true;
                }
            }
        }


        this.nobetciTanimliKontrolEdildiBtn.setVisible(false);
        if (nobetOnayliEvrak) {


            this.mahkemeKarar = srv.getMahkemeKararBilgileri(new Long(mahkemeKararIslemId));
            this.mahkemeKarar.setMahkemeKararTuru(srv.turBelirle(this.mahkemeKarar, this.detayMi));

            //redmine : 4096 Geregi
            if(this.mahkemeKarar != null){
                List<MahkemeKararTalepPojo> talepListesi = srv.kismiIadeListesiBy(this.mahkemeKarar.getMahkemeKararNo(), this.mahkemeKarar.getSorusturmaNo(), this.mahkemeKarar.getKararTip());
                showKismiIadeBtn(talepListesi, this.xmlEvrak.getId());
            }




            PersonelService srvPer = ServiceManager.getPersonelService();
            boolean hukukNobetciMi = srvPer.hukukNobetciMi(personel.getPersonelId());
			/*
		    if (this.mahkemeKarar.getDurum() != null && this.mahkemeKarar.getDurum().equalsIgnoreCase("ONAYLANDI")) {
				if(hukukNobetciMi){
					this.onaylaButton.setVisible(false);
					this.tanimlaButton.setVisible(true);
				}else if(mahkemeKararTanimlamaMi()){
					this.onaylaButton.setVisible(false);
					this.tanimlaButton.setVisible(true);
					this.temizleButton.setLabel("Kontrol Grubuna İade");
					this.temizleButton.setWidth("200");
				}else{
					if(srv.tumKararlarOnaylanmismi(xmlEvrak.getId())){
						this.onaylaButton.setVisible(false);
						this.tanimlaButton.setVisible(false);
						this.itirazEtButton.setVisible(false);
						//this.temizleButton.setVisible(false);
						this.iadeButton.setVisible(false);
					}else{
						this.onaylaButton.setVisible(true);
						this.tanimlaButton.setVisible(false);
						this.itirazEtButton.setVisible(true);
						this.temizleButton.setVisible(true);
						this.iadeButton.setVisible(true);
					}
				}
			} else{
				if(hukukNobetciMi){
					this.onaylaButton.setVisible(true);
					this.tanimlaButton.setVisible(false);
				}else if(mahkemeKararTanimlamaMi()){
					this.onaylaButton.setVisible(false);
				}
				this.tanimlaButton.setVisible(false);
			}
			IymIslemler.IymLogger(personel, Sessions.getCurrent(),
					"Mahkeme Karar Kayıt Girişi",
					"Evrak Id : " + xmlEvrak.getId() + " Evrak Sayı : "
							+ xmlEvrak.getEvrakSiraNo()
							+ " Mahkeme Karar Talep Id : "
							+ this.mahkemeKararIslem.getId(), this, null);
			
			*/

            this.nobetciTanimliKontrolEdildiBtn.setVisible(true);
            this.onaylaButton.setVisible(false);
            this.tanimlaButton.setVisible(false);
            this.temizleButton.setVisible(false);
            this.iadeButton.setVisible(false);
            this.itirazEtButton.setVisible(true);
            this.buttonGroupBox.setVisible(true);
            this.sucTipiLbl.setVisible(false);
            this.sucTipleriCombobox.setVisible(false);
            this.sucTipiEkleButton.setVisible(false);


        } else if (detayMi) {
            this.mahkemeKarar = srv.getMahkemeKararBilgileri(new Long(mahkemeKararIslemId));
            this.mahkemeKarar.setMahkemeKararTuru(srv.turBelirle(this.mahkemeKarar, this.detayMi));




            this.onaylaButton.setVisible(false);
            this.tanimlaButton.setVisible(false);
            this.temizleButton.setVisible(false);
            this.iadeButton.setVisible(false);
            this.itirazEtButton.setVisible(true);
            this.buttonGroupBox.setVisible(true);
            this.sucTipiLbl.setVisible(false);
            this.sucTipleriCombobox.setVisible(false);
            this.sucTipiEkleButton.setVisible(false);

            if (personel.getYetkiList().contains(IYMSabitler.MAHKEME_KARAR_DENETCI))
                itirazEtButton.setVisible(false);

            IymIslemler.IymLogger(
                    personel,
                    Sessions.getCurrent(),
                    "Mahkeme Karar Kayıt Girişi",
                    "Evrak Id : " + xmlEvrak.getId() + " Evrak Sayı : "
                            + xmlEvrak.getEvrakSiraNo()
                            + " Mahkeme Karar No : "
                            + this.mahkemeKarar.getId(), this, null);
        } else {
            this.mahkemeKararIslem = srv.getMahkemeKararIslemBilgileri(new Long(mahkemeKararIslemId));
            this.mahkemeKararIslem.setMahkemeKararTuru(srv.turBelirle(this.mahkemeKararIslem, this.detayMi));

            //redmine : 4096 Geregi
            if(this.mahkemeKararIslem != null){

                List<MahkemeKararTalepPojo> talepListesi = srv.kismiIadeListesiBy(this.mahkemeKararIslem.getMahkemeKararNo(), this.mahkemeKararIslem.getSorusturmaNo(), this.mahkemeKararIslem.getKararTip());
                showKismiIadeBtn(talepListesi, this.xmlEvrak.getId());

            }

            PersonelService srvPer = ServiceManager.getPersonelService();
            boolean hukukNobetciMi = srvPer.hukukNobetciMi(personel.getPersonelId());
            if (this.mahkemeKararIslem.getDurum() != null
                    && this.mahkemeKararIslem.getDurum().equalsIgnoreCase(
                    "ONAYLANDI")) {
                if (hukukNobetciMi) {
                    this.onaylaButton.setVisible(false);
                    this.tanimlaButton.setVisible(true);
                } else if (mahkemeKararTanimlamaMi()) {
                    this.onaylaButton.setVisible(false);
                    this.tanimlaButton.setVisible(true);
                    this.temizleButton.setLabel("Kontrol Grubuna İade");
                    this.temizleButton.setWidth("200");
                } else {
                    if (srv.tumKararlarOnaylanmismi(xmlEvrak.getId())) {
                        this.onaylaButton.setVisible(false);
                        this.itirazEtButton.setVisible(false);
                        //this.temizleButton.setVisible(false);
                        this.iadeButton.setVisible(false);


                        boolean onayci1709Arasinda = personel.getYetkiList().contains(IYMSabitler.MAHKEME_KARAR_ONAYLAMA) && TemelIslemler.islemSaati1709Arasi();
                        if (onayci1709Arasinda) {
                            this.tanimlaButton.setVisible(true);
                        }


                    } else {
                        this.onaylaButton.setVisible(true);
                        this.tanimlaButton.setVisible(false);
                        this.itirazEtButton.setVisible(true);
                        this.temizleButton.setVisible(true);
                        this.iadeButton.setVisible(true);
                    }
                }
            } else {
                if (hukukNobetciMi) {
                    this.onaylaButton.setVisible(true);
                    this.tanimlaButton.setVisible(false);
                } else if (mahkemeKararTanimlamaMi()) {
                    this.onaylaButton.setVisible(false);
                }
                this.tanimlaButton.setVisible(false);
            }
            IymIslemler.IymLogger(personel, Sessions.getCurrent(),
                    "Mahkeme Karar Kayıt Girişi",
                    "Evrak Id : " + xmlEvrak.getId() + " Evrak Sayı : "
                            + xmlEvrak.getEvrakSiraNo()
                            + " Mahkeme Karar Talep Id : "
                            + this.mahkemeKararIslem.getId(), this, null);

        }

        bilgileriAyarla();



        this.getDesktop().setAttribute("mahkemeKararKayitWindow", this);
        this.dosyaGorFrame.setScrolling("no");

        if (mahkemeKararAnaSayfa != null
                && mahkemeKararAnaSayfa.getEvrakOnizlemeCheck().isChecked()) {
            this.dosyaGorFrame.setVisible(false);
        } else {
            this.dosyaGorFrame.setVisible(true);
        }


        hedefIadeIslemleri();

    }


    public void itirazTarihceEkrani() {

        Window win = new Window();
        win.setTitle("Mahkeme Karar İtiraz Listesi");
        win.setWidth("90%");
        win.setClosable(true);
        win.setParent(this);
        Include inc = new Include();
        inc.setParent(win);
        Long evrakId = this.xmlEvrak.getId();
        inc.setSrc("/forms/iym/MahkemeKararItirazGrid.zul?evrakId=" + evrakId);
        win.doModal();

    }

    //redmine : 4096 Geregi
    private void showKismiIadeBtn(List<MahkemeKararTalepPojo> talepListesi, Long currentEvrakId){
        if(talepListesi != null){

            MahkemeKararTalepPojo oncekiIade = null;
            for(MahkemeKararTalepPojo talep : talepListesi){
                if(talep.getEvrakId().longValue() != currentEvrakId.longValue()){
                    oncekiIade = talep;
                    break;
                }
            }

            if(oncekiIade != null){
                oncekiKismiIadeBilgiButton.setVisible(true);
                oncekiKismiIadeBilgiButton.setAutag(oncekiIade.getId().toString());
            }
            else{
                oncekiKismiIadeBilgiButton.setVisible(false);
                oncekiKismiIadeBilgiButton.setAutag(null);
            }
        }
    }

    //redmine : 4096 Geregi
    public  void oncekiKismiIadeGoster(){
        try {
            String talepIdStr = oncekiKismiIadeBilgiButton.getAutag();
            if (!TemelIslemler.isNullOrEmpty(talepIdStr)) {
                Long talepId = Long.parseLong(talepIdStr);

                Iymservice iym = ServiceManager.getIymservice();
                IymServiceMahkemeKarar iymServiceMahkemeKarar = ServiceManager.getIymServiceMahkemeKarar();
                MahkemeKararTalepPojo talepBilgisi = iymServiceMahkemeKarar.getMahkemeKararTalepBilgileri(talepId);
                if(talepBilgisi == null){
                    mesajBoxGosterHata("Mahkeme karar talep bilgisi bulunamadı. TalepId:" + talepId);
                    return;
                }

                EvrakKayitPojo evrak = iym.getEvrakById(talepBilgisi.getEvrakId());
                if (evrak != null) {
                    List<HedeflerTalepPojo> hedefler = iymServiceMahkemeKarar.kismiIadeHedefListesi(evrak.getEvrakId());
                    StringBuilder hedeflerStr = new StringBuilder();
                    for(HedeflerTalepPojo hedef : hedefler){
                        if(hedeflerStr.indexOf(hedef.getHedefNo()) < 0) {
                            hedeflerStr.append(hedef.getHedefNo() + "\n");
                        }
                    }

                    String msgStr = "Evrak Sıra No : " + evrak.getEvrakSiraNo() + "\n"  + evrak.getAciklama() ;
                    msgStr += "\n\nHedefler :\n " + hedeflerStr;

                    mesajBoxGosterInformation(msgStr);
                } else {
                    mesajBoxGosterHata("Evrak bulunamadı. EvrakId:" + this.xmlEvrak.getId());
                }
                IymIslemler.IymLogger(personel, Sessions.getCurrent(), "Hedef İade Bilgisi Gösterildi. ", "Evrak Id : " + xmlEvrak.getId(), this, null);
            }

        }catch(Exception ex){
            mesajBoxGosterHata("Hata oluştu : " + ex.getMessage());
        }

    }

    //Adli uzatma kararlarında hedef süresi 31 günden fazlaysa
    //kullanıcıyı uyarıyor ve false dönüyor
    private boolean adliKararUzatmaSuresiKontrol(MAHKEME_KARAR_ISLEM_TURU mahkemeKararIslemTuru, String mahkemeKararTipi,
                                                 List<HedeflerIslemPojo> hedefler) {

        boolean sonuc = true;
		/*
		 * 
		 * 16.12.2016 tarihinde mustafa duruakan maili ile bu kontrol kaldırıldı
		 *
		 *		
		
		if(mahkemeKararIslemTuru.equals(MAHKEME_KARAR_ISLEM_TURU.UZATMA) && 
				(mahkemeKararTipi.equals(MAHKEME_KARAR_TIPLERI.ADLI_HAKIM_KARARI.getKararKodu()) ||
						 mahkemeKararTipi.equals(MAHKEME_KARAR_TIPLERI.ADLI_YAZILI_EMIR.getKararKodu())   ||
						 mahkemeKararTipi.equals(MAHKEME_KARAR_TIPLERI.ADLI_KHK_YAZILI_EMIR.getKararKodu())   ||
				 mahkemeKararTipi.equals(MAHKEME_KARAR_TIPLERI.ADLI_ASKERI_HAKIM_KARARI.getKararKodu())))
				 {
					for(HedeflerIslemPojo hedef : hedefler)
					{
						if(!adliHedefSureKontrol(hedef))
						{
							uyariMesajiGoster("Uzatma süresi 31 günden fazla olmamalıdır");
							sonuc=false;
						}
					}
					
				 }
		 */
        return sonuc;
    }

    public boolean adliHedefSureKontrol(HedeflerIslemPojo h) {
        Long sure = 0l;
        Long gun = 0l;

        Date hedefBaslangicTarihi = TemelIslemler.ParseTarihZaman(h.getBaslamaTarihi());
        Date hedefBitisTarihi = TemelIslemler.ParseTarihZaman(h.getBitisTarihi());

        sure = TemelIslemler.TarihGunFark(hedefBitisTarihi, hedefBaslangicTarihi);

        if (sure > 0)
            gun = sure / (24 * 60 * 60 * 1000);

        if (gun > 31) {
            return false;
        } else
            return true;

    }

    private void uyariMesajiGoster(String mesaj) {

        IymIslemler.uyariMesaj(uyariMesajiDiv, mesaj, "middle_center", 20000);

    }

    private void rowBul() {
        try {
            this.r = (Row) this.getDesktop().getAttribute("row");
            if (this.r == null)
                throw new Exception();
        } catch (Exception e) {
            //logger.error("", e);
            try {
                if (this.getDesktop().getAttribute("row") != null && this.getDesktop().getAttribute("row") instanceof Treerow) {
                    this.tr = (Treerow) this.getDesktop().getAttribute("row");
                    if (this.tr == null)
                        throw new Exception();
                }
            } catch (Exception es) {
                //logger.error("", es);
            }
        }
    }

    public void hedeflerListBind() {
        hedeflerListBind(this.mahkemeKararIslem.getMahkemeKararTuru(), this.mahkemeKararIslem.getId());
    }

    public void hedeflerListBind(MAHKEME_KARAR_ISLEM_TURU tur, Long mahkemeKararId) {
        this.aidiyatTab.setVisible(true);
        this.aidiyatTabPanel.setVisible(true);
        this.hedefTab.setVisible(true);
        this.hedefTabPanel.setVisible(true);
        this.hedefAidiyatTablar.setSelectedPanel(this.hedefTabPanel);
        IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
        if (tur == MAHKEME_KARAR_ISLEM_TURU.TANIMLAMA
                || tur == MAHKEME_KARAR_ISLEM_TURU.UZATMA
                || tur == MAHKEME_KARAR_ISLEM_TURU.SONLANDIRMA) {
            List<HedeflerIslemPojo> hedefler = null;

            if (mahkemeKararIslem != null) {
                hedefler = srv.hedeflerIslemListGetir(mahkemeKararId, this.mahkemeKararIslem.getKararTip());
            } else if (mahkemeKarar != null) {
                hedefler = srv.hedeflerListGetir(mahkemeKararId, this.mahkemeKarar.getKararTip());
            }

            //null pointer.
            hedefler = hedefler == null ? new ArrayList<HedeflerIslemPojo>() : hedefler;

            ListModelList lm = new ListModelList(hedefler);
            hedeflerList.setModel(lm);
            toplamHedefSayiLbl.setValue(hedefler.size() + "");

            if (tur != MAHKEME_KARAR_ISLEM_TURU.SONLANDIRMA) {
                List<MahkemeAidiyatIslemPojo> aidiyatlar = null;
                if (mahkemeKarar != null) {
                    aidiyatlar = srv.aidiyatlarListGetir(mahkemeKararId);
                }
                if (mahkemeKararIslem != null) {
                    aidiyatlar = srv.aidiyatlarIslemListGetir(mahkemeKararId);
                }

                lm = new ListModelList(aidiyatlar);
                aidiyatList.setModel(lm);
                toplamAidiyatSayiLbl.setValue(aidiyatlar.size() + "");
                sucTipiLayout.setVisible(true);
                sucTipleriBind();
                mahkemeKararSucTipleriBind();
            }
            mahkemeKararGroupBoxAcKapat();

            if (!detayMi) {
                if (nobetOnayliEvrak) {

                } else {
                    //Adli uzatmalarda 31 gün kontrolü false dönerse
                    //hedef listesi renderer güncelleniyor ve hedef süresinin üst tarafında "Süreyi düzelt" butonu çıkıyor
                    if (!adliKararUzatmaSuresiKontrol(tur, this.mahkemeKararIslem.getKararTip(), hedefler)) {
                        hedeflerList.setRowRenderer(new HedeflerListRowRenderer(this,this.xmlEvrak.getId(), true));
                    }
                }
            }
        }
    }

    public void mahkemeKararGroupBoxAcKapat() {
	/*	int count = hedeflerList.getRows().getChildren().size();

		Groupbox kararGroupBox = (Groupbox) getFellow("kararGroupBox");
		int size;

		if (!kararGroupBox.isOpen()) {
			if (count > 15) {
				count = 15;
			}

		} else {
			if (count > 5) {
				count = 5;
			}
		}

		size = count * 50 + 40;
		hedeflerList.setHeight(size + "px");
		hedefTabPanel.setHeight(size + "px");
		hedefAidiyatTablar.setHeight(size+"px");*/
    }

    public void sucTipiEkle() {
        IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
        if (sucTipleriCombobox.getSelectedItem() != null) {
            if (sucTipleriCombobox.getSelectedItem().getValue() != null) {
                String tip = (String) sucTipleriCombobox.getSelectedItem()
                        .getValue();
                if (!tip.equalsIgnoreCase(""))
                    if (!srv.mahkemeKararSucTipiEkle(mahkemeKararIslem.getId(),
                            tip)) {
                        IymIslemler.IymLogger(personel, Sessions.getCurrent(),
                                "Mahkeme Karar Suç Tipi Eklenemedi",
                                "Mahkeme Karar Talep Id : "
                                        + this.mahkemeKararIslem.getId()
                                        + " Suç Tipi : " + tip, this, null);
                        this.mesajBoxGosterHata("Suç tipi eklenemedi!!!");
                    } else {
                        mahkemeKararSucTipleriBind();
                        IymIslemler.IymLogger(personel, Sessions.getCurrent(),
                                "Mahkeme Karar Suç Tipi Eklendi",
                                "Mahkeme Karar Talep Id : "
                                        + this.mahkemeKararIslem.getId()
                                        + " Suç Tipi : " + tip, this, null);
                    }
            }
        }
    }

    private void mahkemeKararSucTipleriBind() {
        final IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
        ArrayList<MahkemeSucTipleriPojo> sucList = null;
        if (mahkemeKarar != null) {
            sucList = (ArrayList<MahkemeSucTipleriPojo>) srv.mahkemeKararSucTipleriListGetir(this.mahkemeKarar.getId());
        } else {
            sucList = (ArrayList<MahkemeSucTipleriPojo>) srv.mahkemeKararSucTipleriIslemListGetir(this.mahkemeKararIslem.getId());
        }
        sucTipleriDiv.getChildren().clear();
        if (sucList != null) {
            for (final MahkemeSucTipleriPojo s : sucList) {
                Toolbarbutton t = new Toolbarbutton(s.getSucAciklama());
                t.setVisible(true);
                t.setParent(sucTipleriDiv);
                if (detayMi)
                    t.setDisabled(true);
                final MahkemeKararIslemPojo i2 = this.mahkemeKararIslem;
                final Long kararId = this.mahkemeKararIslem != null ? this.mahkemeKararIslem.getId() : this.mahkemeKarar.getId();
                t.addEventListener("onClick", new EventListener<Event>() {
                    @Override
                    public void onEvent(Event arg0) throws Exception {
                        if (!srv.mahkemeKararSucTipiCikar(kararId, s.getSucTipi())) {
                            IymIslemler.IymLogger(personel, Sessions.getCurrent(),
                                    "Mahkeme Karar Suç Tipi Silinemedi",
                                    "Mahkeme Karar Talep Id : " + kararId
                                            + " Suç Tipi : " + s.getSucTipi(),
                                    this, null);
                            mesajBoxGosterHata("Suç tipi silinemedi!!!");
                        } else {
                            IymIslemler.IymLogger(personel, Sessions.getCurrent(),
                                    "Mahkeme Karar Suç Tipi Silindi",
                                    "Mahkeme Karar Talep Id : " + kararId
                                            + " Suç Tipi : " + s.getSucTipi(),
                                    this, null);
                            mahkemeKararSucTipleriBind();
                        }
                    }
                });
            }
        }

    }

    private void sucTipleriBind() {
        IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
        ArrayList<MahkemeSucTipleriPojo> sucList = null;
        if (mahkemeKararIslem != null) {
            sucList = (ArrayList<MahkemeSucTipleriPojo>) srv.sucTipleriListGetir(this.mahkemeKararIslem.getKararTip());
        } else if (mahkemeKarar != null) {
            sucList = (ArrayList<MahkemeSucTipleriPojo>) srv.sucTipleriListGetir(this.mahkemeKarar.getKararTip());
        }

        Comboitem li = new Comboitem();

        li.setLabel("");
        li.setValue(null);
        li.setParent(sucTipleriCombobox);
        if (sucList != null) {
            for (int i = 0; i < sucList.size(); i++) {
                MahkemeSucTipleriPojo s = (MahkemeSucTipleriPojo) sucList.get(i);
                li = new Comboitem();
                li.setLabel(s.getSucAciklama());
                li.setValue(s.getSucTipi());
                li.setParent(sucTipleriCombobox);
            }
        }
        sucTipleriCombobox.setSelectedIndex(-1);

    }

    public void onayla() {
        IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();

        if (this.mahkemeKararIslem.getMahkemeKararTuru().equals(MAHKEME_KARAR_ISLEM_TURU.SONLANDIRMA)
                || this.mahkemeKararIslem.getMahkemeKararTuru().equals(MAHKEME_KARAR_ISLEM_TURU.UZATMA)) {
            if (!srv.ilsikilendirilmemisHedefKontrol(this.mahkemeKararIslem.getId(), this.mahkemeKararIslem.getMahkemeKararTuru())) {
                String logStr = "Mahkeme Karar Talep Id : " + this.mahkemeKararIslem.getId();
                IymIslemler.IymLogger(personel, Sessions.getCurrent(),  "Kararda İlişkilendirilmemiş Hedefler Var", logStr, this, null);
                this.mesajBoxGosterHata("Kararda İlişkilendirilmemiş Hedefler Var!!!");
            }
        }

        if (!srv.mahkemeKararIslemOnayla(this.mahkemeKararIslem)) {
            IymIslemler.IymLogger(personel, Sessions.getCurrent(),
                    "Mahkeme Karar Onaylanamadı", "Mahkeme Karar Talep Id : " + this.mahkemeKararIslem.getId(), this, null);
            this.mesajBoxGosterHata("Karar Onaylanamadı. Yeniden deneyiniz.");

            return;
        } else {
            IymIslemler.IymLogger(personel, Sessions.getCurrent(),
                    "Mahkeme Karar Onaylandı", "Evrak Id : " + this.mahkemeKararIslem.getEvrakId() + " Mahkeme Karar Talep Id : "
                            + this.mahkemeKararIslem.getId(), this, null);

            //Mahkeme karar onaylandığı zaman
            mahkemeKararAnaSayfa.ekleOnaylananKararList(this.mahkemeKararIslem.getId());


            Treerow tr = null;
            if (islenecekEvraklarTree != null && islenecekEvraklarTree.getSelectedItem() != null
                    && islenecekEvraklarTree.getSelectedItem().getChildren() != null && islenecekEvraklarTree.getSelectedItem().getChildren().size() > 0) {
                //Cast exception'dan kurtulmak icin
                try {
                    if (islenecekEvraklarTree.getSelectedItem() != null && islenecekEvraklarTree.getSelectedItem().getChildren() != null && islenecekEvraklarTree.getSelectedItem().getChildren().size() > 0) {
                        if (islenecekEvraklarTree.getSelectedItem().getChildren().get(0) instanceof Treerow) {
                            tr = (Treerow) islenecekEvraklarTree.getSelectedItem().getChildren().get(0);
                        }
                    }
                } catch (Exception extmp) {

                }
            }

            if (tr != null) {
                mahkemeKararAnaSayfa.onaylananKararaOKIconEkle(tr);
            }


            try {//TODO bu try ın içinde onaylama yapıldıktan sonra, atamanın pasif yapılması, yeni atama satırının eklenmesi, ya da evrak_mahkeme_kara_islem seviye update i yapılması işlemleri exception throw edebilir
                // bu durumda onaylı mahkeme kararı, tanıma havuzuna muhtemelen dusmemiştir.

                PersonelService srvPer = ServiceManager.getPersonelService();
                boolean hukukNobetci = srvPer.hukukNobetciMi(personel.getPersonelId());

                boolean onayci1709Arasinda = personel.getYetkiList().contains(IYMSabitler.MAHKEME_KARAR_ONAYLAMA) && TemelIslemler.islemSaati1709Arasi();
                boolean tumKararlarOnaylanmis = srv.tumKararlarOnaylanmismiKontrol(xmlEvrak.getId());

                if (!hukukNobetci && onayci1709Arasinda) {
                    if (tumKararlarOnaylanmis) {
                        tanimla();
                        mahkemeKararAnaSayfa.sifirlaIslenecekEvrak();
                        return;
                    } else {
                        this.mesajBoxGosterHata("Karar Onaylanmıştır. \nTanımlama yapabilmek için bu evraka ait bütün kararları onaylayınız.");
                        //TODO : diger karar icin log ekle
                        this.onaylaButton.setVisible(false);
                        this.tanimlaButton.setVisible(false);
                        this.itirazEtButton.setVisible(false);
                        this.temizleButton.setVisible(true);
                        this.iadeButton.setVisible(false);
                        return;
                    }
                }

                if (tumKararlarOnaylanmis) {
                    if (!hukukNobetci) {
                        if (!srv.evrakiOnayGrubunaGonder(this.mahkemeKararIslem)) {
                            throw new Exception("Hata: Evrak Tanımalama Gruba Gonderilemedi");
                        }

                        MahkemeKararAtamaPojo atama = new MahkemeKararAtamaPojo();

                        atama.setEvrakId(xmlEvrak.getId());
                        atama.setKullaniciId((int) personel.getIymId());
                        atama.setDurum("A");
                        atama.setSeviye(1);
                        atama.setAciklama(aciklamaTxt.getValue());
                        atama.setSebebi(2);
                        atama.setGonderenId((int) personel.getIymId());
                        atama.setGonderilenId(0);

                        if (!srv.mahkemeKararAtamaOnayla(atama)) {
                            throw new Exception("Hata: Mahkeme karar atama yapılamadı!");
                        }

                    }
                    Tabpanel mahkemeKararPanel = (Tabpanel) mahkemeKararAnaSayfa
                            .getFellow("mahkemeKararAnaSayfaTabPanel");
                    Tabbox mahkemeKararTablar = (Tabbox) mahkemeKararAnaSayfa
                            .getFellow("mahkemeKararTablar");
                    mahkemeKararAnaSayfa.islenecekEvraklarListBind();

                    if (!hukukNobetci) {
                        mahkemeKararTablar.setSelectedPanel(mahkemeKararPanel);
                        this.detach();
                    }


                    if (!hukukNobetci) {
                        this.onaylaButton.setVisible(false);
                        this.tanimlaButton.setVisible(false);
                        this.itirazEtButton.setVisible(false);
                        //	this.temizleButton.setVisible(false);
                        this.iadeButton.setVisible(false);
                    } else {
                        this.tanimlaButton.setVisible(true);
                        this.itirazEtButton.setVisible(true);
                        this.temizleButton.setVisible(true);
                        this.iadeButton.setVisible(true);
                        this.onaylaButton.setVisible(false);
                    }


                } else {
                    this.onaylaButton.setVisible(true);
                    this.tanimlaButton.setVisible(false);
                    this.itirazEtButton.setVisible(true);
                    this.temizleButton.setVisible(true);
                    this.iadeButton.setVisible(true);
                }

            } catch (Exception e) {
                logger.error("", e);
            }
        }

    }

    public void iadeEt() {
        new XmlEvrakIadeEt(this.buttonGroupBox, this.xmlEvrak).iadeEt();
        IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
        XmlEvrakPojo e = srv.getEvrakBilgileri(this.xmlEvrak.getId());
        if (e.getDurumu() == null || "".equalsIgnoreCase(e.getDurumu())) {
            this.detach();
        }


    }


    public void itirazEt() {
        new XmlEvrakItirazEt(this.buttonGroupBox, this.xmlEvrak).itirazEt();
	/*	Include inc2 = (Include) this.getDesktop().getPage("ANA_PENCERE")
				.getFellow("contents").getFellow("xcontents");*/
        //this.detach();
    }

    public void tanimla() {
        IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();

        if (!srv.tumKararlarOnaylanmismiKontrol(xmlEvrak.getId())) {

            String hataMsg = "Evrakta onaylanmamış karar bulunmaktadır. Lütfen sol taraftaki ağaç yapısını kullanarak kararı onaylayınız. ";
            IymIslemler.IymLogger(personel, Sessions.getCurrent(), "Hata :  Evrakta Onaylanmamış Karar Bulunmaktadır.", "Evrak Id : " + xmlEvrak.getId(), this, null);
            this.mesajBoxGosterHata(hataMsg);
            return;

        }
		
		/*String sonuc = srv.tanimlayaGonder(xmlEvrak,
					this.mahkemeKararIslem.getMahkemeKararTuru());
		if (sonuc== null ||  !sonuc.equalsIgnoreCase("1")) {
			IymIslemler.IymLogger(personel, Sessions.getCurrent(),
					"Evrak Tanımlanamadı", "Evrak Id : " + xmlEvrak.getId()
							+ " Hata : " + sonuc, this, null);
			this.mesajBoxGosterHata("Hata :  Evrak Tanımlanamadı : " + sonuc);
			return;
		}*/


        String aciklama = TemelIslemler.isNullOrEmpty(aciklamaTxt.getValue()) ? "" : aciklamaTxt.getValue().trim();
        Hatalar hatalar = srv.tanimla(xmlEvrak, aciklama);
        ArrayList<HatalarPojo> hatalarListe = hatalar.hatalarListeGetir();
        if (!hatalarListe.isEmpty()) {
            StringBuilder hatalarStr = new StringBuilder();
            for (HatalarPojo h : hatalarListe) {
                hatalarStr.append(h.getHataMesaji()).append("   \n");
            }
            IymIslemler.IymLogger(personel, Sessions.getCurrent(),
                    "Evrak Tanımlanamadı", "Evrak Id : " + xmlEvrak.getId() + " Hata : " + hatalarStr,
                    this, null);

            this.onaylaButton.setVisible(false);
            this.iadeButton.setVisible(false);
            this.itirazEtButton.setVisible(false);
            this.mesajBoxGosterHata("Hata :  Evrak Tanımlanamadı! \n " + hatalarStr);

            return;
        }

        IymIslemler.IymLogger(personel, Sessions.getCurrent(),
                "Evrak Tanımlandı", "Evrak Id : " + xmlEvrak.getId(), this,
                null);
        mahkemeKararAnaSayfa.cikarOnaylananKararList(this.mahkemeKararIslem.getId());
        mahkemeKararAnaSayfa.cikarAcikEvrakList(this.xmlEvrak.getId());

        MahkemeKararAtamaPojo atama = new MahkemeKararAtamaPojo();

        atama.setEvrakId(xmlEvrak.getId());
        atama.setSeviye(1);

        if (!srv.mahkemeKararAtamaTanimla(atama)) {
            this.mesajBoxGosterHata("Hata: Atama kaldırılamadı!");
        }


        try {

            Treeitem ti = (Treeitem) islenecekEvraklarTree.getSelectedItem();
            ti.setDisabled(true);
            Treeitem ti2 = (Treeitem) ti.getParent().getParent();
            ti2.setDisabled(true);
        } catch (Exception e) {
            logger.error("", e);
        }
        this.tanimlaButton.setVisible(false);
        this.iadeButton.setVisible(false);
        this.temizleButton.setVisible(false);
        this.itirazEtButton.setVisible(false);
        this.buttonGroupBox.setVisible(false);
        this.sucTipiLbl.setVisible(false);
        this.sucTipleriCombobox.setVisible(false);
        this.sucTipiEkleButton.setVisible(false);
    }

    private void bilgileriAyarla() {

        //Redmine : 5591 Geregi  	Mesai Saaiti dışında açıklamaların Hakim ekranlarında görünmemesi
        boolean nobetOnayliKontrol = false;

        Long onaylayanId = 0L;
        String aciklama = "";
        ArrayList<MahkemeKararAtamaPojo> atamaList = null;

        if (personel.getYetkiList().contains(IYMSabitler.MAHKEME_KARAR_TANIMLAMA)) { //Hakim icin
            atamaList = srv.mahkemeKararAtamaGetir(xmlEvrak.getId(), 2);
            if (atamaList != null && atamaList.size() > 0) {
                MahkemeKararAtamaPojo atama = atamaList.get(0);
                if (atama.getDurum().equals("A") && atama.getGonderilenId().intValue() == (int) personel.getIymId()) {
                    onaylayanId = atama.getGonderenId().longValue();
                    aciklama = atama.getAciklama();
                    nobetOnayliKontrol = true;
                }
            }
        }


        if(!nobetOnayliKontrol){
            atamaList = srv.mahkemeKararAtamaGetir(this.xmlEvrak.getId(), 1);
            if(atamaList != null && atamaList.size() > 0) {
                MahkemeKararAtamaPojo atama = atamaList.get(0);
                onaylayanId = atama.getGonderenId().longValue();
                aciklama = atama.getAciklama();
            }
        }

        String onaylayanAdi = iymsrv.kullaniciAdiGetir(onaylayanId);
        if(!TemelIslemler.isNullOrEmpty(onaylayanAdi)) {
            evrakOnaylayanLbl.setValue(evrakOnaylayanLbl.getValue() + " Onaylayan:" + onaylayanAdi);
            evrakAciklamaLbl.setValue(evrakAciklamaLbl.getValue() + " Onaylayan Açıklama:" + aciklama);
        }

        /*
        for (MahkemeKararAtamaPojo atamaPojo : atamaList) {
            evrakOnaylayanLbl.setValue(evrakOnaylayanLbl.getValue() + " Onaylayan:" + iymsrv.kullaniciAdiGetir(onaylayanId));
            evrakAciklamaLbl.setValue(evrakAciklamaLbl.getValue() + " Onaylayan Açıklama:" + aciklama);
        }
        */

        evrakAciklamaLbl2.setValue(evrakAciklamaLbl2.getValue() + " Kolluk Açıklama : " + TemelIslemler.nullTemizle(this.xmlEvrak.getAciklama()));
        kurumAdiLbl.setValue(this.xmlEvrak.getEvrakGeldigiKurumAdi());
        evrakTarihiLbl.setValue(this.xmlEvrak.getEvrakTarihi());
        evrakIliLbl.setValue(this.xmlEvrak.getGelenIl());
        evrakNoLbl.setValue(this.xmlEvrak.getEvrakNo());
        evrakSiraNoLbl.setValue(this.xmlEvrak.getEvrakSiraNo());

        if (this.mahkemeKararIslem != null) {
            gorunumAyarla(this.mahkemeKararIslem.getMahkemeKararTuru());
            kararTipLbl.setValue(this.mahkemeKararIslem.getIslemTipi());
            kararNoLbl.setValue(this.mahkemeKararIslem.getMahkemeKararNo());
            sorusturmaNoLbl.setValue(this.mahkemeKararIslem.getSorusturmaNo());
            mahkemeIliLbl.setValue(this.mahkemeKararIslem.getMahkemeIli());
            mahkemeAdiLbl.setValue(this.mahkemeKararIslem.getMahkemeAdi());
            baslangicTarihiLbl.setValue(this.mahkemeKararIslem
                    .getMahkemeKararBaslamaTarihi());
            bitisTarihiLbl.setValue(this.mahkemeKararIslem
                    .getMahkemeKararBitisTarihi());
            if (this.mahkemeKararIslem.getMahkemeKararTuru() == MAHKEME_KARAR_ISLEM_TURU.TANIMLAMA
                    || this.mahkemeKararIslem.getMahkemeKararTuru() == MAHKEME_KARAR_ISLEM_TURU.UZATMA
                    || this.mahkemeKararIslem.getMahkemeKararTuru() == MAHKEME_KARAR_ISLEM_TURU.SONLANDIRMA) {
                hedeflerListBind(this.mahkemeKararIslem.getMahkemeKararTuru(),
                        this.mahkemeKararIslem.getId());
            } else if (this.mahkemeKararIslem.getMahkemeKararTuru() == MAHKEME_KARAR_ISLEM_TURU.AIDIYAT_DEGISTIRME) {
                aidiyatEkleCikarListBind(this.mahkemeKararIslem.getId());
            } else if (this.mahkemeKararIslem.getMahkemeKararTuru() == MAHKEME_KARAR_ISLEM_TURU.MAHKEME_KODU_DEGISTIRME) {
                mahkemeKoduDegistirListBind(this.mahkemeKararIslem.getId());
            } else if (this.mahkemeKararIslem.getMahkemeKararTuru() == MAHKEME_KARAR_ISLEM_TURU.HEDEF_ADSOYAD_DEGISTIRME) {
                hedefAdSoyadDegistirListBind(this.mahkemeKararIslem.getId());
            } else if (this.mahkemeKararIslem.getMahkemeKararTuru() == MAHKEME_KARAR_ISLEM_TURU.GENEL_KARAR) {
                genelKararListBind(this.mahkemeKararIslem.getId());
            }
        } else if (this.mahkemeKarar != null) {
            gorunumAyarla(this.mahkemeKarar.getMahkemeKararTuru());
            kararTipLbl.setValue(this.mahkemeKarar.getIslemTipi());
            kararNoLbl.setValue(this.mahkemeKarar.getMahkemeKararNo());
            sorusturmaNoLbl.setValue(this.mahkemeKarar.getSorusturmaNo());
            mahkemeIliLbl.setValue(this.mahkemeKarar.getMahkemeIli());
            mahkemeAdiLbl.setValue(this.mahkemeKarar.getMahkemeAdi());
            baslangicTarihiLbl.setValue(this.mahkemeKarar
                    .getMahkemeKararBaslamaTarihi());
            bitisTarihiLbl.setValue(this.mahkemeKarar
                    .getMahkemeKararBitisTarihi());
            if (this.mahkemeKarar.getMahkemeKararTuru() == MAHKEME_KARAR_ISLEM_TURU.TANIMLAMA
                    || this.mahkemeKarar.getMahkemeKararTuru() == MAHKEME_KARAR_ISLEM_TURU.UZATMA
                    || this.mahkemeKarar.getMahkemeKararTuru() == MAHKEME_KARAR_ISLEM_TURU.SONLANDIRMA) {
                hedeflerListBind(this.mahkemeKarar.getMahkemeKararTuru(),
                        this.mahkemeKarar.getId());

            } else if (this.mahkemeKarar.getMahkemeKararTuru() == MAHKEME_KARAR_ISLEM_TURU.AIDIYAT_DEGISTIRME) {
                aidiyatEkleCikarListBind(this.mahkemeKarar.getId());
            } else if (this.mahkemeKarar.getMahkemeKararTuru() == MAHKEME_KARAR_ISLEM_TURU.MAHKEME_KODU_DEGISTIRME) {
                mahkemeKoduDegistirListBind(/*this.mahkemeKararIslem.getId()*/null);//mahkemeKararIslem=null olduğu için kapatıldı
            } else if (this.mahkemeKarar.getMahkemeKararTuru() == MAHKEME_KARAR_ISLEM_TURU.HEDEF_ADSOYAD_DEGISTIRME) {
                hedefAdSoyadDegistirListBind(/*this.mahkemeKararIslem.getId()*/null);//mahkemeKararIslem=null olduğu için kapatıldı
            } else if (this.mahkemeKarar.getMahkemeKararTuru() == MAHKEME_KARAR_ISLEM_TURU.GENEL_KARAR) {
                genelKararListBind(/*this.mahkemeKararIslem.getId()*/null);//mahkemeKararIslem=null olduğu için kapatıldı
            }
        }
        Iymservice srv2 = ServiceManager.getIymservice();
        List<IymFilePojo> dosyaList = srv2.evrakDosyaGetir2(xmlEvrak.getId());
        IymFilePojo dosya = null;

        if (dosyaList.size() > 0) {

            for (int index = 0; index < dosyaList.size(); ++index) {

                dosya = dosyaList.get(index);

                if (dosya != null) {

                    String ROOT_PATH_FILES = IYMSabitler.ROOT_PATH_FILES;
                    File f = new File(ROOT_PATH_FILES + dosya.getFileName()
                            + ".pdf");
                    if (!f.exists()) {
                        try {
                            resimToPdfDonustur(xmlEvrak.getId());
                        } catch (IOException e) {
                            logger.error("resimToPdfDonustur hatasi.", e);
                        }
                    }

                    try {
                        logger.trace(ROOT_PATH_FILES + dosya.getFileName() + ".pdf");
                        dosyaGorFrame.setAlign("top");
                        //dosyaGorFrame.getContent().getByteData()
                        dosyaGorFrame.setContent(new AMedia(dosya.getFileName()
                                + ".pdf", null, "attach;application/pdf",
                                IymIslemler.dosyaBindEt(ROOT_PATH_FILES + dosya.getFileName() + ".pdf")));
                        IymIslemler.IymLogger(personel, Sessions.getCurrent(), "Evrak Gör", "Evrak Id : " + xmlEvrak.getId(), this, null);
                    } catch (Exception ex) {
                        logger.error("Evrak Gor hata. dosya:" + ROOT_PATH_FILES + dosya.getFileName(), ex);
                        IymIslemler.IymLogger(personel, Sessions.getCurrent(), "Evrak Gör", "Evrak Id : " + xmlEvrak.getId(), this, ex.getMessage());
                    }
                }
            }
        }
        /*
         * else this.mesajBoxGosterHata("PDF DOSYASI BULUNAMADI");
         */
    }

    public static String resimToPdfDonustur(Long evrakId) throws IOException {

        Rectangle pageSize;
        RandomAccessFileOrArray myFile;
        String fileName = "";
        logger.debug("RESIM TO PDF GIRIS : " + evrakId);
        Iymservice srv = ServiceManager.getIymservice();
        List<IymFilePojo> fList = srv.evrakDosyaGetir(evrakId);
        if (fList != null && fList.size() > 0) {
            com.lowagie.text.Document TifftoPDF = new com.lowagie.text.Document();
            com.lowagie.text.Document jpgToPdf = new com.lowagie.text.Document();
            com.lowagie.text.Document jpgToPdfTest = new com.lowagie.text.Document();
            fileName = IYMSabitler.ROOT_PATH_FILES + fList.get(0).getFileName();

            int spos = fileName.lastIndexOf(".");
            String uzanti = fileName.substring(spos + 1);
            if (uzanti.equalsIgnoreCase("tiff") || uzanti.equalsIgnoreCase("tif")) {
                try {
                    logger.debug("TIFF  : " + fileName);
                    for (IymFilePojo fi : fList) {
                        String ekName = IYMSabitler.ROOT_PATH_FILES + fi.getFileName();
                        logger.debug("EK KONTROL  : " + ekName);
                        File ek = new File(ekName);
                        if (!ek.exists()) {
                            logger.debug("EK YOK  : " + ekName);
                            return fileName;
                        }
                        myFile = new RandomAccessFileOrArray(ekName);

                        int numberOfPages = TiffImage.getNumberOfPages(myFile);
                        if (numberOfPages == 0) {
                            return fileName;
                        }

                        PdfWriter.getInstance(TifftoPDF, new FileOutputStream(fileName
                                + ".pdf"));
                        /* Birinci sayfanin boyutu */
                        com.lowagie.text.Image image1 = TiffImage.getTiffImage(myFile, 1);
                        pageSize = new Rectangle(image1.getPlainWidth(),
                                image1.getPlainHeight());
                        TifftoPDF.setPageSize(pageSize);
                        myFile.close();
                        TifftoPDF.open();
                        for (int i = 1; i <= numberOfPages; i++) {
                            myFile = new RandomAccessFileOrArray(ekName);
                            com.lowagie.text.Image image = (TiffImage.getTiffImage(myFile, i));
                            /* Diger Sayfalarin boyutu */
                            pageSize = new Rectangle(image.getPlainWidth(),
                                    image.getPlainHeight());
                            TifftoPDF.setPageSize(pageSize);

                            TifftoPDF.add(image);
                            myFile.close();
                        }
                    }
                    logger.debug("TIFF  BITTI: " + fileName);
                } catch (Exception i1) {
                    logger.error("fileName:{}", fileName, i1);
                    TifftoPDF.close();
                    return fileName;
                }
            } else if (uzanti.equalsIgnoreCase("jpeg") || uzanti.equalsIgnoreCase("jpg")) {
                try {
                    logger.debug("JPEG  : " + fileName);
                    jpgToPdf.setMargins(5, 5, 5, 5);
                    PdfWriter.getInstance(jpgToPdf, new FileOutputStream(fileName
                            + ".pdf"));
                    jpgToPdf.open();
                    for (IymFilePojo fi : fList) {
                        String ekName = IYMSabitler.ROOT_PATH_FILES + fi.getFileName();
                        logger.debug("EK KONTROL  : " + ekName);
                        File ek = new File(ekName);
                        if (!ek.exists()) {
                            logger.error("EK YOK  : " + ekName);
                            return fileName;
                        }
                        com.lowagie.text.Image jpeg = com.lowagie.text.Image.getInstance(ekName);
                        jpeg.scaleToFit(585, 825);
                        logger.debug(jpeg.getPlainWidth() + "  -  " + jpeg.getPlainHeight());
                        pageSize = new Rectangle(jpeg.getPlainWidth(), jpeg.getPlainHeight());
                        jpgToPdf.setPageSize(pageSize);
                        jpgToPdf.add(jpeg);
                    }


                    logger.debug("JPEG BITTI: " + fileName);

                } catch (DocumentException e) {
                    logger.error("", e);
                    jpgToPdf.close();
                    jpgToPdfTest.close();

                }
            }
            TifftoPDF.close();
            jpgToPdf.close();
            jpgToPdfTest.close();
            logger.debug("RESIM TO PDF BITTI: " + evrakId);
        }
        if (fileName.length() > 0)
            return fileName + ".pdf";
        return fileName;
    }

    private void genelKararListBind(Long id) {
        this.genelKararTab.setVisible(true);
        this.genelKararTabPanel.setVisible(true);
        this.hedefAidiyatTablar.setSelectedPanel(this.genelKararTabPanel);
        IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
        List<GenelKararPojo> genelKarar = srv.genelKararListGetir(this.mahkemeKararIslem.getEvrakId());

        ListModelList lm = new ListModelList(genelKarar);
        genelKararList.setModel(lm);
    }

    private void hedefAdSoyadDegistirListBind(Long id) {
        this.hedefAdSoyadDegistirTab.setVisible(true);
        this.hedefAdSoyadDegistirTabPanel.setVisible(true);
        this.hedefAidiyatTablar
                .setSelectedPanel(this.hedefAdSoyadDegistirTabPanel);
        IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
        List<HedeflerDetayIslemPojo> detayKarar = null;
        if (mahkemeKarar != null) {
            detayKarar = srv.hedeflerDetayListGetir(this.mahkemeKarar.getId());
        } else {
            detayKarar = srv.hedeflerDetayIslemListGetir(this.mahkemeKararIslem.getId());
        }

        ListModelList lm = new ListModelList(detayKarar);
        hedefAdSoyadDegistirList.setModel(lm);
    }

    private void mahkemeKoduDegistirListBind(Long id) {
        this.mahkemeKoduDegistirTab.setVisible(true);
        this.mahkemeKoduDegistirTabPanel.setVisible(true);
        this.hedefAidiyatTablar
                .setSelectedPanel(this.mahkemeKoduDegistirTabPanel);
        IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
        List<MahkemeKoduDegistirPojo> detayKarar = null;
        if (mahkemeKarar != null) {
            detayKarar = srv.mahkemeKararDegistirListGetir(this.mahkemeKarar.getId());
        } else {
            detayKarar = srv.mahkemeKararDegistirIslemListGetir(this.mahkemeKararIslem.getId());
        }

        ListModelList lm = new ListModelList(detayKarar);
        mahkemeKoduDegistirList.setModel(lm);
    }

    private void aidiyatEkleCikarListBind(Long id) {
        this.aidiyatEkleCikarTab.setVisible(true);
        this.aidiyatEkleCikarTabPanel.setVisible(true);
        this.hedefAidiyatTablar.setSelectedPanel(this.aidiyatEkleCikarTabPanel);
        IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
        List<MahkemeAidiyatDetayIslemPojo> aidiyatlar = null;
        if (mahkemeKarar != null) {
            aidiyatlar = srv.aidiyatDetayListGetir(this.mahkemeKarar.getId());
        } else {
            aidiyatlar = srv.aidiyatDetayIslemListGetir(this.mahkemeKararIslem.getId());
        }

        ListModelList lm = new ListModelList(aidiyatlar);
        aidiyatEkleCikarList.setModel(lm);
    }

    private void gorunumAyarla(MAHKEME_KARAR_ISLEM_TURU tur) {
        if (tur == MAHKEME_KARAR_ISLEM_TURU.TANIMLAMA)
            kararGroupbox.setSclass("profile-tanimlama");
        else if (tur == MAHKEME_KARAR_ISLEM_TURU.UZATMA)
            kararGroupbox.setSclass("profile-uzatma");
        else if (tur == MAHKEME_KARAR_ISLEM_TURU.SONLANDIRMA)
            kararGroupbox.setSclass("profile-sonlandirma");
        else if (tur == MAHKEME_KARAR_ISLEM_TURU.AIDIYAT_DEGISTIRME)
            kararGroupbox.setSclass("profile-aidiyat");
        else if (tur == MAHKEME_KARAR_ISLEM_TURU.MAHKEME_KODU_DEGISTIRME)
            kararGroupbox.setSclass("profile-mahkemekodu");
        else if (tur == MAHKEME_KARAR_ISLEM_TURU.HEDEF_ADSOYAD_DEGISTIRME)
            kararGroupbox.setSclass("profile-adsoyad");
        else if (tur == MAHKEME_KARAR_ISLEM_TURU.GENEL_KARAR)
            kararGroupbox.setSclass("profile-genelkarar");
    }

    @Override
    public void temizle() {

        PersonelService srvPer = ServiceManager.getPersonelService();
        boolean hukukNobetci = srvPer.hukukNobetciMi(personel.getPersonelId());
        IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();

        boolean temizlendi = false;
        Long evrakId = xmlEvrak.getId();

        if (srv.evrakIslenmisMi(evrakId)) {
            mesajBoxGosterUyari("Tanımlanmış evrak üzerinde işlem yapılamaz.");
            return;
        }

        if(srv.isEvrakKismiIade(evrakId)) {

            temizlendi = srv.kismiEvrakKontrolGrubunaIade(evrakId);

            if(!temizlendi){
                this.mesajBoxGosterHata("Kontrol grubuna gönderme hatası oluştu.");
            }

            IymIslemler.IymLogger(personel, Sessions.getCurrent(),
                    "Evrak Temizlendi", "Evrak Id : " + evrakId, this,null);

        }
        else{
            if (!srv.mahkemeKararIslemTemizle(evrakId, mahkemeKararTanimlamaMi(),
                    hukukNobetci, personel.getIymId(), aciklamaTxt.getValue(), mahkemeKararTanimlamaMi())) {
                IymIslemler.IymLogger(personel, Sessions.getCurrent(),
                        "Evrak Temizlenemedi", "Evrak Id : " + evrakId
                                + " İŞLEMLER GERİ ALINAMADI!!! ", this, null);
                this.mesajBoxGosterHata("İŞLEMLER GERİ ALINAMADI!!!");
                temizlendi = false;
            }
            else{
                temizlendi = true;
            }

        }

       if(temizlendi) {
            IymIslemler.IymLogger(personel, Sessions.getCurrent(),
                    "Evrak Temizlendi", "Evrak Id : " + xmlEvrak.getId(), this,
                    null);

            mahkemeKararAnaSayfa.cikarOnaylananKararList(this.mahkemeKararIslem
                    .getId());

            try {
                Treerow tr = (Treerow) islenecekEvraklarTree.getSelectedItem()
                        .getChildren().get(0);
                tr.getChildren().get(0).getChildren().clear();

                //tr.setStyle("background-color:none;");
            } catch (Exception e) {
                //logger.error("", e);
            }

            Tabpanel mahkemeKararPanel = (Tabpanel) mahkemeKararAnaSayfa.getFellow("mahkemeKararAnaSayfaTabPanel");

            mahkemeKararAnaSayfa.islenecekEvraklarListBind();
            if (!hukukNobetci) {
                Tabbox mahkemeKararTablar = (Tabbox) mahkemeKararAnaSayfa.getFellow("mahkemeKararTablar");
                mahkemeKararTablar.setSelectedPanel(mahkemeKararPanel);
                this.detach();
            } else {
                tanimlaButton.setVisible(false);
                onaylaButton.setVisible(true);
            }

        }

    }

/*	public void tirnakAc() {

		Window win = new Window();
		win.setParent(this);

		String hedefNo = "905432020202";
		String kurumKod = "A";

		String parametre = "hedefNo=" + hedefNo + ";kurumKod=" + kurumKod;
		String encParametre = IymIslemler.encodeString(parametre);

		IymIslemler.IymLogger(personel, Sessions.getCurrent(),
				"Hedef Geçmiş Görüntülendi", "Evrak Id : " + xmlEvrak.getId()
						+ " Hedef No : " + hedefNo + " KurumKod : " + kurumKod,
				this, null);

		Include inc = new Include();
		inc.setParent(win);
		inc.setSrc("/forms/iym/mahkemeKarar/HedefAramaModal.zul?param="
				+ encParametre);

		win.setTitle(hedefNo + " Nolu Hedefin Sorgu Listesi");
		win.setWidth("95%");
		win.setHeight("75%");
		win.setClosable(true);
		win.doModal();

	}*/

    public void tamaminiGoster() {

        Rows r = hedeflerList.getRows();
        for (int i = 0; i < r.getChildren().size(); i++) {
            if (((Row) r.getChildren().get(i)).getDetailChild().isOpen())
                ((Row) r.getChildren().get(i)).getDetailChild().setOpen(false);
            else
                ((Row) r.getChildren().get(i)).getDetailChild().setOpen(true);
        }
    }

    public void tamaminiYenile() {

        String msgStr = null;
        Rows r = hedeflerList.getRows();

        IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();

        if (r != null && r.getChildren() != null) {
            for (int i = 0; i < r.getChildren().size(); i++) {

                Row row = (Row) r.getChildren().get(i);
                HedeflerIslemPojo hedefler = row.getValue();


                String hedefTipi = null;
                try {
                    hedefTipi = srv.getHedefTipi(srv.hedefTipiIslemdenGetir(hedefler.getId()));
                } catch (Exception ex) {
                    //logger.error("", ex);
                    hedefTipi = srv.getHedefTipi(srv.hedefTipiGetir(hedefler.getId()));
                }

                final String hTipi = hedefTipi;

                String gercekHedefTipi = srv.getHedefTipi(hTipi);

                try {

                    if (gercekHedefTipi.equalsIgnoreCase(HEDEF_TIPLERI.GSM.getHedefKodu()) ||
                            gercekHedefTipi.equalsIgnoreCase(HEDEF_TIPLERI.SABIT.getHedefKodu()) ||
                            gercekHedefTipi.equalsIgnoreCase(HEDEF_TIPLERI.UYDU.getHedefKodu()) ||
                            gercekHedefTipi.equalsIgnoreCase(HEDEF_TIPLERI.YURT_DISI.getHedefKodu()) ||
                            gercekHedefTipi.equalsIgnoreCase(HEDEF_TIPLERI.UMTH_MSISDN.getHedefKodu()) ||
                            gercekHedefTipi.equalsIgnoreCase(HEDEF_TIPLERI.GPRS_GSM.getHedefKodu()) ||
                            gercekHedefTipi.equalsIgnoreCase(HEDEF_TIPLERI.GSM_YER_TESPITI.getHedefKodu()) ||
                            gercekHedefTipi.equalsIgnoreCase(HEDEF_TIPLERI.GPRS_YURT_DISI.getHedefKodu())) {
                        String hedef = hedefler.getHedefNo();
                        AboneBilgi bilgi = null;
                        if (hedefler.getHedefNo().substring(0, 2).equalsIgnoreCase("90"))
                            hedef = hedefler.getHedefNo().substring(2);

                        //Redmine : 3696 Geregi. Acik sorgudan gelen abone sorgu bilgileri, canli118'ten gelmiyor. Method degistiridi. Acik sorgu ile benzer yapildi.
                        bilgi = TemelIslemler.aboneBul(hedef); //canli118Bul(hedef);
                        IymIslemler.IymLogger(personel, Sessions.getCurrent(), "Hedef 118 Bilgileri Güncellendi", " Hedef Id : " + hedefler.getId(), this, null);

                        if (bilgi != null) {
                            hedefler.setHedef118Adi(TemelIslemler.nullTemizle(bilgi.getAd()));
                            hedefler.setHedef118Soyadi(TemelIslemler.nullTemizle(bilgi.getSoyad()));
                            hedefler.setHedef118Adres(TemelIslemler.nullTemizle(bilgi.getAdres()));
                            if (!srv.hedeflerIslemGuncelle(hedefler.getId(), hedefler.getHedef118Adi(), hedefler.getHedef118Soyadi(), hedefler.getHedef118Adres())) {
                                //Messagebox.show("Rehber Bilgileri Güncelleme Sırasında Hata ile Karşılaşıldı.");
                            } else {

                                Label l1 = (Label) row.getChildren().get(0).getChildren().get(0).getChildren().get(1).getChildren().get(0).getChildren().get(1);
                                Label l2 = (Label) row.getChildren().get(0).getChildren().get(0).getChildren().get(1).getChildren().get(0).getChildren().get(2);
                                l1.setValue(hedefler.getHedef118Adi() + " " + hedefler.getHedef118Soyadi());
                                l2.setValue(hedefler.getHedef118Adres());

                            }
                        }
                    }


                } catch (Exception ex) {
                    logger.error("", ex);
                    msgStr += ex.getMessage() + "\n";
                }

            } //end of for

        }

        if (msgStr != null && !msgStr.isEmpty()) {
            Messagebox.show(msgStr);
        }
    }


    private AboneBilgi canli118Bul(String hedefNo) {
        JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
        factory.setServiceClass(IYMFideEndpoint.class);
//		factory.setAddress("http://***********:8180/IYMFideReader/IYMOku?wsdl");
        factory.setAddress(PropertyHelper.getProperty("iym.wsdl.location"));

        IYMFideEndpoint client = (IYMFideEndpoint) factory.create();
        AboneBilgi b = client.canliAbone118(hedefNo, tarihFormatla(new Date()));

        return b;
    }

    private String tarihFormatla(Date d) {
        if (d == null) {
            return null;
        }
        DateFormat trDate = new SimpleDateFormat("yyyyMMdd");
        return trDate.format(d);
    }


    public Columnchildren getMahkemeKararChildren() {
        return mahkemeKararChildren;
    }

    public void setMahkemeKararChildren(Columnchildren mahkemeKararChildren) {
        this.mahkemeKararChildren = mahkemeKararChildren;
    }

    public Iframe getDosyaGorFrame() {
        return dosyaGorFrame;
    }

    public void setDosyaGorFrame(Iframe dosyaGorFrame) {
        this.dosyaGorFrame = dosyaGorFrame;
    }

    public Columnchildren getDosyaGorChildren() {
        return dosyaGorChildren;
    }

    public void setDosyaGorChildren(Columnchildren dosyaGorChildren) {
        this.dosyaGorChildren = dosyaGorChildren;
    }

    public XmlEvrakPojo getXmlEvrak() {
        return xmlEvrak;
    }

    public void setXmlEvrak(XmlEvrakPojo xmlEvrak) {
        this.xmlEvrak = xmlEvrak;
    }


    private boolean mahkemeKararOnaylamaMi() {
        try {
            if (personel != null && personel.getYetkiList() != null && personel.getYetkiList().contains(IYMSabitler.MAHKEME_KARAR_ONAYLAMA))
                return true;
        } catch (Exception ex) {
            logger.error("mahkemeKararOnaylamaMi : ", ex);
        }
        return false;
    }

    private boolean mahkemeKararTanimlamaMi() {
        try {
            if (personel != null && personel.getYetkiList() != null &&  personel.getYetkiList().contains(IYMSabitler.MAHKEME_KARAR_TANIMLAMA))
                return true;
        } catch (Exception ex) {
            logger.error("mahkemeKararTanimlamaMi : ", ex);
        }
        return false;
    }


    //Nobetci tarafindan tanimlanan evragin hakim tarafindan kontrolu ve onayi
    public void nobetciTanimliKontrolEdildi() {
        IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();


        String aciklama = aciklamaTxt.getText();

        Hatalar hatalar = srv.nobetTanimliEvragiKontrolEt(xmlEvrak, aciklama);
        ArrayList<HatalarPojo> hatalarListe = hatalar.hatalarListeGetir();
        if (!hatalarListe.isEmpty()) {
            String hatalarStr = "";
            for (HatalarPojo h : hatalarListe) {
                hatalarStr += h.getHataMesaji() + "   \n";
            }
            IymIslemler.IymLogger(personel, Sessions.getCurrent(),
                    "Evrak Tanımlanamadı", "Evrak Id : " + xmlEvrak.getId()
                            + " Hata : " + hatalarStr, this, null);
            this.mesajBoxGosterHata("Hata :  Evrak Tanımlanamadı! \n " + hatalarStr);
            return;
        }
		
		/*
		MahkemeKararAtamaPojo atama = new MahkemeKararAtamaPojo();
		atama.setEvrakId(xmlEvrak.getId());
		atama.setSeviye(2);
		
		if(!srv.mahkemeKararAtamaTanimla(atama))
		{
			this.mesajBoxGosterHata("Hata: Atama kaldırılamadı!");
		}
		
		*/

        IymIslemler.IymLogger(personel, Sessions.getCurrent(), "Nöbet Tanımlı Evrak Kontrol Edildi", "Evrak Id : " + xmlEvrak.getId(), this, null);
        //mahkemeKararAnaSayfa.cikarOnaylananKararList(this.mahkemeKarar.getId());
        //mahkemeKararAnaSayfa.cikarAcikEvrakList(this.xmlEvrak.getId());

        mahkemeKararAnaSayfa.nobetciTanimliEvraklariGoster();


        try {

            Treeitem ti = (Treeitem) islenecekEvraklarTree.getSelectedItem();
            ti.setDisabled(true);
            Treeitem ti2 = (Treeitem) ti.getParent().getParent();
            ti2.setDisabled(true);
        } catch (Exception e) {
            //logger.error("", e);
        }

        this.nobetciTanimliKontrolEdildiBtn.setVisible(false);
        this.tanimlaButton.setVisible(false);
        this.iadeButton.setVisible(false);
        this.temizleButton.setVisible(false);
        this.itirazEtButton.setVisible(false);
        this.buttonGroupBox.setVisible(false);
        this.sucTipiLbl.setVisible(false);
        this.sucTipleriCombobox.setVisible(false);
        this.sucTipiEkleButton.setVisible(false);
    }

    public void hedefIadeIslemleri(){
        try {
            //TODO : evrak uzerinden gitme. mahkeme karar uzerinden git.
            //iym tarafindan listeden kaldirilinca onaylandi olarak duruyor
            boolean kismiIade = srv.isEvrakKismiIade(this.xmlEvrak.getId());

            Toolbarbutton hedefIadeEtBtn = (Toolbarbutton) getFellow("hedefIadeEtBtn");
            ;

            if (mahkemeKararTanimlamaMi()) {
                hedefIadeEtBtn.setVisible(true);
                hedefIadeEtBtn.setLabel("İade Bilgileri");

                hedefIadeEtBtn.removeEventListener("onClick", kısmiIadeEventEvent);
                hedefIadeEtBtn.addEventListener("onClick", kısmiIadeBilgileriEvent);

                hedefIadeEtBtn.setVisible(kismiIade);

            } else if (mahkemeKararOnaylamaMi()) {
                if (kismiIade) {
                    hedefIadeEtBtn.setVisible(true);
                    hedefIadeEtBtn.setLabel("İade Bilgileri");
                    hedefIadeEtBtn.getClientEvents();

                    hedefIadeEtBtn.addEventListener("onClick", kısmiIadeBilgileriEvent);
                    hedefIadeEtBtn.removeEventListener("onClick", kısmiIadeEventEvent);

                } else {
                    hedefIadeEtBtn.setVisible(true);
                    hedefIadeEtBtn.setLabel("Hedef İade Et");

                    hedefIadeEtBtn.removeEventListener("onClick", kısmiIadeBilgileriEvent);
                    hedefIadeEtBtn.addEventListener("onClick", kısmiIadeEventEvent);

                }
            } else {
                hedefIadeEtBtn.setVisible(false);
            }
        }catch(Exception ex){
            logger.error("KISMI_IADE : ", ex);
            mesajBoxGosterHata("Hata oluştu : " + ex.getMessage());
        }

    }

    public void hedefIadeBilgileriniGoster(){
        Iymservice iym = ServiceManager.getIymservice();
        try {
            EvrakKayitPojo evrak = iym.getEvrakById(this.xmlEvrak.getId());
            if (evrak != null) {
                mesajBoxGosterInformation("İade Bilgisi :\n" + evrak.getAciklama());
            } else {
                mesajBoxGosterHata("Evrak bulunamadı");
            }

            IymIslemler.IymLogger(personel, Sessions.getCurrent(), "Hedef İade Bilgisi Gösterildi. ", "Evrak Id : " + xmlEvrak.getId(), this, null);

        }catch (Exception ex){
            mesajBoxGosterHata("Hata oluştu : " + ex.getMessage());
        }

    }

    public void kismiIadeLogEkle(String logAciklama){
        if(personel != null){
            LoggerService.getIymKubikLog().info(personel.getKullaniciAd() + "|" + TemelIslemler.TurkZaman(new java.util.Date())  + "|" + logAciklama);
        }
    }
    
    private boolean isContain(List<HedeflerIslemPojo> list, String hedefNo){
        for(HedeflerIslemPojo hedef : list){
            if(hedef.getHedefNo().equals(hedefNo)){
                return true;
            }
        }

        return false;
    }
    public void seciliHedefleriIadeEt() {
        try {
            if (!mahkemeKararOnaylamaMi()) {
                mesajBoxGosterHata("Hedef iade işlemini tanımlamacı yapabilir.");
                return;
            }

            kismiIadeLogEkle("Iade hedef giriş. EvrakId: " + xmlEvrak.getId() != null ? xmlEvrak.getId().toString() : "");

            String msgStr = null;
            Rows r = hedeflerList.getRows();
            List<String> seciliHedefler = new ArrayList<String>();

            IymServiceMahkemeKarar srv = ServiceManager.getIymServiceMahkemeKarar();
            Long mahkemeKararId = 0L;
            if (r != null && r.getChildren() != null) {
                for (int i = 0; i < r.getChildren().size(); i++) {
                    Row row = (Row) r.getChildren().get(i);
                    HedeflerIslemPojo hedef = row.getValue();
                    mahkemeKararId = hedef.getMahkemeKararId();
                    if (!TemelIslemler.isNullOrEmpty(hedef.getDurumu()) && hedef.getDurumu().equals(IYMSabitler.HEDEF_IADE)) {
                        if(!seciliHedefler.contains(hedef.getHedefNo())) {
                            seciliHedefler.add(hedef.getHedefNo());
                        }
                    }
                } //end of for
            }

            if (seciliHedefler.size() < 1) {
                Messagebox.show("İade edilecek hedefleri seçiniz.");
                return;
            }

            new XmlEvrakHedefIadeEt(this, mahkemeKararId, seciliHedefler, this.xmlEvrak).iadeEt();


        } catch (Exception ex) {

        }
    }






}
