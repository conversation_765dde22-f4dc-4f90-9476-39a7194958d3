package iym.common.service.db.mk;

import iym.common.model.entity.iym.mk.MahkemeKararAidiyatIslem;
import iym.common.service.db.GenericDbService;

import java.util.List;

/**
 * Service interface for MahkemeKararAidiyatIslem entity
 */
public interface DbMahkemeKararAidiyatIslemService extends GenericDbService<MahkemeKararAidiyatIslem, Long> {

    List<MahkemeKararAidiyatIslem> findByMahkemeKararId(Long mahkemeKararId);

}
