# GitHub Workflow Updates Required for New Docker Configuration

## Overview
This document outlines the necessary updates to GitHub workflows to properly utilize the new Docker configuration files and docker-compose setup.

## Current Issues Identified

### 1. Dockerfile Usage
**Problem**: Workflows dynamically generate Dockerfiles instead of using the ones in the repository
**Location**: `docker-build.yml` - `Create Dockerfile for Backend` and `Create Dockerfile for Makos` steps
**Files Affected**:
- `backend/Dockerfile` (new, not used)
- `makos/Dockerfile` (new, not used)

### 2. Missing Docker Compose Integration
**Problem**: No workflow validates or tests the new docker-compose.yml setup
**New File**: `docker/docker-compose.yml` (not referenced in any workflow)

### 3. Application Properties Configuration
**Problem**: Workflows don't use the new `application-docker.properties` files
**New Files**:
- `backend/src/main/resources/application-docker.properties`
- `makos/src/main/resources/application-docker.properties`

## Required Updates

### Update 1: Modify docker-build.yml

#### Remove Dynamic Dockerfile Creation
Remove these sections from `docker-build.yml`:
```yaml
- name: Create Dockerfile for Backend
  run: |
    cat > backend/Dockerfile << 'EOF'
    # ... existing dynamic content ...
    EOF

- name: Create Dockerfile for Makos
  run: |
    cat > makos/Dockerfile << 'EOF'
    # ... existing dynamic content ...
    EOF
```

#### Update Build Commands
Update Maven build commands to include all necessary modules:
```yaml
# Backend build
- name: Build backend JAR
  run: mvn clean package -pl backend,common,database -am -DskipTests

# Makos build  
- name: Build makos JAR
  run: mvn clean package -pl makos,common,database -am -DskipTests
```

### Update 2: Add Docker Compose Validation Job

#### New Job: validate-compose
Add this new job to `docker-build.yml`:

```yaml
  validate-compose:
    name: Validate Docker Compose Setup
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Validate docker-compose.yml
        run: |
          cd docker
          docker-compose config
          
      - name: Build all services
        run: |
          cd docker
          docker-compose build
          
      - name: Test service startup
        run: |
          cd docker
          docker-compose up -d postgres oracle
          sleep 60
          docker-compose ps
          docker-compose down
```

### Update 3: Update Dockerfile Context Paths

#### Backend Dockerfile Context
Update the build context for backend:
```yaml
- name: Build and push Docker image
  uses: docker/build-push-action@v5
  with:
    context: .
    file: ./backend/Dockerfile
    push: ${{ github.event_name != 'pull_request' }}
    tags: ${{ steps.meta.outputs.tags }}
    labels: ${{ steps.meta.outputs.labels }}
    cache-from: type=gha
    cache-to: type=gha,mode=max
```

#### Makos Dockerfile Context
Update the build context for makos:
```yaml
- name: Build and push Docker image
  uses: docker/build-push-action@v5
  with:
    context: .
    file: ./makos/Dockerfile
    push: ${{ github.event_name != 'pull_request' }}
    tags: ${{ steps.meta.outputs.tags }}
    labels: ${{ steps.meta.outputs.labels }}
    cache-from: type=gha
    cache-to: type=gha,mode=max
```

### Update 4: Environment Variables for Docker Profiles

#### Add Spring Profile Configuration
Add environment variables to use Docker profiles:
```yaml
- name: Build backend JAR
  run: mvn clean package -pl backend,common,database -am -DskipTests
  env:
    SPRING_PROFILES_ACTIVE: docker
    
- name: Build makos JAR
  run: mvn clean package -pl makos,common,database -am -DskipTests
  env:
    SPRING_PROFILES_ACTIVE: docker
```

## Testing Strategy

### 1. Local Testing
Before implementing workflow changes:
```bash
# Test docker-compose locally
cd docker
docker-compose config
docker-compose build
docker-compose up -d
```

### 2. Workflow Testing
Test workflow changes in a feature branch:
```bash
# Create feature branch
git checkout -b feature/docker-workflow-updates

# Push to trigger workflows
git push origin feature/docker-workflow-updates
```

## Implementation Checklist

- [ ] Remove dynamic Dockerfile creation from `docker-build.yml`
- [ ] Update Maven build commands to include all modules
- [ ] Add Docker Compose validation job
- [ ] Update Dockerfile context paths
- [ ] Add Spring profile configuration
- [ ] Test changes in feature branch
- [ ] Update documentation

## Files to Review

### New Docker Configuration Files
- `docker/docker-compose.yml`
- `backend/Dockerfile`
- `makos/Dockerfile`
- `backend/src/main/resources/application-docker.properties`
- `makos/src/main/resources/application-docker.properties`

### Existing Workflow Files to Update
- `.github/workflows/docker-build.yml`
- `.github/workflows/ci.yml` (for integration testing)

## Notes
- Ensure all new files are committed before testing workflow changes
- Consider adding a separate workflow specifically for Docker Compose testing
- Update README files to document the new Docker setup
- Consider adding health checks to the docker-compose services

## Migration Strategy

1. **Phase 1**: Update docker-build.yml to use existing Dockerfiles
2. **Phase 2**: Add Docker Compose validation job
3. **Phase 3**: Test thoroughly in feature branch
4. **Phase 4**: Merge to main after validation
5. **Phase 5**: Monitor builds and adjust as needed

## Contact
For questions about these updates, please refer to the project maintainers or create an issue in the repository.