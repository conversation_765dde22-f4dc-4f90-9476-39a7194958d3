package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.HedeflerDetayIslem;
import iym.common.model.entity.iym.talep.HedeflerDetayTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HedeflerDetayIslemRepo extends JpaRepository<HedeflerDetayIslem, Long> {

    List<HedeflerDetayIslem> findByMahkemeKararTalepId(Long mahkemeKararTalepId);


}
