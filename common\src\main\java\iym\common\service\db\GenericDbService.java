package iym.common.service.db;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;

public interface GenericDbService<T, ID extends Serializable>{

     T save(T entity);
     void update(T entity);
     void delete(T entity);
     void deleteById(ID id);
     Optional<T> findById(ID id);
     List<T> findAll();
     Page<T> findAll(Pageable pageable);

}
