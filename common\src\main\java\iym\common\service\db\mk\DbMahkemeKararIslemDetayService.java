package iym.common.service.db.mk;

import iym.common.model.entity.iym.mk.DetayMahkemeKararIslem;
import iym.common.service.db.GenericDbService;

import java.util.List;


public interface DbMahkemeKararIslemDetayService extends GenericDbService<DetayMahkemeKararIslem, Long> {

    List<DetayMahkemeKararIslem> findByEvrakId(Long evrakId);

    List<DetayMahkemeKararIslem> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

}
