package iym.db.jpa.dao.talep;

import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for MahkemeKararTalep entity
 */
@Repository
public interface DetayMahkemeKararTalepRepo extends JpaRepository<DetayMahkemeKararTalep, Long> {

    List<DetayMahkemeKararTalep> findByEvrakId(Long evrakId);

    List<DetayMahkemeKararTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);


}
