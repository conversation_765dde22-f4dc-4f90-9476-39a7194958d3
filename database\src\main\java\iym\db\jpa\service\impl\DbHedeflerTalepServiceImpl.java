package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.talep.HedeflerTalep;
import iym.common.service.db.DbHedeflerTalepService;
import iym.db.jpa.dao.HedeflerTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * Service implementation for HedeflerTalep entity
 */
@Service
public class DbHedeflerTalepServiceImpl extends GenericDbServiceImpl<HedeflerTalep, Long> implements DbHedeflerTalepService {

    private final HedeflerTalepRepo hedeflerTalepRepo;

    @Autowired
    public DbHedeflerTalepServiceImpl(HedeflerTalepRepo repository) {
        super(repository);
        this.hedeflerTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedeflerTalep> findByMahkemeKararTalepId(Long mahkemeKararId) {
        return hedeflerTalepRepo.findByMahkemeKararTalepId(mahkemeKararId);
    }


    @Override
    @Transactional(readOnly = true)
    public List<HedeflerTalep> findByHedefNo(String hedefNo) {
        return hedeflerTalepRepo.findByHedefNo(hedefNo);
    }



    @Override
    @Transactional(readOnly = true)
    public List<HedeflerTalep> findByBaslamaTarihiBetween(Date startDate, Date endDate) {
        return hedeflerTalepRepo.findByBaslamaTarihiBetween(startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedeflerTalep> findByKayitTarihiBetween(Date startDate, Date endDate) {
        return hedeflerTalepRepo.findByKayitTarihiBetween(startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedeflerTalep> findByTanimlamaTarihiBetween(Date startDate, Date endDate) {
        return hedeflerTalepRepo.findByTanimlamaTarihiBetween(startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedeflerTalep> findByKapatmaTarihiBetween(Date startDate, Date endDate) {
        return hedeflerTalepRepo.findByKapatmaTarihiBetween(startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedeflerTalep> findByImhaTarihiBetween(Date startDate, Date endDate) {
        return hedeflerTalepRepo.findByImhaTarihiBetween(startDate, endDate);
    }


}
