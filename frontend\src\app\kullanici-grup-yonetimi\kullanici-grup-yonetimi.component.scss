:host {
  ::ng-deep .p-dialog .p-field {
    margin-bottom: 1rem;
  }

  // p-multiSelect içinde chip g<PERSON>rünümünü düzelt
  ::ng-deep .p-multiselect-label-container {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: flex-start !important;
    white-space: normal !important;
    min-height: 3.2rem;
    gap: 0.25rem;
  }

  ::ng-deep .p-multiselect-token {
    margin-bottom: 0.25rem;
    max-width: 100%;
  }

  // Çok sayıda chip varsa container'a yükseklik ver (opsiyonel)
  ::ng-deep .p-multiselect {
    min-height: 4rem;
  }

  // Dropdowndan taşmaları engelle (gerekirse)
  ::ng-deep .p-multiselect-items-wrapper {
    max-height: 300px;
    overflow-y: auto;
  }
}
