package iym.db.jpa.dao.sorgu;

import iym.common.model.entity.iym.sorgu.MahkemeKararSorguInfo;
import iym.common.model.entity.iym.sorgu.MahkemeKararSorguParam;
import iym.common.model.entity.iym.sorgu.MahkemeKararTalepSorguInfo;
import iym.common.model.entity.iym.sorgu.MahkemeKararTalepSorguParam;
import org.springframework.stereotype.Repository;

import java.util.List;


public interface MahkemeKararRepoDynamicQueries {

    List<MahkemeKararSorguInfo> mahkemeKararSorgu(String kurumKodu, MahkemeKararSorguParam sorguParam);

    List<MahkemeKararSorguInfo> islenecekKarar<PERSON>istesi(String kurumKodu);
}