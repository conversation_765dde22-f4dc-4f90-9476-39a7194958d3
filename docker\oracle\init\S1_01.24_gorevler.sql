-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create GOREVLER2 table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'GOREVLER2';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.GOREVLER2 (
      GOREV VARCHAR2(75 BYTE) NOT NULL,
      GOREV_KODU NUMBER NOT NULL,
      GOREV_IMZA_ADI VARCHAR2(100 BYTE),
      IMZA_YETKI CHAR(1 BYTE),
      SILINDI NUMBER,
      GOREV_KODU2 VARCHAR2(100 BYTE),
      GOREV_TIPI VARCHAR2(100 BYTE),
      ONCELIK NUMBER,
      BASLAMA_TARIHI DATE,
      BITIS_TARIHI DATE,
      CONSTRAINT GOREVLER2Y1_PRM PRIMARY KEY (GOREV, GOREV_KODU) ENABLE
    )';
    
    -- Create index
    EXECUTE IMMEDIATE 'CREATE INDEX iym.I_GOREVLER2Y_GK ON iym.GOREVLER2 (GOREV_KODU ASC)';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.GOREVLER2;
  IF row_count = 0 THEN
    -- Sample data 1
    INSERT INTO iym.GOREVLER2 (
      GOREV, GOREV_KODU, GOREV_IMZA_ADI, IMZA_YETKI, SILINDI, 
      GOREV_KODU2, GOREV_TIPI, ONCELIK, BASLAMA_TARIHI, BITIS_TARIHI
    ) VALUES (
      'Yönetici', 1, 'Yönetici İmza', 'E', 0,
      'YON', 'Yönetim', 1, TO_DATE('2023-01-01', 'YYYY-MM-DD'), TO_DATE('2025-12-31', 'YYYY-MM-DD')
    );
    
    -- Sample data 2
    INSERT INTO iym.GOREVLER2 (
      GOREV, GOREV_KODU, GOREV_IMZA_ADI, IMZA_YETKI, SILINDI, 
      GOREV_KODU2, GOREV_TIPI, ONCELIK, BASLAMA_TARIHI, BITIS_TARIHI
    ) VALUES (
      'Uzman', 2, 'Uzman İmza', 'H', 0,
      'UZM', 'Teknik', 2, TO_DATE('2023-01-01', 'YYYY-MM-DD'), TO_DATE('2025-12-31', 'YYYY-MM-DD')
    );
    
    -- Sample data 3
    INSERT INTO iym.GOREVLER2 (
      GOREV, GOREV_KODU, GOREV_IMZA_ADI, IMZA_YETKI, SILINDI, 
      GOREV_KODU2, GOREV_TIPI, ONCELIK, BASLAMA_TARIHI, BITIS_TARIHI
    ) VALUES (
      'Memur', 3, 'Memur İmza', 'H', 0,
      'MEM', 'İdari', 3, TO_DATE('2023-01-01', 'YYYY-MM-DD'), TO_DATE('2025-12-31', 'YYYY-MM-DD')
    );
  END IF;
END;
/

COMMIT;
