package iym.db.jpa.dao;

import iym.common.model.entity.iym.EvrakMahkemeKararIslem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for EvrakMahkemeKararIslem entity
 */
@Repository
public interface EvrakMahkemeKararIslemRepo extends JpaRepository<EvrakMahkemeKararIslem, Long> {

    List<EvrakMahkemeKararIslem> findByKurum(String kurum);
    

    List<EvrakMahkemeKararIslem> findByKurumAndSeviye(String kurum, String seviye);
}
