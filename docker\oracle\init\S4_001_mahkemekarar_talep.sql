-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MAHKEME_KARAR_TALEP if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = '<PERSON><PERSON><PERSON><PERSON>_KARAR_TALEP_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAHKEME_KARAR_TALEP_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create MAHKEME_KARAR_TALEP table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_KARAR_TALEP';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_KARAR_TALEP (
      ID NUMBER NOT NULL,
      EVR<PERSON>K_ID NUMBER NOT NULL,
      <PERSON><PERSON><PERSON>ANICI_ID NUMBER NOT NULL,
      K<PERSON><PERSON><PERSON>_TARIHI DATE NOT NULL,
      DURUM VARCHAR2(20 BYTE),
      HUKUK_BIRIM VARCHAR2(50 BYTE),
      KARAR_TIP VARCHAR2(20 BYTE),
      MAH_KARAR_BAS_TAR DATE,
      MAH_KARAR_BITIS_TAR DATE,
      MAHKEME_ADI VARCHAR2(250 BYTE),
      MAHKEME_KARAR_NO VARCHAR2(50 BYTE),
      MAHKEME_ILI VARCHAR2(4 BYTE) NOT NULL,
      ACIKLAMA VARCHAR2(500 BYTE),
      HAKIM_SICIL_NO VARCHAR2(20 BYTE),
      SORUSTURMA_NO VARCHAR2(50 BYTE),
      GERCEK_MAH_ID NUMBER,
      MAHKEME_KODU VARCHAR2(10 BYTE),
      CONSTRAINT MAHKEME_KARAR_TALEP_ID_IDX PRIMARY KEY (ID) ENABLE
    )';
    
    -- Create indexes
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAHKEME_KAR_TALEP_EVRAK_ID_IDX ON iym.MAHKEME_KARAR_TALEP (EVRAK_ID ASC)';
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAHKEME_KAR_TALEP_UNIQ ON iym.MAHKEME_KARAR_TALEP (MAHKEME_KARAR_NO ASC, MAHKEME_ADI ASC, MAHKEME_ILI ASC)';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.MAHKEME_KARAR_TALEP;
  IF row_count = 0 THEN
    -- Make sure we have evrak and users in the respective tables
    DECLARE
      evrak_count NUMBER;
      user_count NUMBER;
    BEGIN
      SELECT COUNT(*) INTO evrak_count FROM iym.EVRAK_KAYIT;
      SELECT COUNT(*) INTO user_count FROM iym.KULLANICILAR;
      
      IF evrak_count > 0 AND user_count > 0 THEN
        -- Get the ID of the admin user
        DECLARE
          admin_id NUMBER;
          evrak_id1 NUMBER;
          evrak_id2 NUMBER;
          evrak_id3 NUMBER;
        BEGIN
          SELECT ID INTO admin_id FROM iym.KULLANICILAR WHERE KULLANICI_ADI = 'admin';
          
          -- Get the ID of the first evrak
          SELECT ID INTO evrak_id1 FROM iym.EVRAK_KAYIT WHERE EVRAK_SIRA_NO = 'TEST-2025-001';
          -- Sample data 1 - İletişimin Denetlenmesi Kararı
          INSERT INTO iym.MAHKEME_KARAR_TALEP (
            ID, EVRAK_ID, KULLANICI_ID, KAYIT_TARIHI,
             KARAR_TIP, MAH_KARAR_BAS_TAR, MAH_KARAR_BITIS_TAR,
             MAHKEME_KARAR_NO, SORUSTURMA_NO, MAHKEME_ILI, MAHKEME_KODU, ACIKLAMA ) VALUES
            (iym.MAHKEME_KARAR_TALEP_SEQ.NEXTVAL, evrak_id1, admin_id, SYSDATE,
            '100', SYSDATE, ADD_MONTHS(SYSDATE, 3),
            '2023/1234', '2023/123', '0601', '06000101', 'İletişimin denetlenmesi kararı 1');
          
          -- Get the ID of the second evrak
          SELECT ID INTO evrak_id2 FROM iym.EVRAK_KAYIT WHERE EVRAK_SIRA_NO = 'TEST-2025-002';
          -- Sample data 2 - Mahkeme Kararı
          INSERT INTO iym.MAHKEME_KARAR_TALEP (
            ID, EVRAK_ID, KULLANICI_ID, KAYIT_TARIHI,
            KARAR_TIP, MAH_KARAR_BAS_TAR, MAH_KARAR_BITIS_TAR,
            MAHKEME_KARAR_NO, SORUSTURMA_NO, MAHKEME_ILI, MAHKEME_KODU, ACIKLAMA) VALUES
            (iym.MAHKEME_KARAR_TALEP_SEQ.NEXTVAL, evrak_id2, admin_id, SYSDATE,
            '300', SYSDATE, ADD_MONTHS(SYSDATE, 3),
            '2023/1235', '2023/126', '0601', '06000101', 'İletişimin denetlenmesi kararı 2');


           -- Get the ID of the second evrak
          SELECT ID INTO evrak_id3 FROM iym.EVRAK_KAYIT WHERE EVRAK_SIRA_NO = 'TEST-2025-003';
          -- Sample data 3 - Mahkeme Kararı
          INSERT INTO iym.MAHKEME_KARAR_TALEP (
            ID, EVRAK_ID, KULLANICI_ID, KAYIT_TARIHI,
            KARAR_TIP, MAH_KARAR_BAS_TAR, MAH_KARAR_BITIS_TAR,
            MAHKEME_KARAR_NO, SORUSTURMA_NO, MAHKEME_ILI, MAHKEME_KODU, ACIKLAMA) VALUES
            (iym.MAHKEME_KARAR_TALEP_SEQ.NEXTVAL, evrak_id3, admin_id, SYSDATE,
            '300', SYSDATE, ADD_MONTHS(SYSDATE, 3),
            '2023/1239', '2023/128', '0601', '06000101', 'İletişimin denetlenmesi kararı 3');

        EXCEPTION
          WHEN NO_DATA_FOUND THEN
            NULL; -- Required data not found, skip insertions
        END;
      END IF;
    END;
  END IF;
END;
/

COMMIT;
