package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.SorguTipleri;
import iym.common.service.db.DbSorguTipleriService;
import iym.db.jpa.dao.SorguTipleriRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;


@Service
public class DbSorguTipleriServiceImpl extends GenericDbServiceImpl<SorguTipleri, String> implements DbSorguTipleriService {

    private final SorguTipleriRepo sorguTipleriRepo;

    @Autowired
    public DbSorguTipleriServiceImpl(SorguTipleriRepo repository) {
        super(repository);
        this.sorguTipleriRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<SorguTipleri> findBySorguTipi(String sorguTipi){
        return sorguTipleriRepo.findBySorguTipi(sorguTipi);
    }


}
