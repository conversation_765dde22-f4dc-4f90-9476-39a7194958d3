package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.MahkemeSuclarIslem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MahkemeSuclarIslemRepo extends JpaRepository<MahkemeSuclarIslem, Long> {


    List<MahkemeSuclarIslem> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

}
