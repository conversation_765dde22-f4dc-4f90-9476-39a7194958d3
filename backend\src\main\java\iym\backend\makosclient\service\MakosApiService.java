package iym.backend.makosclient.service;

import iym.makos.api.client.gen.api.HealthCheckControllerApi;
import iym.makos.api.client.gen.api.MahkemeKararTalepControllerApi;
import iym.makos.api.client.gen.model.*;
import iym.makos.api.client.gen.model.IDAidiyatBilgisiGuncellemeResponse;
import iym.makos.api.client.gen.model.IDHedefGuncellemeResponse;
import iym.makos.api.client.gen.model.IDMahkemeKararGuncellemeResponse;
import iym.makos.api.client.gen.model.IDYeniKararResponse;
import iym.makos.api.client.gen.model.ITKararResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.io.File;

/**
 * MAKOS API Service
 * Generated API client'ı kullanarak MAKOS işlemlerini gerçekleştirir
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MakosApiService {

    private final MahkemeKararTalepControllerApi mahkemeKararTalepControllerApi;
    private final HealthCheckControllerApi healthCheckControllerApi;

    /**
     * Health check işlemi
     *
     * @return String response
     */
    public HealthCheckResponse healthCheck() {
        try {
            log.info("Performing health check");
            HealthCheckResponse response = healthCheckControllerApi.healthCheck();
            log.info("Health check successful: {}", response);
            return response;
        } catch (RestClientException e) {
            log.error("Health check failed: {}", e.getMessage(), e);
            throw new RuntimeException("Health check failed", e);
        }
    }

    /**
     * Health check with basic authorization işlemi
     *
     * @return String response
     */
    public HealthCheckResponse healthCheckAuthorized() {
        try {
            log.info("Performing health check with basic authorization");
            HealthCheckResponse response = healthCheckControllerApi.healthCheckAuthorized();
            log.info("Health check with basic authorization successful: {}", response);
            return response;
        } catch (RestClientException e) {
            log.error("Health check with basic authorization failed: {}", e.getMessage(), e);
            throw new RuntimeException("Health check with basic authorization failed", e);
        }
    }

    /**
     * Mahkeme bilgisi güncelleme işlemi
     *
     * @param mahkemeKararDosyasi Mahkeme karar dosyası
     * @param mahkemeKararDetay   Mahkeme karar detayları
     * @return IDMahkemeKararGuncellemeResponse response
     */
    public IDMahkemeKararGuncellemeResponse mahkemeBilgisiGuncelle(File mahkemeKararDosyasi, IDMahkemeKararGuncellemeRequest mahkemeKararDetay) {
        try {
            log.info("Updating mahkeme bilgisi for request ID: {}", mahkemeKararDetay.getId());
            IDMahkemeKararGuncellemeResponse response = mahkemeKararTalepControllerApi.mahkemeBilgisiGuncelle(mahkemeKararDosyasi, mahkemeKararDetay);
            log.info("Mahkeme bilgisi update successful for ID: {}", mahkemeKararDetay.getId());
            return response;
        } catch (RestClientException e) {
            log.error("Mahkeme bilgisi update failed for ID {}: {}", mahkemeKararDetay.getId(), e.getMessage(), e);
            throw new RuntimeException("Mahkeme bilgisi update failed", e);
        }
    }

    /**
     * ID tipi karar gönderme işlemi
     *
     * @param mahkemeKararDosyasiID Karar dosyası
     * @param mahkemeKararDetayID   Karar detayları
     * @return IDYeniKararResponse response
     */
    public IDYeniKararResponse kararGonderID(File mahkemeKararDosyasiID, IDYeniKararRequest mahkemeKararDetayID) {
        try {
            log.info("Sending ID karar for request ID: {}", mahkemeKararDetayID.getId());
            IDYeniKararResponse response = mahkemeKararTalepControllerApi.yeniKararID(mahkemeKararDosyasiID, mahkemeKararDetayID);
            log.info("ID karar send successful for ID: {}", mahkemeKararDetayID.getId());
            return response;
        } catch (RestClientException e) {
            log.error("ID karar send failed for ID {}: {}", mahkemeKararDetayID.getId(), e.getMessage(), e);
            throw new RuntimeException("ID karar send failed", e);
        }
    }

    /**
     * IT tipi karar gönderme işlemi
     *
     * @param mahkemeKararDosyasiIT Karar dosyası
     * @param mahkemeKararDetayIT   Karar detayları
     * @return ITKararResponse response
     */
    public ITKararResponse kararGonderIT(File mahkemeKararDosyasiIT, ITKararRequest mahkemeKararDetayIT) {
        try {
            log.info("Sending IT karar for request ID: {}", mahkemeKararDetayIT.getId());
            ITKararResponse response = mahkemeKararTalepControllerApi.yenikararIT(mahkemeKararDosyasiIT, mahkemeKararDetayIT);
            log.info("IT karar send successful for ID: {}", mahkemeKararDetayIT.getId());
            return response;
        } catch (RestClientException e) {
            log.error("IT karar send failed for ID {}: {}", mahkemeKararDetayIT.getId(), e.getMessage(), e);
            throw new RuntimeException("IT karar send failed", e);
        }
    }

    /**
     * Hedef ad soyad güncelleme işlemi
     *
     * @param mahkemeKararDosyasi Mahkeme karar dosyası
     * @param mahkemeKararDetay   Mahkeme karar detayları
     * @return IDHedefGuncellemeResponse response
     */
    public IDHedefGuncellemeResponse hedefAdSoyadGuncelle(File mahkemeKararDosyasi, IDHedefGuncellemeRequest mahkemeKararDetay) {
        try {
            log.info("Updating hedef ad soyad for request ID: {}", mahkemeKararDetay.getId());
            IDHedefGuncellemeResponse response = mahkemeKararTalepControllerApi.hedefBilgisiGuncelle(mahkemeKararDosyasi, mahkemeKararDetay);
            log.info("Hedef ad soyad update successful for ID: {}", mahkemeKararDetay.getId());
            return response;
        } catch (RestClientException e) {
            log.error("Hedef ad soyad update failed for ID {}: {}", mahkemeKararDetay.getId(), e.getMessage(), e);
            throw new RuntimeException("Hedef ad soyad update failed", e);
        }
    }


    /**
     * Aidiyet bilgisi güncelleme işlemi
     *
     * @param mahkemeKararDosyasi Mahkeme karar dosyası
     * @param mahkemeKararDetay   Mahkeme karar detayları
     * @return IDAidiyatBilgisiGuncellemeResponse response
     */
    public IDAidiyatBilgisiGuncellemeResponse aidiyatBilgisiGuncelle(File mahkemeKararDosyasi, IDAidiyatBilgisiGuncellemeRequest mahkemeKararDetay) {
        try {
            log.info("Updating aidiyet bilgisi for request ID: {}", mahkemeKararDetay.getId());
            IDAidiyatBilgisiGuncellemeResponse response = mahkemeKararTalepControllerApi.aidiyatBilgisiGuncelle(mahkemeKararDosyasi, mahkemeKararDetay);
            log.info("Aidiyet bilgisi update successful for ID: {}", mahkemeKararDetay.getId());
            return response;
        } catch (RestClientException e) {
            log.error("Aidiyet bilgisi update failed for ID {}: {}", mahkemeKararDetay.getId(), e.getMessage(), e);
            throw new RuntimeException("Aidiyet bilgisi update failed", e);
        }
    }
}

