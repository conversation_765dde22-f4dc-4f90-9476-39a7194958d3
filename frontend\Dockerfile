# Angular Uygulamasını Derleme Aşaması
FROM node:20-alpine AS angular-build
WORKDIR /app

COPY package.json package-lock.json ./
RUN npm install

COPY . .
RUN npm run build --configuration=production

# NGINX ile Yayın Aşaması
FROM nginx:stable-alpine AS angular-nginx

# Türkiye saat dilimi
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Europe/Istanbul /etc/localtime && \
    echo "Europe/Istanbul" > /etc/timezone

# Build edilmiş Angular uygulamasını kopyala
COPY --from=angular-build /app/dist/btk-ng/browser /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

# Docker entrypoint script'ini kopyala
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

EXPOSE 80

# Entrypoint script ile nginx'i başlat
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
