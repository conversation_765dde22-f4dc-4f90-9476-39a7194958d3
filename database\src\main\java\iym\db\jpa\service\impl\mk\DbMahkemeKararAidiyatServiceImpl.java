package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.MahkemeKararAidiyat;
import iym.common.service.db.mk.DbMahkemeKararAidiyatService;
import iym.db.jpa.dao.mk.MahkemeKararAidiyatRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for MahkemeAidiyat entity
 */
@Service
public class DbMahkemeKararAidiyatServiceImpl extends GenericDbServiceImpl<MahkemeKararAidiyat, Long> implements DbMahkemeKararAidiyatService {

    private final MahkemeKararAidiyatRepo mahkemeKararAidiyatRepo;

    @Autowired
    public DbMahkemeKararAidiyatServiceImpl(MahkemeKararAidiyatRepo mahkemeKararAidiyatRepo) {
        super(mahkemeKararAidiyatRepo);
        this.mahkemeKararAidiyatRepo = mahkemeKararAidiyatRepo;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeKararAidiyat> findByMahkemeKararId(Long mahkemeKararId) {
        return mahkemeKararAidiyatRepo.findByMahkemeKararId(mahkemeKararId);
    }


    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeKararAidiyat> findByMahkemeKararIdAndAidiyatKod(Long mahkemeKararId, String aidiyatKod) {
        return mahkemeKararAidiyatRepo.findByMahkemeKararIdAndAidiyatKod(mahkemeKararId, aidiyatKod);
    }
}
