package iym.common.testcontainer;

import org.junit.jupiter.api.TestInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.OracleContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

/**
 * Abstract base class for Oracle Testcontainer integration tests.
 * <p>
 * This class provides a shared Oracle 11 XE container for all tests that extend it.
 * The container is started once and reused across all test classes to improve performance.
 * <p>
 * Usage:
 * - Extend this class in your test classes
 * - Use @ActiveProfiles("oracle-test") to activate Oracle test profile
 * - Use @DataJpaTest, @Transactional, @Sql annotations as needed
 * <p>
 * Features:
 * - Oracle 11 XE Docker container (gvenzl/oracle-xe:********-slim)
 * - Automatic schema creation (IYM schema)
 * - Connection pooling with HikariCP
 * - SQL logging enabled for debugging
 * - Proper cleanup after tests
 */
@Testcontainers
@TestInstance(TestInstance.Lifecycle.PER_CLASS) // Required for parallel execution safety
public abstract class AbstractOracleTestContainerForDataJpa {

    private static final Logger log = LoggerFactory.getLogger(AbstractOracleTestContainerForDataJpa.class);

    /**
     * Oracle 11 XE container using the same image as in docker-compose.yml
     * Container is shared across all test classes for better performance
     * Reuse disabled for CI compatibility
     * IPv4 networking enforced for CI environment compatibility
     */
    @Container
    protected static final OracleContainer ORACLE_CONTAINER = new OracleContainer("gvenzl/oracle-xe:********-slim-faststart")
            .withDatabaseName("XE")
            .withUsername("iym")
            .withPassword("iym")
            .withReuse(false) // Disable reuse for CI environment compatibility
            .withSharedMemorySize(512 * 1024 * 1024L) // 512MB shared memory for better performance
            .withStartupTimeout(java.time.Duration.ofMinutes(5)) // Increased timeout for parallel execution
            .withEnv("ORACLE_ALLOW_REMOTE", "true") // Allow remote connections
            .withEnv("ORACLE_DISABLE_ASYNCH_IO", "true") // Disable async IO for better CI compatibility
            .withEnv("ORACLE_ENABLE_XDB", "false") // Disable XDB to reduce memory usage
            .withEnv("APP_USER_ROLE", "DBA") // Add DBA role for better permissions
            .withCreateContainerCmdModifier(cmd -> {
                // Force IPv4 networking for CI compatibility
                cmd.getHostConfig()
                    .withNetworkMode("bridge")
                    .withExtraHosts("localhost:127.0.0.1"); // Force localhost to IPv4
            })
            .withCommand("oracle-xe") // Explicit command for better startup
            .waitingFor(org.testcontainers.containers.wait.strategy.Wait.forLogMessage(".*DATABASE IS READY TO USE!.*\\n", 1)
                .withStartupTimeout(java.time.Duration.ofMinutes(5))); // Wait for Oracle to be fully ready
    /**
     * Configure Spring properties dynamically based on the running container
     * Properties are configured as suppliers that will be evaluated when needed
     * IPv4 localhost is enforced in JDBC URL to prevent IPv6 connection issues
     */
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        System.out.println("🔧 Configuring Spring properties for Oracle testcontainer...");
        
        // 🔧 CRITICAL: Ensure container is started before accessing properties
        if (!ORACLE_CONTAINER.isRunning()) {
            ORACLE_CONTAINER.start();
            log.info("✅ Oracle container started for @DynamicPropertySource");
        }
        
        // Ensure container is fully started and ready before configuring properties
        waitForOracleContainerReady();

        // Log container startup information
        logContainerStartupInfo();
        
        // Additional validation to ensure container is truly ready
        if (!ORACLE_CONTAINER.isRunning()) {
            throw new RuntimeException("Oracle container is not running after waitForOracleContainerReady");
        }

        try {
            // Test container accessibility one more time before configuring properties
            String testUrl = ORACLE_CONTAINER.getJdbcUrl().replace("localhost", "127.0.0.1");
            System.out.println("🔗 Final connection test with URL: " + testUrl);
            
            try (java.sql.Connection connection = java.sql.DriverManager.getConnection(
                    testUrl,
                    ORACLE_CONTAINER.getUsername(),
                    ORACLE_CONTAINER.getPassword())) {
                
                try (java.sql.Statement stmt = connection.createStatement()) {
                    stmt.executeQuery("SELECT 1 FROM DUAL");
                }
                System.out.println("✅ Final connection test successful");
            }
        } catch (Exception e) {
            throw new RuntimeException("Oracle container is not accessible after waitForOracleContainerReady: " + e.getMessage(), e);
        }

        // Oracle datasource configuration with enhanced connection properties
        // Using suppliers ensures container is started when properties are accessed
        registry.add("spring.datasource.url", () -> {
            String jdbcUrl = ORACLE_CONTAINER.getJdbcUrl();
            // Force IPv4 localhost in JDBC URL to prevent IPv6 connection issues
            jdbcUrl = jdbcUrl.replace("localhost", "127.0.0.1");
            // Add connection properties for better stability and IPv4 enforcement
            return jdbcUrl + "?oracle.net.CONNECT_TIMEOUT=60000&oracle.jdbc.ReadTimeout=60000&oracle.net.useAddressCache=false";
        });
        registry.add("spring.datasource.username", ORACLE_CONTAINER::getUsername);
        registry.add("spring.datasource.password", ORACLE_CONTAINER::getPassword);
        registry.add("spring.datasource.driver-class-name", () -> "oracle.jdbc.OracleDriver");

        // Disable embedded database detection
        registry.add("spring.datasource.embedded-database-connection", () -> "none");
        registry.add("spring.test.database.replace", () -> "none");

        // JPA/Hibernate configuration for Oracle
        registry.add("spring.jpa.database-platform", () -> "org.hibernate.dialect.Oracle12cDialect");
        registry.add("spring.jpa.properties.hibernate.default_schema", () -> "iym");
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "create");
        registry.add("spring.jpa.show-sql", () -> "true");
        registry.add("spring.jpa.properties.hibernate.format_sql", () -> "true");

        // Enhanced connection pool configuration for testcontainers with parallel execution safety
        registry.add("spring.datasource.hikari.connection-timeout", () -> "60000"); // 60 seconds
        registry.add("spring.datasource.hikari.maximum-pool-size", () -> "5");
        registry.add("spring.datasource.hikari.minimum-idle", () -> "1");
        registry.add("spring.datasource.hikari.idle-timeout", () -> "300000"); // 5 minutes
        registry.add("spring.datasource.hikari.max-lifetime", () -> "600000"); // 10 minutes
        registry.add("spring.datasource.hikari.leak-detection-threshold", () -> "30000"); // 30 seconds
        registry.add("spring.datasource.hikari.keepalive-time", () -> "300000"); // 5 minutes

        // Generate unique pool name for parallel execution safety
        String uniquePoolName = "OracleTestPool-" + Thread.currentThread().getName() + "-" + System.currentTimeMillis();
        registry.add("spring.datasource.hikari.pool-name", () -> uniquePoolName);

        // Additional parallel execution safety settings
        registry.add("spring.datasource.hikari.initialization-fail-timeout", () -> "-1"); // Don't fail fast
        registry.add("spring.datasource.hikari.isolate-internal-queries", () -> "true");
        registry.add("spring.datasource.hikari.connection-test-query", () -> "SELECT 1 FROM DUAL");
        registry.add("spring.datasource.hikari.validation-timeout", () -> "5000"); // 5 seconds

        // Logging configuration for tests
        registry.add("logging.level.org.hibernate.SQL", () -> "DEBUG");
        registry.add("logging.level.org.hibernate.type.descriptor.sql.BasicBinder", () -> "TRACE");
        registry.add("logging.level.iym", () -> "DEBUG");
        registry.add("logging.level.com.zaxxer.hikari", () -> "DEBUG"); // HikariCP logging
        
        System.out.println("✅ Spring properties configured successfully for Oracle testcontainer");
    }

    /**
     * Validates that the Oracle container is ready and accessible via IPv4
     * This method should be called before running tests to ensure connectivity
     * Provides comprehensive logging for debugging connection issues
     */
    protected static void validateContainerReadiness() {
        if (!ORACLE_CONTAINER.isRunning()) {
            throw new IllegalStateException("Oracle container is not running");
        }

        // Log comprehensive container details for debugging
        System.out.println("🔍 ===== ORACLE CONTAINER DEBUG INFORMATION =====");
        System.out.println("  📦 Container ID: " + ORACLE_CONTAINER.getContainerId());
        System.out.println("  🌐 Original JDBC URL: " + ORACLE_CONTAINER.getJdbcUrl());
        System.out.println("  🏠 Container Host: " + ORACLE_CONTAINER.getHost());
        System.out.println("  🔌 Exposed Port: " + ORACLE_CONTAINER.getExposedPorts());
        System.out.println("  🎯 Mapped Port (1521): " + ORACLE_CONTAINER.getMappedPort(1521));

        String ipv4JdbcUrl = ORACLE_CONTAINER.getJdbcUrl().replace("localhost", "127.0.0.1");
        System.out.println("  🔄 IPv4 JDBC URL: " + ipv4JdbcUrl);
        System.out.println("  👤 Username: " + ORACLE_CONTAINER.getUsername());
        System.out.println("  🔑 Password: " + ORACLE_CONTAINER.getPassword());

        // Log network information
        System.out.println("  🌍 Network Mode: " + ORACLE_CONTAINER.getNetworkMode());
        try {
            System.out.println("  📍 Container IP: " + ORACLE_CONTAINER.getHost());
        } catch (Exception e) {
            System.out.println("  📍 Container IP: Unable to retrieve (" + e.getMessage() + ")");
        }

        // Log JVM network preferences
        System.out.println("  ⚙️ JVM IPv4 Stack: " + System.getProperty("java.net.preferIPv4Stack"));
        System.out.println("  ⚙️ JVM IPv6 Addresses: " + System.getProperty("java.net.preferIPv6Addresses"));

        // Test basic connectivity with detailed error reporting
        System.out.println("  🧪 Testing connectivity...");
        try {
            java.sql.Connection connection = java.sql.DriverManager.getConnection(
                ipv4JdbcUrl,
                ORACLE_CONTAINER.getUsername(),
                ORACLE_CONTAINER.getPassword()
            );

            // Test actual database operation
            java.sql.Statement stmt = connection.createStatement();
            java.sql.ResultSet rs = stmt.executeQuery("SELECT 1 FROM DUAL");
            if (rs.next()) {
                System.out.println("  ✅ Oracle container connectivity test PASSED");
                System.out.println("  ✅ Database query test PASSED");
            }
            rs.close();
            stmt.close();
            connection.close();

        } catch (Exception e) {
            System.err.println("  ❌ Oracle container connectivity test FAILED");
            System.err.println("  ❌ Error Type: " + e.getClass().getSimpleName());
            System.err.println("  ❌ Error Message: " + e.getMessage());

            // Log detailed stack trace for debugging
            System.err.println("  📋 Full Stack Trace:");
            e.printStackTrace();

            // Log additional debugging info for specific error types
            if (e instanceof java.sql.SQLRecoverableException) {
                System.err.println("  🔍 SQLRecoverableException - Usually indicates network/connection issues");
            } else if (e instanceof java.net.ConnectException) {
                System.err.println("  🔍 ConnectException - Container port may not be ready or accessible");
            } else if (e instanceof java.sql.SQLTransientConnectionException) {
                System.err.println("  🔍 SQLTransientConnectionException - Connection pool or timeout issue");
            }

            System.err.println("🔍 ================================================");
            throw new IllegalStateException("Oracle container is not accessible", e);
        }

        System.out.println("🔍 ================================================");
    }

    /**
     * Waits for Oracle container to be fully ready and accessible
     * This method ensures Oracle database is completely initialized before tests run
     * Enhanced for parallel execution safety with proper wait strategy
     */
    private static void waitForOracleContainerReady() {
        System.out.println("🚀 Starting Oracle container readiness check...");
        
        // First, ensure container is actually started
        int containerStartRetries = 30; // 30 retries = 1 minute max wait for container start
        int containerRetryCount = 0;

        while (!ORACLE_CONTAINER.isRunning() && containerRetryCount < containerStartRetries) {
            System.out.println("⏳ Oracle container not running, waiting for startup... (attempt " +
                (containerRetryCount + 1) + "/" + containerStartRetries + ")");
            try {
                Thread.sleep(2000); // Wait 2 seconds between container checks
                containerRetryCount++;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Interrupted while waiting for container startup", e);
            }
        }

        if (!ORACLE_CONTAINER.isRunning()) {
            throw new RuntimeException("Oracle container failed to start after " + (containerStartRetries * 2) + " seconds");
        }

        System.out.println("✅ Oracle container is running, checking port mapping...");

        // Ensure container is not just running but also has mapped ports available
        int portRetries = 15; // 15 retries = 30 seconds max wait for port mapping
        int portRetryCount = 0;
        boolean portReady = false;

        while (!portReady && portRetryCount < portRetries) {
            try {
                // This will throw exception if container is not fully started with mapped ports
                int mappedPort = ORACLE_CONTAINER.getMappedPort(1521);
                String jdbcUrl = ORACLE_CONTAINER.getJdbcUrl();
                portReady = true;
                System.out.println("✅ Oracle container port mapping ready: " + mappedPort + ", JDBC URL: " + jdbcUrl);
            } catch (IllegalStateException e) {
                portRetryCount++;
                System.out.println("⏳ Oracle container port mapping not ready... (attempt " + portRetryCount + "/" + portRetries + ")");
                try {
                    Thread.sleep(2000); // Wait 2 seconds between port checks
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrupted while waiting for port mapping", ie);
                }
            }
        }

        if (!portReady) {
            throw new RuntimeException("Oracle container port mapping failed to become ready after " + (portRetries * 2) + " seconds");
        }

        System.out.println("⏳ Waiting for Oracle database to be fully ready...");

        // Wait for Oracle to be ready with retry logic - reduced retries since we now use proper wait strategy
        int maxRetries = 15; // 15 retries = 30 seconds max wait (reduced since container should be ready faster)
        int retryCount = 0;
        boolean isReady = false;

        while (!isReady && retryCount < maxRetries) {
            try {
                // This can also throw IllegalStateException if container is not fully ready
                String testUrl = ORACLE_CONTAINER.getJdbcUrl().replace("localhost", "127.0.0.1");
                System.out.println("🔗 Testing connection with URL: " + testUrl);
                
                java.sql.Connection connection = java.sql.DriverManager.getConnection(
                    testUrl,
                    ORACLE_CONTAINER.getUsername(),
                    ORACLE_CONTAINER.getPassword()
                );

                // Test if Oracle is fully ready by executing a simple query
                java.sql.Statement stmt = connection.createStatement();
                java.sql.ResultSet rs = stmt.executeQuery("SELECT 1 FROM DUAL");
                if (rs.next()) {
                    isReady = true;
                    System.out.println("✅ Oracle database is ready after " + (retryCount * 2) + " seconds");
                }
                rs.close();
                stmt.close();
                connection.close();

            } catch (java.sql.SQLException e) {
                retryCount++;
                if (e.getMessage().contains("ORA-01033")) {
                    System.out.println("⏳ Oracle still initializing... (attempt " + retryCount + "/" + maxRetries + ")");
                } else if (e.getMessage().contains("Connection refused")) {
                    System.out.println("⏳ Oracle port not ready... (attempt " + retryCount + "/" + maxRetries + ")");
                } else {
                    System.out.println("⏳ Oracle connection issue: " + e.getMessage() + " (attempt " + retryCount + "/" + maxRetries + ")");
                }

                try {
                    Thread.sleep(2000); // Wait 2 seconds between retries
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrupted while waiting for Oracle", ie);
                }
            } catch (IllegalStateException e) {
                // Handle the case where getJdbcUrl() is called before container is fully started
                retryCount++;
                System.out.println("⏳ Oracle container JDBC URL not ready... (attempt " + retryCount + "/" + maxRetries + ")");

                try {
                    Thread.sleep(2000); // Wait 2 seconds between retries
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrupted while waiting for Oracle", ie);
                }
            }
        }

        if (!isReady) {
            throw new RuntimeException("Oracle database failed to become ready after " + (maxRetries * 2) + " seconds");
        }
        
        System.out.println("🎉 Oracle container is fully ready and accessible!");
    }

    /**
     * Logs container startup information for debugging
     * Called during @DynamicPropertySource configuration
     */
    private static void logContainerStartupInfo() {
        System.out.println("🚀 ===== ORACLE CONTAINER STARTUP =====");
        System.out.println("  📦 Container starting/started: " + ORACLE_CONTAINER.isRunning());
        if (ORACLE_CONTAINER.isRunning()) {
            System.out.println("  🌐 JDBC URL: " + ORACLE_CONTAINER.getJdbcUrl());
            System.out.println("  🎯 Mapped Port: " + ORACLE_CONTAINER.getMappedPort(1521));
            System.out.println("  🔄 IPv4 URL: " + ORACLE_CONTAINER.getJdbcUrl().replace("localhost", "127.0.0.1"));
        }
        System.out.println("🚀 =====================================");
    }

    /**
     * Test configuration class for Oracle Testcontainer
     * Note: DataSource configuration is now handled via @DynamicPropertySource
     * to avoid race conditions with container startup
     */
    @TestConfiguration
    public static class OracleTestContainerConfiguration {
        // DataSource configuration moved to @DynamicPropertySource method
        // to ensure container is started before accessing connection properties
    }

    /**
     * Get the Oracle container instance for advanced usage in tests
     */
    protected static OracleContainer getOracleContainer() {
        return ORACLE_CONTAINER;
    }

    /**
     * Cleanup method to ensure proper resource management between test classes
     * This method can be called in @AfterAll or @DirtiesContext scenarios
     */
    protected static void cleanupContainerResources() {
        try {
            if (ORACLE_CONTAINER.isRunning()) {
                System.out.println("🧹 Cleaning up Oracle container resources...");

                // Force close any remaining connections
                try {
                    ORACLE_CONTAINER.execInContainer("sqlplus", "-S", "iym/iym@XE", "-c",
                        "ALTER SYSTEM KILL SESSION 'SID,SERIAL#' IMMEDIATE;");
                } catch (Exception e) {
                    System.out.println("⚠️ Warning: Could not force close connections: " + e.getMessage());
                }

                System.out.println("✅ Oracle container resources cleaned up");
            }
        } catch (Exception e) {
            System.out.println("⚠️ Warning: Error during container cleanup: " + e.getMessage());
        }
    }

    /**
     * Ensure container is in a clean state for the next test
     * This method helps prevent test interference in parallel execution
     * Less aggressive cleanup to avoid interfering with other parallel tests
     */
    protected static void ensureCleanContainerState() {
        try {
            if (ORACLE_CONTAINER.isRunning()) {
                // Only clean up inactive sessions to avoid interfering with other tests
                // This is a more conservative approach for parallel execution
                String cleanupSql = "BEGIN " +
                    "FOR c IN (SELECT sid, serial# FROM v$session WHERE username = 'IYM' AND status = 'INACTIVE' AND type = 'USER') " +
                    "LOOP " +
                    "BEGIN " +
                    "EXECUTE IMMEDIATE 'ALTER SYSTEM KILL SESSION ''' || c.sid || ',' || c.serial# || ''' IMMEDIATE'; " +
                    "EXCEPTION WHEN OTHERS THEN NULL; " + // Ignore errors for parallel safety
                    "END; " +
                    "END LOOP; " +
                    "END;";

                try {
                    ORACLE_CONTAINER.execInContainer("sqlplus", "-S", "iym/iym@XE", "-c", cleanupSql);
                    System.out.println("🔄 Container state cleaned for next test");
                } catch (Exception e) {
                    // Don't fail if cleanup fails - this is expected in parallel execution
                    System.out.println("⚠️ Info: Container cleanup skipped (parallel execution): " + e.getMessage());
                }
            }
        } catch (Exception e) {
            // Don't fail the test if cleanup fails, just log it
            System.out.println("⚠️ Warning: Could not clean container state: " + e.getMessage());
        }
    }

    /**
     * Execute SQL script in the Oracle container
     * Useful for test data setup
     */
    protected void executeSqlScript(String sqlScript) {
        try {
            ORACLE_CONTAINER.execInContainer("sqlplus", "-S", "iym/iym@XE", "@" + sqlScript);
        } catch (Exception e) {
            throw new RuntimeException("Failed to execute SQL script: " + sqlScript, e);
        }
    }

    /**
     * Execute SQL statement in the Oracle container
     * Useful for quick test data setup
     */
    protected void executeSql(String sql) {
        try {
            ORACLE_CONTAINER.execInContainer("sqlplus", "-S", "iym/iym@XE", "-c", sql);
        } catch (Exception e) {
            throw new RuntimeException("Failed to execute SQL: " + sql, e);
        }
    }
}
