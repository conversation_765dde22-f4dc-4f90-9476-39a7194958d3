package iym.common.model.entity.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * Entity class for MAHKEME_KARAR_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MahkemeKararTipleri")
@Table(name = "MAH_KARAR_TIPLERI")
public class MahkemeKararTipleri implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MAH_KARAR_TIPLERI_SEQ")
    @SequenceGenerator(name = "MAH_KARAR_TIPLERI_SEQ", sequenceName = "MAH_KARAR_TIPLERI_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "KARAR_KODU", nullable = false)
    @NotNull
    private Long kararKodu;

    @Column(name = "KARAR_TIPI", length = 100)
    @Size(max = 100)
    private String kararTipi;

    @Column(name = "KARAR_TURU", length = 10)
    @Size(max = 10)
    private String kararTuru;

    @Column(name = "SONLANDIRMAMI")
    private Long sonlandirma;


}
