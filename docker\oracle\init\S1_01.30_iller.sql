-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create ILLER table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'ILLER';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.ILLER (
      IL_KOD VARCHAR2(4 BYTE) NOT NULL,
      IL_ADI VARCHAR2(50 BYTE),
      ILCE_ADI VARCHAR2(50 BYTE),
      CONSTRAINT IL_KOD_IDX PRIMARY KEY (IL_KOD) ENABLE
    )';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.ILLER;
  IF row_count = 0 THEN
    -- Sample data - Adana
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('0100', 'ADANA', 'MERKEZ');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('0101', 'ADANA', 'CEYHAN');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('0102', 'ADANA', 'FEKE');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('0103', 'ADANA', 'KARAİSALI');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('0104', 'ADANA', 'KARATAŞ');
    
    -- Sample data - Ankara
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('0600', 'ANKARA', 'MERKEZ');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('0601', 'ANKARA', 'AKYURT');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('0602', 'ANKARA', 'AYAŞ');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('0603', 'ANKARA', 'BALA');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('0604', 'ANKARA', 'BEYPAZARI');
    
    -- Sample data - İstanbul
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('3400', 'İSTANBUL', 'MERKEZ');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('3401', 'İSTANBUL', 'ADALAR');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('3402', 'İSTANBUL', 'ARNAVUTKÖY');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('3403', 'İSTANBUL', 'ATAŞEHİR');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('3404', 'İSTANBUL', 'AVCILAR');
  END IF;
END;
/

COMMIT;
