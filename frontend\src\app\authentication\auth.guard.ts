import { inject } from '@angular/core';
import { CanActivateFn, ActivatedRouteSnapshot, Router } from '@angular/router';
import { AuthService } from './auth.service';
import { MenuService } from '../menu/menu.service';

export const AuthGuard: CanActivateFn = async (route: ActivatedRouteSnapshot) => {
  const authService = inject(AuthService);
  const router = inject(Router);
  const menuService = inject(MenuService);

  if (!authService.checkAuth()) {
    router.navigate(['/login']);
    return false;
  }

  // Eğer menu boşsa yüklenmesini bekle
  if (menuService.menuItems().length === 0) {
    await menuService.loadMenuAsync(); // asenkron versiyonunu oluşturacağız
  }

  const currentUrl = '/' + route.pathFromRoot
    .map(r => r.url.map(u => u.path).join('/'))
    .filter(p => p)
    .join('/');

  const allMenuItems = flattenMenuItems(menuService.menuItems());
  const matchedMenu = allMenuItems.find(m => '/' + m.routerLink === currentUrl);

  const menuRoles = matchedMenu?.roles ?? [];
  const userRoles = authService.getUserPermissions();
  const hasPermission = menuRoles.length === 0 || menuRoles.some((role: string) => userRoles.includes(role));

  if (!hasPermission) {
    router.navigate(['/not-found']);
    return false;
  }

  return true;
};

function flattenMenuItems(items: any[]): any[] {
  return items.reduce((acc, item) => {
    acc.push(item);
    if (item.items) {
      acc.push(...flattenMenuItems(item.items));
    }
    return acc;
  }, [] as any[]);
}
