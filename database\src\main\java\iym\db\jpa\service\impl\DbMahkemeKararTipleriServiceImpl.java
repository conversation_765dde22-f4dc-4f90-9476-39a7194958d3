package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.MahkemeKararTipleri;
import iym.common.model.entity.iym.SucTipi;
import iym.common.service.db.DbMahkemeKararTipleriService;
import iym.db.jpa.dao.MahkemeKararTipleriRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for MahkemeAidiyat entity
 */
@Service
public class DbMahkemeKararTipleriServiceImpl extends GenericDbServiceImpl<MahkemeKararTipleri, Long> implements DbMahkemeKararTipleriService {

    private final MahkemeKararTipleriRepo mahkemeKararTipleriRepo;

    @Autowired
    public DbMahkemeKararTipleriServiceImpl(MahkemeKararTipleriRepo repository) {
        super(repository);
        this.mahkemeKararTipleriRepo = repository;
    }


    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeKararTipleri> findByKararKodu(String kararKodu){
        return mahkemeKararTipleriRepo.findByKararKodu(kararKodu);
    }



}
