import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { Router } from '@angular/router';

// PrimeNG Imports
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextarea } from 'primeng/inputtextarea';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { ToastModule } from 'primeng/toast';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { FileUploadModule } from 'primeng/fileupload';
import { TableModule } from 'primeng/table';
import { DialogModule } from 'primeng/dialog';
import { MessageService } from 'primeng/api';

// Models
import { 
  IDYeniKararRequest,
  HedefDetayID,
  KararTuru,
  EvrakTuru,
  MahkemeKararTip 
} from '../shared/models/iym.models';

// Services
import { TalepService } from '../shared/services/talep.service';

@Component({
  selector: 'app-id-yeni-karar',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CardModule,
    ButtonModule,
    InputTextModule,
    InputTextarea,
    DropdownModule,
    CalendarModule,
    CheckboxModule,
    ToastModule,
    ProgressSpinnerModule,
    FileUploadModule,
    TableModule,
    DialogModule
  ],
  providers: [MessageService],
  templateUrl: './id-yeni-karar.component.html',
  styleUrls: ['./id-yeni-karar.component.scss']
})
export class IdYeniKararComponent implements OnInit {
  
  talepForm: FormGroup;
  hedefDialogVisible = false;
  hedefForm: FormGroup;
  editingHedefIndex = -1;
  yukleniyor = false;
  seciliDosya: File | null = null;

  // Dropdown Options
  evrakTuruOptions = [
    { label: 'İletişimin Denetlenmesi', value: EvrakTuru.ILETISIMIN_DENETLENMESI }
  ];

  mahkemeKararTipOptions = [
    { label: 'Önleyici Hakim Kararı', value: MahkemeKararTip.ONLEYICI_HAKIM_KARARI },
    { label: 'Adli Hakim Kararı', value: MahkemeKararTip.ADLI_HAKIM_KARARI },
    { label: 'Adli Yazılı Emir', value: MahkemeKararTip.ADLI_YAZILI_EMIR }
  ];

  hedefTipiOptions = [
    { label: 'GSM', value: '10' },
    { label: 'Sabit', value: '20' },
    { label: 'UYDU', value: '30' },
    { label: 'Yurt Dışı', value: '40' },
    { label: 'E-posta', value: '50' },
    { label: 'IMEI', value: '60' },
    { label: 'IMSI', value: '70' }
  ];

  sureTipiOptions = [
    { label: 'Gün', value: 'GUN' },
    { label: 'Ay', value: 'AY' },
    { label: 'Yıl', value: 'YIL' }
  ];

  constructor(
    private fb: FormBuilder,
    private talepService: TalepService,
    private messageService: MessageService,
    private router: Router
  ) {
    this.talepForm = this.createForm();
    this.hedefForm = this.createHedefForm();
  }

  ngOnInit(): void {
    // Component initialization
    
    // Development mode check for test utilities
    if (this.isDevelopmentMode()) {
      console.log('🧪 Development mode detected - Test utilities available');
      console.log('💡 Use fillTestData() or clearTestData() in console');
      
      // Expose test methods to global window object
      (window as any).fillTestData = () => this.fillTestData();
      (window as any).clearTestData = () => this.clearTestData();
      (window as any).addTestHedef = (adi?: string, soyadi?: string, hedefNo?: string) => this.addTestHedef(adi, soyadi, hedefNo);
      (window as any).validateTestForm = () => this.validateTestForm();
      (window as any).createTestFile = () => this.createTestFile();
      
      console.log('✅ Test utilities bound to window object');
    }
  }

  private isDevelopmentMode(): boolean {
    return window.location.hostname === 'localhost' || window.location.port === '4200';
  }

  // Test data utilities for development
  fillTestData(): void {
    if (!this.isDevelopmentMode()) {
      console.warn('Test data utilities only available in development mode');
      return;
    }

    this.talepForm.patchValue({
      // Evrak Detayları
      evrakNo: '2024-TEST-001',
      evrakTarihi: new Date('2024-01-15'),
      evrakKurumKodu: '12345',
      evrakTuru: EvrakTuru.ILETISIMIN_DENETLENMESI,
      havaleBirimi: 'TEST BİRİMİ',
      evrakAciklama: 'Test amaçlı yeni karar talebi',
      geldigiIlIlceKodu: '3401',
      acilmi: true,
      evrakKonusu: 'Test konusu',

      // Mahkeme Karar Detayları
      mahkemeKararTipi: MahkemeKararTip.ONLEYICI_HAKIM_KARARI,
      mahkemeKodu: 'IST-001',
      mahkemeKararNo: '2024/12345',
      mahkemeIlIlceKodu: '3401',
      sorusturmaNo: '2024-SOR-001',
      mahkemeAciklama: 'Test mahkeme kararı'
    });

    // Add sample targets
    this.addTestHedef();
    this.addTestHedef('Ahmet', 'Test', '2');

    // Create test file
    this.createTestFile();

    console.log('✅ Test data loaded successfully!');
    console.log('📋 Form values:', this.talepForm.value);
  }

  addTestHedef(adi: string = 'Mehmet', soyadi: string = 'Örnek', hedefNo: string = '1'): void {
    const testHedef = this.fb.group({
      hedefNo: [hedefNo, Validators.required],
      hedefTipi: ['10', Validators.required],
      hedefAdi: [adi],
      hedefSoyadi: [soyadi],
      baslamaTarihi: [new Date(), Validators.required],
      suresi: ['30'],
      sureTipi: ['GUN', Validators.required],
      bimAidiyatKod: ['BIM-TEST-001', Validators.required],
      canakNo: ['CN-TEST-001']
    });

    this.hedefDetayListesi.push(testHedef);
    console.log(`🎯 Added test hedef: ${adi} ${soyadi}`);
  }

  createTestFile(): void {
    const fileContent = '%PDF-1.4\n%Test PDF Content\n%%EOF';
    const blob = new Blob([fileContent], { type: 'application/pdf' });
    this.seciliDosya = new File([blob], 'test-karar.pdf', { type: 'application/pdf' });
    console.log('📄 Test file created: test-karar.pdf');
  }

  clearTestData(): void {
    if (!this.isDevelopmentMode()) {
      console.warn('Test data utilities only available in development mode');
      return;
    }

    this.talepForm.reset();
    this.hedefDetayListesi.clear();
    this.seciliDosya = null;
    
    // Reset to defaults
    this.talepForm.patchValue({
      evrakTuru: EvrakTuru.ILETISIMIN_DENETLENMESI,
      evrakTarihi: new Date()
    });

    console.log('🧹 Test data cleared');
  }

  validateTestForm(): void {
    const isValid = this.talepForm.valid;
    console.log('📊 Form Status:', isValid ? 'Valid' : 'Invalid');
    if (!isValid) {
      console.log('❌ Validation errors:', this.talepForm.errors);
      Object.keys(this.talepForm.controls).forEach(key => {
        const control = this.talepForm.get(key);
        if (control && control.invalid) {
          console.log(`❌ ${key}:`, control.errors);
        }
      });
    }
  }

  private createForm(): FormGroup {
    return this.fb.group({
      // EvrakDetay fields
      evrakNo: ['', [Validators.required, Validators.maxLength(50)]],
      evrakTarihi: [new Date(), Validators.required],
      evrakKurumKodu: ['', Validators.required],
      evrakTuru: [EvrakTuru.ILETISIMIN_DENETLENMESI, Validators.required],
      havaleBirimi: [''],
      evrakAciklama: [''],
      geldigiIlIlceKodu: ['', Validators.required],
      acilmi: [false],
      evrakKonusu: [''],

      // MahkemeKararBilgisi fields
      mahkemeKararTipi: [null, Validators.required],
      
      // MahkemeKararDetay fields
      mahkemeKodu: ['', Validators.required],
      mahkemeKararNo: ['', Validators.required],
      mahkemeIlIlceKodu: ['', Validators.required],
      sorusturmaNo: [''],
      mahkemeAciklama: [''],

      // ID specific fields
      hedefDetayListesi: this.fb.array([]),
      mahkemeAidiyatKodlari: [[]],
      mahkemeSucTipiKodlari: [[]]
    });
  }

  private createHedefForm(): FormGroup {
    return this.fb.group({
      hedefNo: ['', Validators.required],
      hedefTipi: [null, Validators.required],
      hedefAdi: [''],
      hedefSoyadi: [''],
      baslamaTarihi: [new Date(), Validators.required],
      suresi: [''],
      sureTipi: [null, Validators.required],
      bimAidiyatKod: ['', Validators.required],
      canakNo: ['']
    });
  }

  get hedefDetayListesi(): FormArray {
    return this.talepForm.get('hedefDetayListesi') as FormArray;
  }

  get hedefler(): HedefDetayID[] {
    return this.hedefDetayListesi.value;
  }

  hedefEkleDialog(): void {
    this.editingHedefIndex = -1;
    this.hedefForm.reset();
    this.hedefForm.patchValue({
      baslamaTarihi: new Date(),
      sureTipi: 'GUN'
    });
    this.hedefDialogVisible = true;
  }

  hedefDuzenleDialog(index: number): void {
    this.editingHedefIndex = index;
    const hedef = this.hedefDetayListesi.at(index).value;
    this.hedefForm.patchValue({
      ...hedef,
      baslamaTarihi: new Date(hedef.baslamaTarihi)
    });
    this.hedefDialogVisible = true;
  }

  hedefKaydet(): void {
    if (this.hedefForm.valid) {
      const hedefData = {
        ...this.hedefForm.value,
        baslamaTarihi: this.hedefForm.value.baslamaTarihi.toISOString()
      };

      if (this.editingHedefIndex >= 0) {
        // Güncelleme
        this.hedefDetayListesi.at(this.editingHedefIndex).patchValue(hedefData);
        this.messageService.add({
          severity: 'success',
          summary: 'Başarılı',
          detail: 'Hedef güncellendi'
        });
      } else {
        // Yeni ekleme
        this.hedefDetayListesi.push(this.fb.control(hedefData));
        this.messageService.add({
          severity: 'success',
          summary: 'Başarılı',
          detail: 'Hedef eklendi'
        });
      }

      this.hedefDialogVisible = false;
      this.hedefForm.reset();
    } else {
      this.messageService.add({
        severity: 'warn',
        summary: 'Uyarı',
        detail: 'Lütfen tüm zorunlu alanları doldurun'
      });
    }
  }

  hedefSil(index: number): void {
    this.hedefDetayListesi.removeAt(index);
    this.messageService.add({
      severity: 'info',
      summary: 'Bilgi',
      detail: 'Hedef silindi'
    });
  }

  onDosyaSecildi(event: any): void {
    const dosyalar = event.files;
    if (dosyalar && dosyalar.length > 0) {
      this.seciliDosya = dosyalar[0];
      this.messageService.add({
        severity: 'info',
        summary: 'Dosya Seçildi',
        detail: `${this.seciliDosya?.name} dosyası seçildi`
      });
    }
  }

  onSubmit(): void {
    if (this.talepForm.valid && this.hedefDetayListesi.length > 0 && this.seciliDosya) {
      this.yukleniyor = true;
      
      const formData = this.talepForm.value;
      
      // Create request object with proper hedefDetayListesi structure
      const hedefDetayListesi = formData.hedefDetayListesi.map((hedef: any) => ({
        hedefNoAdSoyad: {
          hedefNo: hedef.hedefNo,
          hedefTipi: hedef.hedefTipi,
          hedefAdi: hedef.hedefAdi,
          hedefSoyadi: hedef.hedefSoyadi
        },
        baslamaTarihi: hedef.baslamaTarihi instanceof Date 
          ? hedef.baslamaTarihi.toISOString().replace('Z', '')
          : new Date(hedef.baslamaTarihi).toISOString().replace('Z', ''),
        sure: parseInt(hedef.suresi) || 30,
        sureTip: hedef.sureTipi,
        bimAidiyatKod: hedef.bimAidiyatKod,
        canakNo: hedef.canakNo,
        ilgiliMahkemeKararDetayi: {
          mahkemeKodu: formData.mahkemeKodu,
          mahkemeKararNo: formData.mahkemeKararNo,
          mahkemeIlIlceKodu: formData.mahkemeIlIlceKodu,
          sorusturmaNo: formData.sorusturmaNo,
          aciklama: formData.mahkemeAciklama
        }
      }));

      const request: IDYeniKararRequest = {
        id: this.generateUUID(),
        kararTuru: KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR,
        evrakDetay: {
          evrakNo: formData.evrakNo,
          evrakTarihi: formData.evrakTarihi instanceof Date 
            ? formData.evrakTarihi.toISOString().replace('Z', '')
            : new Date(formData.evrakTarihi).toISOString().replace('Z', ''),
          evrakKurumKodu: formData.evrakKurumKodu,
          evrakTuru: formData.evrakTuru,
          havaleBirimi: formData.havaleBirimi,
          aciklama: formData.evrakAciklama,
          geldigiIlIlceKodu: formData.geldigiIlIlceKodu,
          acilmi: formData.acilmi,
          evrakKonusu: formData.evrakKonusu
        },
        mahkemeKararBilgisi: {
          mahkemeKararTipi: formData.mahkemeKararTipi,
          mahkemeKararDetay: {
            mahkemeKodu: formData.mahkemeKodu,
            mahkemeKararNo: formData.mahkemeKararNo,
            mahkemeIlIlceKodu: formData.mahkemeIlIlceKodu,
            sorusturmaNo: formData.sorusturmaNo,
            aciklama: formData.mahkemeAciklama
          }
        },
        hedefDetayListesi: hedefDetayListesi,
        mahkemeAidiyatKodlari: formData.mahkemeAidiyatKodlari,
        mahkemeSucTipiKodlari: formData.mahkemeSucTipiKodlari
      };

      // Send request
      this.talepService.yeniKararIdGonder(request, this.seciliDosya).subscribe({
        next: (response) => {
          this.yukleniyor = false;
          this.messageService.add({
            severity: 'success',
            summary: 'Başarılı',
            detail: 'ID Yeni Karar talebi başarıyla gönderildi'
          });
          this.talepForm.reset();
          this.hedefDetayListesi.clear();
          this.seciliDosya = null;
        },
        error: (error) => {
          this.yukleniyor = false;
          this.messageService.add({
            severity: 'error',
            summary: 'Hata',
            detail: 'Talep gönderilirken hata oluştu: ' + error.message
          });
        }
      });
    } else {
      let errorMessage = 'Lütfen kontrol edin: ';
      if (!this.talepForm.valid) errorMessage += 'Form alanları, ';
      if (this.hedefDetayListesi.length === 0) errorMessage += 'En az bir hedef, ';
      if (!this.seciliDosya) errorMessage += 'Dosya seçimi ';
      
      this.messageService.add({
        severity: 'warn',
        summary: 'Uyarı',
        detail: errorMessage
      });
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.talepForm.controls).forEach(key => {
      const control = this.talepForm.get(key);
      control?.markAsTouched();
    });
  }

  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  onReset(): void {
    this.talepForm.reset();
    this.hedefDetayListesi.clear();
    this.seciliDosya = null;
    this.messageService.add({
      severity: 'info',
      summary: 'Form Sıfırlandı',
      detail: 'Tüm alanlar temizlendi'
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.talepForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  isHedefFieldInvalid(fieldName: string): boolean {
    const field = this.hedefForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.talepForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) {
        return 'Bu alan zorunludur';
      }
      if (field.errors['maxlength']) {
        return `Maksimum ${field.errors['maxlength'].requiredLength} karakter olmalıdır`;
      }
    }
    return '';
  }

  getHedefFieldError(fieldName: string): string {
    const field = this.hedefForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) {
        return 'Bu alan zorunludur';
      }
    }
    return '';
  }

  formatTarih(tarih: string): string {
    return new Date(tarih).toLocaleDateString('tr-TR');
  }

  getHedefTipiLabel(value: string): string {
    const option = this.hedefTipiOptions.find(opt => opt.value === value);
    return option ? option.label : value;
  }

  getSureTipiLabel(value: string): string {
    const option = this.sureTipiOptions.find(opt => opt.value === value);
    return option ? option.label : value;
  }

  hedefDialogKapat(): void {
    this.hedefDialogVisible = false;
    this.hedefForm.reset();
    this.editingHedefIndex = -1;
  }
}
