package iym.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum KullaniciKurum {

    EMNIYET("02", "EMNIYET"),
    MIT("04", "MIT"),
    JANDARMA("03", "JANDARMA"),
    BTK("01", "BTK"),
    ADLI("06", "ADLI"),
    EMNIYET_SIBER("07", "EMNIYET_SIBER"),
    IDB("05", "EMNIYET IDB");

    private final String value;

    private final String name;

    private static final Map<String, KullaniciKurum> VALUE_MAP;
    private static final Map<String, KullaniciKurum> NAME_MAP;
    static {
        // Build lookup maps for fast retrieval
        VALUE_MAP = Arrays.stream(KullaniciKurum.values())
                .collect(Collectors.toMap(KullaniciKurum::getValue, Function.identity()));

        NAME_MAP = Arrays.stream(KullaniciKurum.values())
                .collect(Collectors.toMap(KullaniciKurum::getName, Function.identity()));
    }
    KullaniciKurum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static Optional<KullaniciKurum> convert(String kurum){

        for (KullaniciKurum type : KullaniciKurum.values()){
            if (type.value.equals( kurum))
                return Optional.of(type);
        }

        return Optional.empty();
    }

    /**
     * Converts name (case-insensitive) to enum.
     */
    public static Optional<KullaniciKurum> fromName(String name) {
        if (name == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(NAME_MAP.get(name));
    }

    /**
     * Case-sensitive name-based conversion.
     * Throws exception if no match found.
     */
    public static KullaniciKurum fromNameOrThrow(String name) {
        return fromName(name)
                .orElseThrow(() -> new IllegalArgumentException("Geçersiz Kurum: " + name));
    }
}
