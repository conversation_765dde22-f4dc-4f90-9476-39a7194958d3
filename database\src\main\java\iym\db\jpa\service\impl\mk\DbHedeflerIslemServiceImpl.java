package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.enums.HedefTip;
import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.model.entity.iym.mk.HedeflerIslem;
import iym.common.service.db.mk.DbHedeflerIslemService;
import iym.common.service.db.mk.DbHedeflerService;
import iym.db.jpa.dao.mk.HedeflerIslemRepo;
import iym.db.jpa.dao.mk.HedeflerRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for Hedefler entity
 */
@Service
public class DbHedeflerIslemServiceImpl extends GenericDbServiceImpl<HedeflerIslem, Long> implements DbHedeflerIslemService {

    private final HedeflerIslemRepo hedeflerIslemRepo;

    @Autowired
    public DbHedeflerIslemServiceImpl(HedeflerIslemRepo repository) {
        super(repository);
        this.hedeflerIslemRepo = repository;
    }


    @Override
    @Transactional(readOnly = true)
    public List<HedeflerIslem> findByMahkemeKararId(Long mahkemeKararId) {
        return hedeflerIslemRepo.findByMahkemeKararTalepId(mahkemeKararId);
    }

}
