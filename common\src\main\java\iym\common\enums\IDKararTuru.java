package iym.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum IDKararTuru {
	ILETISIMIN_DENETLENMESI_YENI_KARAR(0),
	ILETISIMIN_DENETLENMESI_UZATMA_KARARI(1),
	ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI(2),
	ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME(3),
	ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME(4),
	ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME(5),
	ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME(6)
	;


	private final int kararTuru;

	IDKararTuru(int kararTuru){
		this.kararTuru = kararTuru;
	}

	@JsonValue
	public int getKararTuru(){
		return this.kararTuru;
	}

	@JsonCreator
	public static IDKararTuru fromName(String name) {
		for (IDKararTuru kararTuru : IDKararTuru.values()) {
			if (kararTuru.name().equals(name)) {
				return kararTuru;
			}
		}
		throw new IllegalArgumentException("Gecersiz kararTuru: '" + name + "'");
	}

	//@JsonCreator
	public static IDKararTuru fromValue(int value) {
		for (IDKararTuru evrakTuru : IDKararTuru.values()) {
			if (evrakTuru.kararTuru == value) {
				return evrakTuru;
			}
		}
		throw new IllegalArgumentException("Gecersiz kararTuru: '" + value + "'");
	}
}
