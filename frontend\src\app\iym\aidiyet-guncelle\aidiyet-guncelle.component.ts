import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';

// PrimeNG Imports
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextarea } from 'primeng/inputtextarea';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { ToastModule } from 'primeng/toast';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { FileUploadModule } from 'primeng/fileupload';
import { MultiSelectModule } from 'primeng/multiselect';
import { MessageService } from 'primeng/api';

// Models
import {
  IDAidiyatBilgisiGuncellemeRequest,
  KararTuru,
  EvrakTuru,
  MahkemeKararTip,
  GuncellemeTip
} from '../shared/models/iym.models';

// Services
import { TalepService } from '../shared/services/talep.service';

@Component({
  selector: 'app-aidiyet-guncelle',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CardModule,
    ButtonModule,
    InputTextModule,
    InputTextarea,
    DropdownModule,
    CalendarModule,
    CheckboxModule,
    ToastModule,
    ProgressSpinnerModule,
    FileUploadModule,
    MultiSelectModule
  ],
  providers: [MessageService],
  templateUrl: './aidiyet-guncelle.component.html',
  styleUrls: ['./aidiyet-guncelle.component.scss']
})
export class AidiyetGuncelleComponent implements OnInit {
  
  talepForm: FormGroup;
  yukleniyor = false;
  seciliDosya: File | null = null;

  // Dropdown Options
  evrakTuruOptions = [
    { label: 'İletişimin Denetlenmesi', value: EvrakTuru.ILETISIMIN_DENETLENMESI }
  ];

  mahkemeKararTipOptions = [
    { label: 'Aidiyet Bilgisi Değiştirme', value: MahkemeKararTip.MAHKEME_AIDIYAT_DEGISTIRME }
  ];

  aidiyetKodlariOptions = [
    { label: 'Emniyet Genel Müdürlüğü', value: 'EGM' },
    { label: 'Milli İstihbarat Teşkilatı', value: 'MIT' },
    { label: 'Jandarma Genel Komutanlığı', value: 'JGK' },
    { label: 'Sahil Güvenlik Komutanlığı', value: 'SGK' },
    { label: 'Gümrük ve Ticaret Bakanlığı', value: 'GTB' }
  ];

  constructor(
    private fb: FormBuilder,
    private talepService: TalepService,
    private messageService: MessageService,
    private router: Router
  ) {
    this.talepForm = this.createForm();
  }

  ngOnInit(): void {
    // Component initialization
  }

  private createForm(): FormGroup {
    return this.fb.group({
      // EvrakDetay fields
      evrakNo: ['', [Validators.required, Validators.maxLength(50)]],
      evrakTarihi: [new Date(), Validators.required],
      evrakKurumKodu: ['', Validators.required],
      evrakTuru: [EvrakTuru.ILETISIMIN_DENETLENMESI, Validators.required],
      havaleBirimi: [''],
      evrakAciklama: [''],
      geldigiIlIlceKodu: ['', Validators.required],
      acilmi: [false],
      evrakKonusu: [''],

      // MahkemeKararBilgisi fields
      mahkemeKararTipi: [MahkemeKararTip.MAHKEME_AIDIYAT_DEGISTIRME, Validators.required],
      
      // MahkemeKararDetay fields
      mahkemeKodu: ['', Validators.required],
      mahkemeKararNo: ['', Validators.required],
      mahkemeIlIlceKodu: ['', Validators.required],
      sorusturmaNo: [''],
      mahkemeAciklama: [''],

      // Aidiyet specific fields
      hedefNo: ['', Validators.required],
      eskiAidiyetKodu: ['', Validators.required],
      yeniAidiyetKodlari: [[], Validators.required],
      aidiyetDegisiklikGerekce: ['', Validators.required]
    });
  }

  onDosyaSecildi(event: any): void {
    const dosyalar = event.files;
    if (dosyalar && dosyalar.length > 0) {
      this.seciliDosya = dosyalar[0];
      this.messageService.add({
        severity: 'info',
        summary: 'Dosya Seçildi',
        detail: `${this.seciliDosya?.name} dosyası seçildi`
      });
    }
  }

  onSubmit(): void {
    if (this.talepForm.valid && this.seciliDosya) {
      this.yukleniyor = true;
      
      const formData = this.talepForm.value;
      
      // Create request object
      const request: IDAidiyatBilgisiGuncellemeRequest = {
        id: this.generateUUID(),
        kararTuru: KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME,
        evrakDetay: {
          evrakNo: formData.evrakNo,
          evrakTarihi: formData.evrakTarihi.toISOString(),
          evrakKurumKodu: formData.evrakKurumKodu,
          evrakTuru: formData.evrakTuru,
          havaleBirimi: formData.havaleBirimi,
          aciklama: formData.evrakAciklama,
          geldigiIlIlceKodu: formData.geldigiIlIlceKodu,
          acilmi: formData.acilmi,
          evrakKonusu: formData.evrakKonusu
        },
        mahkemeKararBilgisi: {
          mahkemeKararTipi: formData.mahkemeKararTipi,
          mahkemeKararDetay: {
            mahkemeKodu: formData.mahkemeKodu,
            mahkemeKararNo: formData.mahkemeKararNo,
            mahkemeIlIlceKodu: formData.mahkemeIlIlceKodu,
            sorusturmaNo: formData.sorusturmaNo,
            aciklama: formData.mahkemeAciklama
          }
        },
        // Backend'in beklediği nested yapı
        aidiyatGuncellemeKararDetayListesi: [
          {
            mahkemeKararDetay: {
              mahkemeKodu: formData.mahkemeKodu,
              mahkemeKararNo: formData.mahkemeKararNo,
              mahkemeIlIlceKodu: formData.mahkemeIlIlceKodu,
              sorusturmaNo: formData.sorusturmaNo,
              aciklama: formData.mahkemeAciklama
            },
            aidiyatGuncellemeDetayList: [
              {
                guncellemeTip: GuncellemeTip.EKLE,
                hedefAidiyatDetay: {
                  hedef: {
                    hedefNo: formData.hedefNo,
                    hedefTipi: 'GSM',
                    baslamaTarihi: new Date().toISOString(),
                    sureTipi: 'AY',
                    bimAidiyatKod: formData.yeniAidiyetKodlari[0] || formData.eskiAidiyetKodu
                  },
                  aidiyatKodu: formData.yeniAidiyetKodlari[0] || formData.eskiAidiyetKodu
                }
              }
            ]
          }
        ]
      };

      // Send request
      this.talepService.aidiyetBilgisiGuncelle(request, this.seciliDosya).subscribe({
        next: (response) => {
          this.yukleniyor = false;
          this.messageService.add({
            severity: 'success',
            summary: 'Başarılı',
            detail: 'Aidiyet bilgisi güncelleme talebi başarıyla gönderildi'
          });
          this.talepForm.reset();
          this.seciliDosya = null;
        },
        error: (error) => {
          this.yukleniyor = false;
          this.messageService.add({
            severity: 'error',
            summary: 'Hata',
            detail: 'Talep gönderilirken hata oluştu: ' + error.message
          });
        }
      });
    } else {
      let errorMessage = 'Lütfen kontrol edin: ';
      if (!this.talepForm.valid) errorMessage += 'Form alanları, ';
      if (!this.seciliDosya) errorMessage += 'Dosya seçimi ';
      
      this.messageService.add({
        severity: 'warn',
        summary: 'Uyarı',
        detail: errorMessage
      });
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.talepForm.controls).forEach(key => {
      const control = this.talepForm.get(key);
      control?.markAsTouched();
    });
  }

  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  onReset(): void {
    this.talepForm.reset();
    this.talepForm.patchValue({
      evrakTuru: EvrakTuru.ILETISIMIN_DENETLENMESI,
      mahkemeKararTipi: MahkemeKararTip.MAHKEME_AIDIYAT_DEGISTIRME,
      evrakTarihi: new Date()
    });
    this.seciliDosya = null;
    this.messageService.add({
      severity: 'info',
      summary: 'Form Sıfırlandı',
      detail: 'Tüm alanlar temizlendi'
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.talepForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.talepForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) {
        return 'Bu alan zorunludur';
      }
      if (field.errors['maxlength']) {
        return `Maksimum ${field.errors['maxlength'].requiredLength} karakter olmalıdır`;
      }
    }
    return '';
  }

  getAidiyetKoduLabel(value: string): string {
    const option = this.aidiyetKodlariOptions.find(opt => opt.value === value);
    return option ? option.label : value;
  }
}
