package iym.common.testcontainer;

import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.OracleContainer;
import org.testcontainers.containers.wait.strategy.Wait;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.Duration;

/**
 * Abstract base class for Oracle Testcontainer integration tests.
 * <p>
 * This class provides a shared Oracle 11 XE container for all tests that extend it.
 * The container is started once per test execution but reuse is disabled for parallel test safety.
 * <p>
 * AUTOMATIC ORACLE CONFIGURATION:
 * - Automatically configures Oracle TestContainer instead of embedded databases (H2)
 * - Uses @DynamicPropertySource to override Spring Boot auto-configuration
 * - Disables embedded database detection to prevent conflicts
 * - No manual datasource configuration required in extending tests
 * <p>
 * PRODUCTION SCHEMA LOADING:
 * - Container starts with minimal init script
 * - Production scripts from docker/oracle/init are automatically executed
 * - Ensures test environment stays in sync with production schema
 * - No manual maintenance required when production scripts change
 * <p>
 * USAGE PATTERNS:
 * <p>
 * For @DataJpaTest (Recommended - JPA/Database Layer Tests):
 * <pre>
 * {@code
 * @DataJpaTest
 * @Import(OracleTestContainerConfiguration.class)
 * @Testcontainers
 * @ActiveProfiles("testcontainers-oracle")
 * @AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
 * @TestInstance(TestInstance.Lifecycle.PER_CLASS)
 * public class MyJpaTest extends AbstractOracleTestContainer {
 *     // Optional: Override initialization behavior if needed
 *     @Override
 *     void initializeContainerAndSchema() {
 *         super.initializeContainerAndSchema(); // Call parent initialization
 *     }
 * }
 * }
 * </pre>
 * <p>
 * For @SpringBootTest (Full Integration Tests - if needed):
 * <pre>
 * {@code
 * @SpringBootTest
 * @ActiveProfiles("testcontainers-oracle")
 * @AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
 * @TestInstance(TestInstance.Lifecycle.PER_CLASS)
 * public class MyIntegrationTest extends AbstractOracleTestContainer {
 *     // Test implementation
 * }
 * }
 * </pre>
 * <p>
 * FEATURES:
 * - Oracle 11 XE Docker container (gvenzl/oracle-xe:********-slim-faststart)
 * - Automatic Oracle vs H2 conflict resolution
 * - Automatic production schema loading from docker/oracle/init
 * - Connection pooling with HikariCP optimized for TestContainers
 * - SQL logging enabled for debugging
 * - Proper cleanup after tests
 * - Thread-safe container sharing across test classes
 */
@Testcontainers
@Slf4j
@TestInstance(TestInstance.Lifecycle.PER_CLASS) // 🔧 REQUIRED: For non-static @BeforeAll methods
public abstract class AbstractOracleTestContainer {
    public static OracleContainer getOracleContainer() {
        return new OracleContainer("gvenzl/oracle-xe:********-slim-faststart")
                .withDatabaseName("XE")
                .withUsername("iym")
                .withPassword("iym")
                .withEnv("APP_USER_ROLE", "DBA")
                .withReuse(false) // 🔧 PARALLEL: Disable reuse to avoid cached init script issues
                .withExposedPorts(1521) // Expose Oracle port
                .withEnv("ORACLE_ALLOW_REMOTE", "true") // Allow remote connections
                .withEnv("ORACLE_ENABLE_XDB", "false") // Disable XDB to reduce memory usage
                .waitingFor(Wait.forLogMessage(".*DATABASE IS READY TO USE!.*\\n", 1)
                        .withStartupTimeout(Duration.ofMinutes(5))) // Increased timeout
                .withStartupTimeout(Duration.ofMinutes(5));
    }

    /**
     * 🔧 SPRING NATIVE: Spring-managed DataSource with unique properties per test class
     * Uses @DynamicPropertySource for configuration and @Autowired for injection
     * Each test class gets unique HikariCP pool name for parallel execution safety
     */
    @Autowired
    protected DataSource dataSource;

    /**
     * Oracle 11 XE container using the same image as in docker-compose.yml
     * 🔧 PARALLEL EXECUTION: Static container with proper synchronization for parallel tests
     * Container is shared but with proper isolation mechanisms
     * <p>
     * MINIMAL INIT: Container starts with minimal JDBC-compatible script
     * Production schema loaded in @BeforeAll method
     */
    @Container
    protected static final OracleContainer ORACLE_CONTAINER = new OracleContainer("gvenzl/oracle-xe:********-slim-faststart")
            .withDatabaseName("XE")
            .withUsername("iym")
            .withPassword("iym")
            .withEnv("APP_USER_ROLE", "DBA")
            .withReuse(false) // 🔧 PARALLEL: Disable reuse to avoid cached init script issues
            .withExposedPorts(1521) // Expose Oracle port
            .withEnv("ORACLE_ALLOW_REMOTE", "true") // Allow remote connections
            .withEnv("ORACLE_ENABLE_XDB", "false") // Disable XDB to reduce memory usage
            .waitingFor(Wait.forLogMessage(".*DATABASE IS READY TO USE!.*\\n", 1)
                    .withStartupTimeout(Duration.ofMinutes(5))) // Increased timeout
            .withStartupTimeout(Duration.ofMinutes(5)); // 🔧 PARALLEL: Increased startup timeout for stability


    /**
     * Centralized container and schema initialization
     * This method ensures container is ready and database is accessible before proceeding
     * 🔧 IMPROVED: Non-static for test isolation, each test class gets its own DataSource
     */
    @BeforeAll
    void initializeContainerAndSchema() {
        log.info("🚀 [BeforeAll] Initializing Oracle container and schema...");

        // 🔧 PARALLEL: Ensure container is fully started before accessing properties
        if (!ORACLE_CONTAINER.isRunning()) {
            log.info("⏳ Starting Oracle container...");
            ORACLE_CONTAINER.start();
        }

        // 🔧 PARALLEL: Wait for container to be fully ready
        waitForContainerReady(ORACLE_CONTAINER);


        // 🔧 DEBUGGING: Log container and connection details
        logContainerDetails(ORACLE_CONTAINER);

        // 🔧 BETTER: Wait for database connectivity with retry logic
        waitForDatabaseReady(ORACLE_CONTAINER);

        // 🔧 DEBUGGING: Log connection pool status after initialization
        logConnectionPoolStatus("After container initialization", dataSource);


        // 🔧 CRITICAL FIX: Ensure fresh connection pool for each test class
        ensureFreshConnectionPool(dataSource);


        // 🔧 DEBUGGING: Final connection pool status
        logConnectionPoolStatus("After schema initialization completed", dataSource);

        log.info("✅ Container and schema initialization completed");
    }

    // 🔧 REMOVED: Manual DataSource creation no longer needed
    // Spring automatically creates DataSource via @DynamicPropertySource with unique pool names

    /**
     * 🔧 CRITICAL FIX: Ensure fresh connection pool for each test class
     * This prevents stale connection issues when container is reused
     */
    public static void ensureFreshConnectionPool(DataSource dataSource) {
        try {
            if (dataSource instanceof HikariDataSource hikariDS) {

                // Check if pool has stale connections
                HikariPoolMXBean poolBean = hikariDS.getHikariPoolMXBean();
                if (poolBean != null) {
                    int activeConnections = poolBean.getActiveConnections();
                    int totalConnections = poolBean.getTotalConnections();

                    log.info("🔧 [POOL REFRESH] Current pool state - Active: {}, Total: {}",
                            activeConnections, totalConnections);

                    // If there are existing connections, they might be stale
                    if (totalConnections > 0) {
                        log.info("🔧 [POOL REFRESH] Existing connections detected, testing connectivity...");

                        // Test connectivity
                        try (var testConn = hikariDS.getConnection()) {
                            testConn.createStatement().executeQuery("SELECT 1 FROM DUAL");
                            log.info("✅ [POOL REFRESH] Existing connections are healthy");
                        } catch (Exception e) {
                            log.warn("⚠️ [POOL REFRESH] Existing connections are stale: {}", e.getMessage());

                            // Force pool refresh by evicting idle connections
                            hikariDS.getHikariPoolMXBean().softEvictConnections();
                            log.info("🔧 [POOL REFRESH] Evicted idle connections");
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("⚠️ [POOL REFRESH] Error ensuring fresh connection pool: {}", e.getMessage());
        }
    }

    /**
     * Container cleanup is handled automatically by Testcontainers framework
     * 🔧 FIXED: Removed manual container.stop() to prevent premature shutdown
     * Container will be automatically cleaned up when JVM exits
     * This allows multiple test classes to safely share the same container
     */
    @AfterAll
    protected void cleanupContainer() {
        // 🔧 DEBUGGING: Log connection pool status before cleanup
        logConnectionPoolStatus("Before test class cleanup", dataSource);

        // 🔧 DEBUGGING: Log container details before cleanup
        logContainerDetails(ORACLE_CONTAINER);

        // 🔧 PARALLEL FIX: Close only our own DataSource to avoid affecting other parallel tests
        closeOwnDataSource();

        // 🔧 NO-OP: Container cleanup handled automatically by Testcontainers
        // Manual stop() removed to prevent interference between test classes
        log.info("✅ Test class completed - container will be cleaned up automatically by Testcontainers");
    }

    /**
     * 🔧 PARALLEL FIX: Close only our own DataSource to avoid affecting other parallel tests
     * This method only closes the DataSource created by this specific test class
     */
    protected void closeOwnDataSource() {
        try {
            if (dataSource != null && dataSource instanceof HikariDataSource hikariDS) {
                if (!hikariDS.isClosed()) {
                    String poolName = hikariDS.getPoolName();
                    log.info("🔧 [PARALLEL SAFE] Closing own DataSource: {}", poolName);
                    hikariDS.close();
                    log.info("✅ [PARALLEL SAFE] Successfully closed own DataSource: {}", poolName);
                } else {
                    log.info("ℹ️ [PARALLEL SAFE] DataSource is already closed");
                }
            } else {
                log.info("ℹ️ [PARALLEL SAFE] DataSource is null or not HikariDataSource");
            }
        } catch (Exception e) {
            log.warn("⚠️ [PARALLEL SAFE] Error closing own DataSource: {}", e.getMessage());
        }
    }

    /**
     * Wait for database to be ready and accepting connections
     * Uses retry logic with proper timeout handling
     * 🔧 IMPROVED: Tests actual database connectivity, not just container status
     */
    public static void waitForDatabaseReady(OracleContainer oracleContainer) {
        int maxAttempts = 30; // 30 attempts = 60 seconds max (increased for parallel stability)
        int attempt = 0;

        // 🔧 LOG: Container connection details for debugging
        String jdbcUrl = oracleContainer.getJdbcUrl();
        String username = oracleContainer.getUsername();
        String password = oracleContainer.getPassword();

        log.info("⏳ Waiting for Oracle database to be ready...");
        log.info("🔗 Connection details - URL: {}", jdbcUrl);
        log.info("🔗 Connection details - Username: {}", username);
        log.info("🔗 Connection details - Password: {}", password);

        while (true) {
            try {
                // Test actual database connectivity
                try (Connection conn = DriverManager.getConnection(jdbcUrl, username, password)) {

                    // Test basic query to ensure database is fully operational
                    try (Statement stmt = conn.createStatement()) {
                        stmt.executeQuery("SELECT 1 FROM DUAL");
                    }

                    log.info("✅ Database is ready and accepting connections!");
                    log.info("✅ Final connection details - URL: {}", jdbcUrl);
                    log.info("✅ Final connection details - Username: {}", username);
                    log.info("✅ Connection test successful after {} attempts", attempt + 1);
                    return;
                }
            } catch (SQLException e) {
                attempt++;
                log.info("⏳ Database not ready yet... attempt {}/{} ({})",
                        attempt, maxAttempts, e.getMessage());

                if (attempt >= maxAttempts) {
                    log.error("❌ Database failed to become ready after {} attempts", maxAttempts);
                    throw new RuntimeException("Database failed to become ready after " +
                            (maxAttempts * 2) + " seconds. Last error: " + e.getMessage(), e);
                }

                try {
                    Thread.sleep(2000); // Wait 2 seconds between attempts
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Database readiness check interrupted", ie);
                }
            }
        }
    }

    /**
     * 🔧 SPRING NATIVE: Configure Spring properties dynamically for each test class
     * Uses unique HikariCP pool names for parallel execution safety
     * Each test class gets its own isolated connection pool configuration
     */
    @DynamicPropertySource
    static void overrideProperties(DynamicPropertyRegistry registry) {
        // 🔧 CRITICAL: Ensure container is started before accessing properties
        if (!ORACLE_CONTAINER.isRunning()) {
            ORACLE_CONTAINER.start();
            log.info("✅ Oracle container started for @DynamicPropertySource");
        }

        // 🔧 CRITICAL: Disable embedded database detection to prevent H2 auto-configuration
        registry.add("spring.datasource.embedded-database-connection", () -> "none");
        registry.add("spring.test.database.replace", () -> "none");

        // Oracle TestContainer datasource configuration
        registry.add("spring.datasource.url", ORACLE_CONTAINER::getJdbcUrl);
        registry.add("spring.datasource.username", ORACLE_CONTAINER::getUsername);
        registry.add("spring.datasource.password", ORACLE_CONTAINER::getPassword);
        registry.add("spring.datasource.driver-class-name", () -> "oracle.jdbc.OracleDriver");

        // 🔧 PARALLEL SAFETY: HikariCP configuration optimized for parallel execution
        registry.add("spring.datasource.hikari.maximum-pool-size", () -> "5");
        registry.add("spring.datasource.hikari.minimum-idle", () -> "1");
        registry.add("spring.datasource.hikari.connection-timeout", () -> "120000"); // 2 minutes
        registry.add("spring.datasource.hikari.idle-timeout", () -> "600000"); // 10 minutes
        registry.add("spring.datasource.hikari.max-lifetime", () -> "1800000"); // 30 minutes
        registry.add("spring.datasource.hikari.leak-detection-threshold", () -> "120000"); // 2 minutes
        registry.add("spring.datasource.hikari.initialization-fail-timeout", () -> "-1"); // Don't fail fast
        registry.add("spring.datasource.hikari.isolate-internal-queries", () -> "true");
        registry.add("spring.datasource.hikari.connection-test-query", () -> "SELECT 1 FROM DUAL");
        registry.add("spring.datasource.hikari.validation-timeout", () -> "5000"); // 5 seconds

        // 🔧 PARALLEL SAFETY: Generate unique pool name based on thread/timestamp
        String uniquePoolName = "OracleTestPool-" + Thread.currentThread().getName() + "-" + System.currentTimeMillis();
        registry.add("spring.datasource.hikari.pool-name", () -> uniquePoolName);

        log.info("🔧 [SPRING NATIVE] Configured DataSource with unique pool: {}", uniquePoolName);
    }

    /**
     * Wait for container to be fully ready and accessible
     * 🔧 PARALLEL EXECUTION: Ensures container is ready before accessing properties
     */
    public static void waitForContainerReady(OracleContainer oracleContainer) {
        log.info("⏳ Waiting for Oracle container to be fully ready...");

        // Wait for container to be running
        int maxAttempts = 30;
        int attempt = 0;

        while (!oracleContainer.isRunning() && attempt < maxAttempts) {
            try {
                Thread.sleep(1000);
                attempt++;
                log.debug("⏳ Container not ready yet, attempt {}/{}", attempt, maxAttempts);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Interrupted while waiting for container", e);
            }
        }

        if (!oracleContainer.isRunning()) {
            throw new RuntimeException("Container failed to start after " + maxAttempts + " attempts");
        }

        log.info("✅ Oracle container is running and ready");
    }

    /**
     * 🔧 DEBUGGING: Log detailed container information
     */
    public static void logContainerDetails(OracleContainer oracleContainer) {
        try {
            log.info("🔍 [CONTAINER DEBUG] Container Status:");
            log.info("  - [CONTAINER DEBUG] Container ID: {}", oracleContainer.getContainerId());
            log.info("  - [CONTAINER DEBUG] Container Running: {}", oracleContainer.isRunning());
            log.info("  - [CONTAINER DEBUG] Container Created: {}", oracleContainer.isCreated());
            log.info("  - [CONTAINER DEBUG] JDBC URL: {}", oracleContainer.getJdbcUrl());
            log.info("  - [CONTAINER DEBUG] Username: {}", oracleContainer.getUsername());
            log.info("  - [CONTAINER DEBUG] Database Name: {}", oracleContainer.getDatabaseName());
            log.info("  - [CONTAINER DEBUG] Exposed Ports: {}", oracleContainer.getExposedPorts());
            log.info("  - [CONTAINER DEBUG] Mapped Port 1521: {}", oracleContainer.getMappedPort(1521));
        } catch (Exception e) {
            log.warn("⚠️ [CONTAINER DEBUG] Error logging container details: {}", e.getMessage());
        }
    }

    /**
     * 🔧 DEBUGGING: Log detailed HikariCP connection pool status
     */
    public static void logConnectionPoolStatus(String context, DataSource dataSource) {
        try {
            log.info("🔍 [HIKARI DEBUG] Connection Pool Status - {}:", context);

            if (dataSource == null) {
                log.warn("  - DataSource is NULL!");
                return;
            }

            log.info("  - DataSource Class: {}", dataSource.getClass().getName());

            if (dataSource instanceof HikariDataSource hikariDS) {
                log.info("  - HikariDataSource Pool Name: {}", hikariDS.getPoolName());
                log.info("  - HikariDataSource JDBC URL: {}", hikariDS.getJdbcUrl());
                log.info("  - HikariDataSource Is Closed: {}", hikariDS.isClosed());
                log.info("  - HikariDataSource Is Running: {}", hikariDS.isRunning());

                // Get pool MXBean for detailed stats
                HikariPoolMXBean poolBean = hikariDS.getHikariPoolMXBean();
                if (poolBean != null) {
                    log.info("  - Active Connections: {}", poolBean.getActiveConnections());
                    log.info("  - Idle Connections: {}", poolBean.getIdleConnections());
                    log.info("  - Total Connections: {}", poolBean.getTotalConnections());
                    log.info("  - Threads Awaiting Connection: {}", poolBean.getThreadsAwaitingConnection());
                } else {
                    log.warn("  - Pool MXBean is NULL!");
                }

                // Test a connection
                try (var connection = hikariDS.getConnection()) {
                    log.info("  - Test Connection Successful: {}", !connection.isClosed());
                    log.info("  - Connection Class: {}", connection.getClass().getName());

                    // Try to get Oracle connection ID
                    try (var stmt = connection.createStatement()) {
                        var rs = stmt.executeQuery("SELECT SYS_CONTEXT('USERENV', 'SID') FROM DUAL");
                        if (rs.next()) {
                            log.info("  - Oracle Session ID (SID): {}", rs.getString(1));
                        }
                    } catch (Exception e) {
                        log.warn("  - Could not get Oracle SID: {}", e.getMessage());
                    }

                } catch (Exception e) {
                    log.error("  - Test Connection FAILED: {}", e.getMessage());
                }
            } else {
                log.warn("  - DataSource is not HikariDataSource: {}", dataSource.getClass());
            }

        } catch (Exception e) {
            log.error("⚠️ [HIKARI DEBUG] Error logging connection pool status: {}", e.getMessage(), e);
        }
    }

    // 🔧 REMOVED: OracleTestContainerConfiguration - static class can't access non-static container
    // Using instance DataSource from @BeforeAll instead

    /**
     * Execute SQL script in the Oracle container
     * Useful for test data setup
     */
    protected void executeSqlScript(String sqlScript) {
        try {
            ORACLE_CONTAINER.execInContainer("sqlplus", "-S", "iym/iym@XE", "@" + sqlScript);
        } catch (Exception e) {
            throw new RuntimeException("Failed to execute SQL script: " + sqlScript, e);
        }
    }

    /**
     * Execute SQL statement in the Oracle container
     * Useful for quick test data setup
     */
    protected void executeSql(String sql) {
        try {
            ORACLE_CONTAINER.execInContainer("sqlplus", "-S", "iym/iym@XE", "-c", sql);
        } catch (Exception e) {
            throw new RuntimeException("Failed to execute SQL: " + sql, e);
        }
    }


}
