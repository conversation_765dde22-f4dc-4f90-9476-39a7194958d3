package iym.common.service.db.mk;

import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.model.entity.iym.mk.HedeflerIslem;
import iym.common.service.db.GenericDbService;

import java.util.List;

/**
 * Service interface for HedeflerIslem entity
 */
public interface DbHedeflerIslemService extends GenericDbService<HedeflerIslem, Long> {

    List<HedeflerIslem> findByMahkemeKararId(Long mahkemeKararId);

}
