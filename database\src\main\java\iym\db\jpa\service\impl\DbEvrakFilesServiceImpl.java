package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.EvrakFiles;
import iym.common.service.db.DbEvrakFilesService;
import iym.db.jpa.dao.EvrakFilesRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service implementation for EvrakFiles entity
 */
@Service
public class DbEvrakFilesServiceImpl extends GenericDbServiceImpl<EvrakFiles, Long> implements DbEvrakFilesService {

    private final EvrakFilesRepo evrakFilesRepo;

    @Autowired
    public DbEvrakFilesServiceImpl(EvrakFilesRepo repository) {
        super(repository);
        this.evrakFilesRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakFiles> findByEvrakId(Long evrakId) {
        return evrakFilesRepo.findByEvrakId(evrakId);
    }

}
