// IYM (İletiş<PERSON>in <PERSON>) Model Tanımları

// Mahkeme Karar Request Modelleri
export interface MahkemeKararRequest {
  id: string;
  kararTuru: KararTuru;
  evrakDetay: EvrakDetay;
  mahkemeKararBilgisi: MahkemeKararBilgisi;
}

export interface EvrakDetay {
  evrakNo: string;
  evrakTarihi: string; // LocalDateTime as ISO string
  evrakKurumKodu: string;
  evrakTuru: EvrakTuru;
  havaleBirimi?: string;
  aciklama?: string;
  geldigiIlIlceKodu: string;
  acilmi: boolean;
  evrakKonusu?: string;
}

export interface MahkemeKararBilgisi {
  mahkemeKararTipi: MahkemeKararTip;
  mahkemeKararDetay: MahkemeKararDetay;
}

export interface MahkemeKararDetay {
  mahkemeKodu: string;
  mahkemeKararNo: string;
  mahkemeIlIlceKodu: string;
  sorusturmaNo?: string;
  aciklama?: string;
}

export interface HedefDetayID {
  hedefNo: string;
  hedefTipi: string;
  hedefAdi?: string;
  hedefSoyadi?: string;
  baslamaTarihi: string;
  suresi?: string;
  sureTipi: string;
  bimAidiyatKod: string;
  canakNo?: string;
}

export interface HedefDetayIT {
  sorguTipi: string;
  hedefNo: string;
  karsiHedefNo?: string;
  baslangicTarihi: string;
  bitisTarihi: string;
  tespitTuru: string;
  tespitTuruDetay?: string;
  aciklama?: string;
}

// Specific Request Types
export interface IDYeniKararRequest extends MahkemeKararRequest {
  hedefDetayListesi: HedefDetayID[];
  mahkemeAidiyatKodlari?: string[];
  mahkemeSucTipiKodlari?: string[];
}

export interface IDUzatmaKarariRequest extends MahkemeKararRequest {
  hedefDetayListesi: HedefDetayID[];
  mahkemeAidiyatKodlari?: string[];
  mahkemeSucTipiKodlari?: string[];
}

export interface IDSonlandirmaKarariRequest extends MahkemeKararRequest {
  hedefDetayListesi: HedefDetayID[];
  mahkemeAidiyatKodlari?: string[];
  mahkemeSucTipiKodlari?: string[];
}

export interface ITKararRequest extends MahkemeKararRequest {
  hedefDetayListesi: HedefDetayIT[];
}

// Güncelleme request'leri dosyanın sonunda tanımlanmıştır

export interface GenelEvrakRequest extends MahkemeKararRequest {
  // General document request
}

// Enums
export enum KararTuru {
  ILETISIMIN_DENETLENMESI_YENI_KARAR = 0,
  ILETISIMIN_DENETLENMESI_UZATMA_KARARI = 1,
  ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI = 2,
  ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME = 3,
  ILETISIMIN_DENETLENMESI_HEDEF_ADSOYAD_GUNCELLEME = 4,
  ILETISIMIN_DENETLENMESI_MAHKEMEKODU_GUNCELLEME = 5,
  ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME = 6,
  ILETISIMIN_TESPITI = 7,
  GENEL_EVRAK = 8
}

export enum EvrakTuru {
  ILETISIMIN_DENETLENMESI = 'ILETISIMIN_DENETLENMESI',
  ILETISIMIN_TESPITI = 'ILETISIMIN_TESPITI'
}

export enum MahkemeKararTip {
  ONLEYICI_HAKIM_KARARI = 100,
  SINYAL_BILGI_DEGERLENDIRME_KARARI = 150,
  ABONE_KUTUK_BILGILERI_KARARI = 151,
  ONLEYICI_YAZILI_EMIR = 200,
  ADLI_HAKIM_KARARI = 300,
  ADLI_HAKIM_HTS_KARARI = 350,
  ADLI_YAZILI_EMIR = 400,
  ADLI_KHK_YAZILI_EMIR = 410,
  ADLI_SAVCILIK_HTS_KARARI = 450,
  HEDEF_AD_SOYAD_DEGISTIRME = 510,
  MAHKEME_KODU_DEGISTIRME = 520,
  MAHKEME_AIDIYAT_DEGISTIRME = 530,
  CANAK_NUMARA_DEGISTIRME = 599,
  ONLEYICI_SONLANDIRMA = 600,
  ADLI_SONLANDIRMA = 700,
  ADLI_SAVCILIK_SONLANDIRMA = 710,
  ADLI_SAVCILIK_YER_TESPITI_SONLANDIRMA = 720,
  ADLI_KHK_SONLANDIRMA = 730,
  ADLI_ASKERI_HAKIM_KARARI = 800,
  ADLI_ASKERI_SONLANDIRMA = 900,
  ADLI_ASKERI_SAVCILIK_SONLANDIRMA = 910,
  ADLI_ASKERI_YER_TESPITI_SONLANDIRMA = 920
}

export interface EvrakKayit {
  xmlVersiyon: string;
  evrakNo: string;
  evrakTarihi: string;
  evrakGeldigiKurum: string;
  evrakTipi: 'ILETISIMIN_DENETLENMESI' | 'ILETISIMIN_TESPITI';
  havaleBirim: string;
  aciklama?: string;
  gelIl: string;
  evrakKonusu?: string;
  acilmi: 'H' | 'E'; // Hayır/Evet
  mahkemeKarar: MahkemeKarar;
}

export interface MahkemeKarar {
  kararTip: string;
  mahkemeKodu: string;
  mahkemeKararNo: string;
  mahkemeIli: string;
  aciklama?: string;
  sorusturmaNo?: string;
  hedefler?: Hedef[];
  itkHedefler?: ItkHedef[];
  mahkemeAidiyat?: MahkemeAidiyat[];
  mahkemeSucTipi?: MahkemeSucTipi[];
}

export interface Hedef {
  hedefNo: string;
  hedefTipi: string;
  hedefAdi?: string;
  hedefSoyadi?: string;
  baslamaTarihi: string;
  suresi?: string;
  sureTipi: string;
  bimAidiyatKod: string;
  canakNo?: string;
}

export interface ItkHedef {
  sorguTipi: string;
  hedefNo: string;
  karsiHedefNo?: string;
  baslangicTarihi: string;
  bitisTarihi: string;
  tespitTuru: string;
  tespitTuruDetay?: string;
  aciklama?: string;
}

export interface MahkemeAidiyat {
  aidiyatKod: string;
}

export interface MahkemeSucTipi {
  sucTipKod: string;
}

// Karar Tipleri Enum
export enum KararTipleri {
  ONLEYICI_HAKIM_KARARI = '100',
  ONLEYICI_YAZILI_EMIR = '200',
  ADLI_HAKIM_KARARI = '300',
  ADLI_YAZILI_EMIR = '400',
  ADLI_KHK_YAZILI_EMIR = '410',
  BILGI_ALMA = '500',
  ONLEYICI_SONLANDIRMA = '600',
  ADLI_SONLANDIRMA = '700',
  ADLI_KHK_SONLANDIRMA = '730',
  ADLI_ASKERI_HAKIM_KARARI = '800',
  ADLI_ASKERI_SONLANDIRMA = '900',
  ONLEYICI_HAKIM_T_KARAR = '150'
}

// Hedef Tipleri Enum
export enum HedefTipleri {
  GSM = '10',
  SABIT = '20',
  UYDU = '30',
  YURT_DISI = '40',
  UMTH_MSISDN = '41',
  UMTH_USERNAME = '42',
  UMTH_IP = '43',
  UMTH_PINCODE = '44',
  EPOSTA = '50',
  IP_TAKIP = '51',
  URL_WEB_ADRESI_TAKIP = '52',
  ADSL_ABONE_TAKIP = '53',
  GPRS = '54',
  IP_ENGELLEME = '55',
  DOMAIN_ENGELLEME = '56',
  IMEI = '60',
  IMSI = '70',
  GPRS_IMSI = '71',
  TT_XDSL_MSISDN = '80',
  TT_XDSL_TEMOSNO = '81',
  TT_XDSL_USERNAME = '82',
  TT_XDSL_IP = '83',
  GPRS_GSM = '90',
  GPRS_IMEI = '91',
  GPRS_YURT_DISI = '92',
  GSM_YER_TESPITI = '200'
}

// Arama Filtreleri
export interface EvrakAramaFiltresi {
  evrakNo?: string;
  baslangicTarihi?: Date;
  bitisTarihi?: Date;
  mahkemeKodu?: string;
  evrakTipi?: string;
  kararTipi?: string;
  hedefNo?: string;
}

export interface ParametreAramaFiltresi {
  parametreAdi?: string;
  parametreGrubu?: string;
  aktifMi?: boolean;
}

// Sonuç Modelleri
export interface EvrakAramaSonucu {
  evrakNo: string;
  evrakTarihi: string;
  evrakTipi: string;
  mahkemeKodu: string;
  kararTipi: string;
  durumu: string;
  aciklama?: string;
}

export interface ParametreSonucu {
  id: number;
  parametreAdi: string;
  parametreDegeri: string;
  parametreGrubu: string;
  aciklama?: string;
  aktifMi: boolean;
  olusturmaTarihi: Date;
  guncellemeTarihi?: Date;
}

// XML İşlem Sonuçları
export interface XmlValidasyonSonucu {
  gecerliMi: boolean;
  hatalar: XmlHata[];
  uyarilar: XmlUyari[];
}

export interface XmlHata {
  satir: number;
  sutun: number;
  mesaj: string;
  seviye: 'ERROR' | 'WARNING' | 'INFO';
}

export interface XmlUyari {
  satir: number;
  sutun: number;
  mesaj: string;
  oneri?: string;
}

// Dosya Yükleme
export interface DosyaYuklemeResponse {
  basarili: boolean;
  dosyaAdi: string;
  dosyaBoyutu: number;
  yuklenmeTarihi: Date;
  hataMesaji?: string;
  validasyonSonucu?: XmlValidasyonSonucu;
}

// Sistem Parametreleri
export interface SistemParametresi {
  id: number;
  anahtar: string;
  deger: string;
  aciklama?: string;
  kategori: string;
  veriTipi: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE';
  zorunluMu: boolean;
  varsayilanDeger?: string;
}

// İstatistik Modelleri
export interface IymIstatistik {
  toplamEvrak: number;
  bekleyenEvrak: number;
  islenenEvrak: number;
  hatalıEvrak: number;
  bugunIslemler: number;
  sonIslemTarihi?: Date;
}

// Rapor Modelleri
export interface RaporParametresi {
  raporTipi: 'GUNLUK' | 'HAFTALIK' | 'AYLIK' | 'OZEL';
  baslangicTarihi: Date;
  bitisTarihi: Date;
  mahkemeKodu?: string;
  evrakTipi?: string;
  formatTipi: 'PDF' | 'EXCEL' | 'CSV';
}

export interface RaporSonucu {
  raporId: string;
  raporAdi: string;
  olusturmaTarihi: Date;
  dosyaYolu: string;
  dosyaBoyutu: number;
  indirmeLinki: string;
}

// İletişim tespiti modelleri
export interface IletisimTespitiFiltresi {
  evrakNo?: string;
  hedefBilgisi?: string;
  tespitiTuru?: string;
  durum?: string;
  baslangicTarihi?: Date;
  bitisTarihi?: Date;
}

export interface IletisimTespitiSonucu {
  id: number;
  evrakNo: string;
  mahkemeKodu: string;
  hedefBilgisi: string;
  tespitiTuru: string;
  durum: string;
  talepTarihi: Date;
  tamamlanmaTarihi?: Date;
  sonucBilgisi?: string;
  hataMesaji?: string;
  ilerlemeYuzdesi: number;
}

// ============================================================================
// GÜNCELLEME REQUEST'LERİ - Backend'e uygun nested yapılar
// ============================================================================

// Güncelleme Tipi Enum
export enum GuncellemeTip {
  EKLE = 'EKLE',
  CIKAR = 'CIKAR'
}

// Aidiyet Güncelleme
export interface HedefAidiyatDetay {
  hedef: Hedef;
  aidiyatKodu: string;
}

export interface AidiyatGuncellemeDetay {
  guncellemeTip: GuncellemeTip;
  hedefAidiyatDetay: HedefAidiyatDetay;
}

export interface AidiyatGuncellemeKararDetay {
  mahkemeKararDetay: MahkemeKararDetay;
  aidiyatGuncellemeDetayList: AidiyatGuncellemeDetay[];
}

export interface IDAidiyatBilgisiGuncellemeRequest extends MahkemeKararRequest {
  aidiyatGuncellemeKararDetayListesi: AidiyatGuncellemeKararDetay[];
}

// Hedef Ad Soyad Güncelleme
export interface HedefWithAdSoyad {
  hedefNo: string;
  ad: string;
  soyad: string;
}

export interface HedefAdSoyadGuncellemeKararDetay {
  mahkemeKararDetay: MahkemeKararDetay;
  hedefAdSoyadListesi: HedefWithAdSoyad[];
}

export interface IDHedefAdSoyadGuncellemeRequest extends MahkemeKararRequest {
  hedefAdSoyadGuncellemeKararDetayListesi: HedefAdSoyadGuncellemeKararDetay[];
}

// Mahkeme Kodu Güncelleme
export interface MahkemeKoduGuncellemeDetay {
  mahkemeKararDetay: MahkemeKararDetay;
  yeniMahkemeKodu: string;
}

export interface IDMahkemeKoduGuncellemeRequest extends MahkemeKararRequest {
  mahkemeKoduGuncellemeDetayListesi: MahkemeKoduGuncellemeDetay[];
}

// Çanak Güncelleme
export interface CanakHedefDetay {
  hedef: Hedef;
  canakHedefNo: string;
}

export interface CanakGuncellemeDetay {
  guncellemeTip: GuncellemeTip;
  canakHedefDetay: CanakHedefDetay;
}

export interface CanakGuncellemeKararDetay {
  mahkemeKararDetay: MahkemeKararDetay;
  canakGuncellemeDetayList: CanakGuncellemeDetay[];
}

export interface IDCanakGuncellemeRequest extends MahkemeKararRequest {
  canakGuncellemeKararDetayListesi: CanakGuncellemeKararDetay[];
}
