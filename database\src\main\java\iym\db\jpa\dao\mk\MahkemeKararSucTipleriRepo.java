package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.MahkemeKararSucTipleri;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MahkemeKararSucRepo entity
 */
@Repository
public interface MahkemeKararSucTipleriRepo extends JpaRepository<MahkemeKararSucTipleri, Long> {

    List<MahkemeKararSucTipleri> findByMahkemeKararId(Long mahkemeKararId);

    Optional<MahkemeKararSucTipleri> findByMahkemeKararIdAndSucTipKodu(Long mahkemeKararId, String sucTipKodu);
}
