package iym.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum GuncellemeTip {
	EKLE(0),
	CIKAR(1);

	private final int guncellemeTip;

	GuncellemeTip(int guncellemeTip){
		this.guncellemeTip = guncellemeTip;
	}

	@JsonValue
	public int getGuncellemeTip(){
		return this.guncellemeTip;
	}

	@JsonCreator
	public static GuncellemeTip fromName(String name) {
		for (GuncellemeTip evrakTuru : GuncellemeTip.values()) {
			if (evrakTuru.name().equals(name)) {
				return evrakTuru;
			}
		}
		throw new IllegalArgumentException("Gecersiz guncellemeTip: '" + name + "'");
	}

	//@JsonCreator
	public static GuncellemeTip fromValue(int value) {
		for (GuncellemeTip evrakTuru : GuncellemeTip.values()) {
			if (evrakTuru.guncellemeTip == value) {
				return evrakTuru;
			}
		}
		throw new IllegalArgumentException("Gecersiz guncellemeTip: '" + value + "'");
	}
}
