package iym.common.service.db;

import iym.common.model.entity.iym.talep.HedeflerAidiyatTalep;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service interface for HedeflerAidiyatTalep entity
 */
public interface DbHedeflerAidiyatTalepService extends GenericDbService<HedeflerAidiyatTalep, Long> {

    Optional<HedeflerAidiyatTalep> findById(Long id);

    List<HedeflerAidiyatTalep> findByHedefTalepId(Long hedefTalepId);

    List<HedeflerAidiyatTalep> findByTarihBetween(Date startDate, Date endDate);
    
    Optional<HedeflerAidiyatTalep> findByHedefTalepIdAndAidiyatKod(Long hedefId, String aidiyatKod);
    

}
