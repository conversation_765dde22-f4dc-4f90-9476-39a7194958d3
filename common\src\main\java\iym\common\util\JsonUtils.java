package iym.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class JsonUtils {

    public static ObjectMapper getMapper() {
        return new Jackson2ObjectMapperBuilder()
                .modules(new JavaTimeModule())
                .featuresToEnable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES)
                .featuresToEnable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS)
                .featuresToEnable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
                .featuresToEnable(DeserializationFeature.ACCEPT_EMPTY_ARRAY_AS_NULL_OBJECT)
                .build();
    }

    public static <T> void serializeToFile(List<T> objects, String file) throws Exception {
        getMapper().writeValue(Paths.get(file).toFile(), objects);
    }

    public static String serialize(Object object) throws Exception {
        return getMapper().writeValueAsString(object);
    }

    public static <T> T deserialize(String json, Class<T> clazz) throws Exception {
        return getMapper().readValue(json, clazz);
    }

    public static <T> List<T> deserializeToList(String json, Class<T> clazz) throws Exception {
        ObjectMapper mapper = getMapper();
        return getMapper().readValue(json, mapper.getTypeFactory().constructCollectionType(List.class, clazz));
    }

    public static <T> List<T> deserializeFileToList(String file, Class<T> clazz) throws Exception {
        byte[] bytes = Files.readAllBytes(Paths.get(file));

        String content = new String(bytes);
        if (content.trim().isEmpty())
            return new ArrayList<>();

        ObjectMapper mapper = getMapper();
        return mapper.readValue(content, mapper.getTypeFactory().constructCollectionType(List.class, clazz));
    }

    public static <T> T deserializeFileToObject(String file, Class<T> clazz) throws Exception {
        byte[] bytes = Files.readAllBytes(Paths.get(file));
        String content = new String(bytes);

        if (content.trim().isEmpty()) {
            return null;
        }

        return getMapper().readValue(content, clazz);
    }

    public static String prettyPrintFromJsonString(String jsonStr){
        ObjectMapper objectMapper = getMapper();
        try {
            Object json = objectMapper.readValue(jsonStr, Object.class);
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(json);
        } catch (JsonProcessingException e) {
            log.error("Error printing object:{}", jsonStr, e);
            return "<Internal Error>";
        }
    }

    public static String prettyPrintFromJsonObject(Object jsonObject){
        ObjectMapper objectMapper = getMapper();
        try {
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonObject);
        } catch (JsonProcessingException e) {
            log.error("Error printing object:{}", jsonObject, e);
            return "<Internal Error>";
        }
    }

}
