package iym.common.util;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * HTTP utility class for shared/common module
 */
@Slf4j
public class HttpUtils {

    // Private constructor to hide the implicit public one
    private HttpUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * Get client IP address from HttpServletRequest
     * Checks various headers to handle proxy scenarios
     * 
     * @param request the HttpServletRequest
     * @return client IP address
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        String ipAddress = null;
        
        try {
            // Check X-Forwarded-For header (most common proxy header)
            ipAddress = request.getHeader("X-Forwarded-For");
            if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
                // Check Proxy-Client-IP header
                ipAddress = request.getHeader("Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
                // Check WL-Proxy-Client-IP header (WebLogic)
                ipAddress = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
                // Check HTTP_CLIENT_IP header
                ipAddress = request.getHeader("HTTP_CLIENT_IP");
            }
            if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
                // Check HTTP_X_FORWARDED_FOR header
                ipAddress = request.getHeader("HTTP_X_FORWARDED_FOR");
            }
            if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
                // Check X-Real-IP header (nginx)
                ipAddress = request.getHeader("X-Real-IP");
            }
            if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
                // Fall back to remote address
                ipAddress = request.getRemoteAddr();
            }
            
            // Handle multiple IPs in X-Forwarded-For header (comma-separated)
            if (ipAddress != null && ipAddress.contains(",")) {
                ipAddress = ipAddress.split(",")[0].trim();
            }
            
        } catch (Exception e) {
            log.error("Error getting client IP address", e);
            ipAddress = "unknown";
        }
        
        return ipAddress != null ? ipAddress : "unknown";
    }
} 