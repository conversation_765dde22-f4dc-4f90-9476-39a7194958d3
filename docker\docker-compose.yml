services:
  # PostgreSQL - Database for Backend service
  postgres:
    image: postgres:15
    container_name: iym-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: iym_db
      POSTGRES_USER: iym
      POSTGRES_PASSWORD: iym
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U iym -d iym_db"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  # Oracle - Database for Makos service
  oracle:
    image: gvenzl/oracle-xe:********-slim
    container_name: iym-oracle
    environment:
      - ORACLE_PASSWORD=oracle
      - ORACLE_DATABASE=XE
      - APP_USER=iym
      - APP_USER_PASSWORD=iym
    ports:
      - "1521:1521"
    volumes:
      - ./oracle/init:/docker-entrypoint-initdb.d
      - oracle-data:/opt/oracle/oradata
    healthcheck:
      test: ["CMD-SHELL", "sqlplus -L iym/iym@//localhost:1521/XE @/docker-entrypoint-initdb.d/S5_healthcheck.sql | grep -q 'Database is ready'"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped

  # Makos - Spring Boot application (works with Oracle database)
  makos:
    build:
      context: ..
      dockerfile: makos/Dockerfile
    container_name: iym-makos
    restart: unless-stopped
    ports:
      - "8081:8080"
    depends_on:
      oracle:
        condition: service_healthy
    environment:
      # Oracle connection information
      SPRING_DATASOURCE_URL: ********************************
      SPRING_DATASOURCE_USERNAME: iym
      SPRING_DATASOURCE_PASSWORD: iym
      # Other settings
      SPRING_PROFILES_ACTIVE: docker
      SERVER_PORT: 8080
      JAVA_OPTS: "-Xms512m -Xmx1024m"

  # Backend - Spring Boot application
  backend:
    build:
      context: ..
      dockerfile: backend/Dockerfile
    container_name: iym-backend
    restart: unless-stopped
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
      makos:
        condition: service_started
    environment:
      CORS_ALLOWED_ORIGINS: http://localhost:4201
      # PostgreSQL connection information
      SPRING_DATASOURCE_URL: **************************************
      SPRING_DATASOURCE_USERNAME: iym
      SPRING_DATASOURCE_PASSWORD: iym
      # Oracle connection information (for Makos module)
      MAKOS_DATASOURCE_URL: ********************************
      MAKOS_DATASOURCE_USERNAME: iym
      MAKOS_DATASOURCE_PASSWORD: iym
      # Makos API URL (for backend to access makos service)
      MAKOS_API_BASE_URL: http://makos:5000/makosapi
      # Other settings
      SPRING_PROFILES_ACTIVE: docker
      SERVER_PORT: 8080
      JAVA_OPTS: "-Xms512m -Xmx1024m"

  # Frontend - Angular application
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
    container_name: iym-frontend
    restart: unless-stopped
    ports:
      - "4200:80" # Angular prod build listens on port 80 via NGINX
    environment:
      # Dynamic API URL configuration
      API_URL: http://localhost:8080
      ENV_API_URL: http://localhost:8080
    depends_on:
      - backend

volumes:
  postgres_data:
    driver: local
  oracle-data:
    driver: local