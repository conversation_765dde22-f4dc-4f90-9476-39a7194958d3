-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MAH<PERSON>ME_AIDIYAT_ISLEM_SEQ if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = '<PERSON><PERSON><PERSON><PERSON>_AIDIYAT_ISLEM_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAHKEME_AIDIYAT_ISLEM_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create MAHKEME_AIDIYAT_ISLEM table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_AIDIYAT_ISLEM';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_AIDIYAT_ISLEM (
      ID NUMBER NOT NULL,
      <PERSON>H<PERSON>ME_ID NUMBER NOT NULL,
      AIDIYAT_KOD VARCHAR2(25) NOT NULL,
      DURUMU VARCHAR2(10),
      CONSTRAINT MAHKEME_AIDIYAT_ISLEM_ID_IDX PRIMARY KEY (ID) ENABLE
    )';

    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX iym.MAHKEME_AIDIYAT_ISLEM_ID ON iym.MAHKEME_AIDIYAT_ISLEM (MAHKEME_ID ASC, AIDIYAT_KOD ASC)';
  END IF;
END;
/



COMMIT;
