package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.MahkemeAidiyatDetayIslem;
import iym.common.model.entity.iym.talep.MahkemeAidiyatDetayTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;


@Repository
public interface MahkemeAidiyatDetayIslemRepo extends JpaRepository<MahkemeAidiyatDetayIslem, Long> {

    List<MahkemeAidiyatDetayIslem> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

}
