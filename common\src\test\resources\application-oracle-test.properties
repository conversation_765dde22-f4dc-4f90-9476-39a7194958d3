# Oracle Testcontainer Test Profile Configuration
# This profile is used for integration tests with Oracle Testcontainer
# The actual connection properties are set dynamically by AbstractOracleTestContainer
# Profile is activated via @ActiveProfiles("oracle-test") annotation in test classes

# JPA/Hibernate configuration for Oracle
spring.jpa.database-platform=org.hibernate.dialect.Oracle12cDialect
spring.jpa.properties.hibernate.default_schema=iym
spring.jpa.hibernate.ddl-auto=create
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# Connection pool configuration optimized for testing
spring.datasource.hikari.connectionTimeout=20000
spring.datasource.hikari.maximumPoolSize=5
spring.datasource.hikari.minimumIdle=2
spring.datasource.hikari.idleTimeout=300000
spring.datasource.hikari.maxLifetime=1200000
spring.datasource.hikari.leakDetectionThreshold=60000

# SQL initialization - disabled since we use init script in container
spring.sql.init.mode=never
spring.jpa.defer-datasource-initialization=false

# Logging configuration for tests
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.springframework.jdbc.core=DEBUG
logging.level.org.testcontainers=INFO
logging.level.iym=DEBUG
logging.level.com.zaxxer.hikari=DEBUG

# Test-specific configurations
spring.test.database.replace=none
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false

# Disable banner for cleaner test output
spring.main.banner-mode=off

# Transaction configuration for tests
spring.jpa.properties.hibernate.connection.autocommit=false
spring.transaction.default-timeout=30

# Oracle-specific configurations
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
