package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.MahkemeKararAidiyat;
import iym.common.model.entity.iym.mk.MahkemeKararAidiyatIslem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MahkemeKararAidiyatIslem entity
 */
@Repository
public interface MahkemeKararAidiyatIslemRepo extends JpaRepository<MahkemeKararAidiyatIslem, Long> {

    List<MahkemeKararAidiyatIslem> findByMahkemeKararId(Long mahkemeKararId);

}
