package iym.common.model.entity.iym.talep;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Entity class for HEDEFLER_AIDIYAT_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "HedeflerAidiyatTalep")
@Table(name = "HEDEFLER_AIDIYAT_TALEP")
public class HedeflerAidiyatTalep implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "HED_AID_TALEP_SEQ")
    @SequenceGenerator(name = "HED_AID_TALEP_SEQ", sequenceName = "HED_AID_TALEP_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "HEDEF_ID", nullable = false)
    @NotNull
    private Long hedefTalepId;

    @Column(name = "AIDIYAT_KOD", nullable = false, length = 15)
    @NotNull
    @Size(max = 15)
    private String aidiyatKod;

    @Column(name = "TARIH", nullable = false)
    @NotNull
    @Temporal(TemporalType.TIMESTAMP)
    private Date tarih;

    @Column(name = "KULLANICI_ID", nullable = false)
    @NotNull
    private Long kullaniciId;

    @Column(name = "DURUMU", length = 15)
    @Size(max = 15)
    private String durumu;
}
