package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.MahkemeKararSucTipleri;
import iym.common.service.db.mk.DbMahkemeKararSucTipleriService;
import iym.db.jpa.dao.mk.MahkemeKararSucTipleriRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for MahkemeKararSuc entity
 */
@Service
public class DbMahkemeKararSucTipleriServiceImpl extends GenericDbServiceImpl<MahkemeKararSucTipleri, Long> implements DbMahkemeKararSucTipleriService {

    private final MahkemeKararSucTipleriRepo mahkemeKararSucTipleriRepo;

    @Autowired
    public DbMahkemeKararSucTipleriServiceImpl(MahkemeKararSucTipleriRepo repository) {
        super(repository);
        this.mahkemeKararSucTipleriRepo = repository;
    }

    @Override
    public List<MahkemeKararSucTipleri> findByMahkemeKararId(Long mahkemeKararId){
        return mahkemeKararSucTipleriRepo.findByMahkemeKararId(mahkemeKararId);
    }

    @Override
    public Optional<MahkemeKararSucTipleri> findByMahkemeKararIdAndSucTipKodu(Long mahkemeKararId, String sucTipKodu){
        return mahkemeKararSucTipleriRepo.findByMahkemeKararIdAndSucTipKodu(mahkemeKararId, sucTipKodu);
    }

}
