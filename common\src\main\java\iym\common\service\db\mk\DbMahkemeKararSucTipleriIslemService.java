package iym.common.service.db.mk;

import iym.common.model.entity.iym.mk.MahkemeKararSucTipleriIslem;
import iym.common.service.db.GenericDbService;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for MahkemeKararSucTipleriIslem entity
 */
public interface DbMahkemeKararSucTipleriIslemService extends GenericDbService<MahkemeKararSucTipleriIslem, Long> {

    List<MahkemeKararSucTipleriIslem> findByMahkemeKararId(Long mahkemeKararId);

    Optional<MahkemeKararSucTipleriIslem> findByMahkemeKararIdAndSucTipKodu(Long mahkemeKararId, String sucTipKodu);

}
