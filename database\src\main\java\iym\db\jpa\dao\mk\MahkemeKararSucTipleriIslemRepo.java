package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.MahkemeKararSucTipleri;
import iym.common.model.entity.iym.mk.MahkemeKararSucTipleriIslem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MahkemeKararSucTipleriIslem entity
 */
@Repository
public interface MahkemeKararSucTipleriIslemRepo extends JpaRepository<MahkemeKararSucTipleriIslem, Long> {

    List<MahkemeKararSucTipleriIslem> findByMahkemeKararId(Long mahkemeKararId);

    Optional<MahkemeKararSucTipleriIslem> findByMahkemeKararIdAndSucTipKodu(Long mahkemeKararId, String sucTipKodu);
}
