// Genel <PERSON>p Gönder Component Styles

.p-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  
  .p-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    border-radius: 8px 8px 0 0;
  }
}

// Form field styling
.p-inputtext,
.p-dropdown,
.p-calendar,
.p-inputtextarea {
  border-radius: 6px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
  
  &:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
  
  &.ng-invalid.ng-dirty {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  }
}

// Dropdown specific styling
.p-dropdown {
  .p-dropdown-label {
    padding: 0.75rem;
  }
  
  &.p-focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
}

// Calendar specific styling
.p-calendar {
  .p-inputtext {
    border: none;
    box-shadow: none;
  }
  
  &.p-focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
}

// Checkbox styling
.p-checkbox {
  .p-checkbox-box {
    border-radius: 4px;
    border: 2px solid #d1d5db;
    transition: all 0.2s ease;
    
    &.p-highlight {
      background: #667eea;
      border-color: #667eea;
    }
  }
}

// File upload styling
.p-fileupload {
  .p-fileupload-choose {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 6px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
  }
}

// Button styling
.p-button {
  border-radius: 6px;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  
  &.p-button-secondary {
    background: #6b7280;
    border-color: #6b7280;
    
    &:hover {
      background: #4b5563;
      border-color: #4b5563;
    }
  }
  
  &:not(.p-button-secondary) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    
    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Error message styling
small.text-red-500 {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: block;
}

// Required field indicator
.text-red-500 {
  color: #ef4444;
}

// Grid responsive adjustments
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }
  
  .md\:col-span-2 {
    grid-column: span 1;
  }
  
  .lg\:grid-cols-3 {
    grid-template-columns: 1fr;
  }
}

// Loading state
.p-progressspinner {
  .p-progressspinner-circle {
    stroke: #667eea;
  }
}

// Toast positioning
:host ::ng-deep .p-toast {
  z-index: 9999;
}

// Card hover effects
.p-card {
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

// Form section spacing
.mb-4 {
  margin-bottom: 1rem;
}

// File upload area styling
.border-dashed {
  border-style: dashed;
  border-width: 2px;
  border-color: #d1d5db;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #667eea;
    background-color: #f8faff;
  }
}

// Selected file display
.bg-blue-50 {
  background-color: #eff6ff;
}

.border-blue-200 {
  border-color: #bfdbfe;
}

.text-blue-500 {
  color: #3b82f6;
}

.text-blue-600 {
  color: #2563eb;
}

.text-blue-800 {
  color: #1e40af;
}

// Form validation states
.ng-invalid.ng-dirty {
  border-color: #ef4444 !important;
}

.ng-valid.ng-dirty {
  border-color: #10b981 !important;
}

// Responsive text sizing
@media (max-width: 640px) {
  h2 {
    font-size: 1.5rem;
  }
  
  .text-2xl {
    font-size: 1.5rem;
  }
}

// Focus states for accessibility
.p-inputtext:focus,
.p-dropdown:focus,
.p-calendar:focus,
.p-inputtextarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

// Animation for form submission
.yukleniyor {
  .p-button {
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
