<div class="p-m-4">
    <p-button *ngIf="authService.hasRole('YETKI_MANAGE')" label="Yeni Rol Ekle" icon="pi pi-plus" (onClick)="openNew()" severity="success"></p-button>
  
    <p-divider></p-divider>
  
    <p-toast />
    <p-confirmdialog />
  
    <p-table [value]="roller" tableStyleClass="p-datatable-sm">
      <ng-template pTemplate="header">
        <tr>
          <th>Domain</th>
          <th>Ad</th>
          <th *ngIf="authService.hasRole('YETKI_MANAGE')"><PERSON><PERSON><PERSON><PERSON></th>
        </tr>
      </ng-template>
  
      <ng-template pTemplate="body" let-rol>
        <tr>
          <td>{{ rol.domain }}</td>
          <td>{{ rol.ad }}</td>
          <td *ngIf="authService.hasRole('YETKI_MANAGE')">
            <p-button icon="pi pi-pencil" (onClick)="editRol(rol)" class="p-button-sm" severity="info" [ngStyle]="{'padding': '0.1rem 0.1rem'}"></p-button>
            <p-button icon="pi pi-trash" (onClick)="deleteRol(rol)" class="p-button-sm" severity="danger" [ngStyle]="{'padding': '0.1rem 0.1rem'}"></p-button>
          </td>
        </tr>
      </ng-template>
    </p-table>
  
    <p-dialog
      header="{{ isEditMode ? 'Rol Düzenle' : 'Yeni Rol Ekle' }}"
      [(visible)]="displayDialog"
      [modal]="true"
      [style]="{ width: '400px' }"
      [breakpoints]="{ '960px': '75vw', '640px': '90vw' }"
    >
      <div class="p-fluid p-3">
        <div class="field">
          <label for="domain">Domain</label>
          <input id="domain" pInputText [(ngModel)]="selectedRol.domain" class="w-full" />
        </div>
        <div class="field">
          <label for="ad">Ad</label>
          <input id="ad" pInputText [(ngModel)]="selectedRol.ad" class="w-full" />
        </div>
      </div>
  
      <p-divider></p-divider>
  
      <ng-template pTemplate="footer">
        <div class="flex justify-end gap-2">
          <p-button label="İptal" icon="pi pi-times" (onClick)="displayDialog = false" severity="secondary"></p-button>
          <p-button label="Kaydet" icon="pi pi-check" (onClick)="saveRol()" severity="success"></p-button>
        </div>
      </ng-template>
    </p-dialog>
  </div>
  