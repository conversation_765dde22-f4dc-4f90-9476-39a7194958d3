package iym.common.model.entity.iym.sorgu;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class MahkemeKararTalepSorguInfo {

    private Long id;
    private String sorusturmaNo;
    private String mahkemeKararNo;
    private String mahkemeKodu;
    private String mahkemeAdi;
    private String durum;
    private String aciklama;
    private Date kayitTarihi;
    private Long kaydedenKullaniciId;
    private String kullaniciAdi;
    private String adi;          // Kullanılmıyor, sadece SQL mapping için
    private String soyadi;
    private String kurumKodu;
    private String kurumAdi;
    private Long evrakId;
    private String evrakSiraNo;
    private String evrakNo;
    private String evrakKonusu;

}
