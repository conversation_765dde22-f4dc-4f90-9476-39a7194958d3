-- V1__add_kurum_column.sql
-- Add missing kurum column to kullanicilar table

-- Add kurum column if it doesn't exist
ALTER TABLE kullanicilar 
ADD COLUMN IF NOT EXISTS kurum VARCHAR(255);

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_kullanicilar_kurum ON kullanicilar(kurum);

-- Set default value for existing records (optional)
-- UPDATE kullanicilar 
-- SET kurum = 'DEFAULT_KURUM' 
-- WHERE kurum IS NULL;
