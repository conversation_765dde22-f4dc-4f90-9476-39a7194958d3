package iym.common.validation;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
public class ValidationResult {

    public static final ValidationResult VALID = ValidationResult.builder().valid(true).build();

    private boolean valid;

    private final List<String> reasons = new ArrayList<>();

    public ValidationResult(boolean valid) {
        this.valid = valid;
    }

    public ValidationResult(String reason) {
        this.valid = false;
        this.reasons.add(reason);
    }

    public ValidationResult(List<String> reasons) {
        this.valid = false;
        this.reasons.addAll(reasons);
    }

    public void addReason(String reason){
        reasons.add(reason);
    }

    public void addFailedReason(String reason){
        this.valid = false;
        this.reasons.add(reason);
    }

}