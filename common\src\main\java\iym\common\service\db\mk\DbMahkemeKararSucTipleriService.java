package iym.common.service.db.mk;

import iym.common.model.entity.iym.mk.MahkemeKararSucTipleri;
import iym.common.service.db.GenericDbService;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for MahkemeKararSuc entity
 */
public interface DbMahkemeKararSucTipleriService extends GenericDbService<MahkemeKararSucTipleri, Long> {

    List<MahkemeKararSucTipleri> findByMahkemeKararId(Long mahkemeKararId);

    Optional<MahkemeKararSucTipleri> findByMahkemeKararIdAndSucTipKodu(Long mahkemeKararId, String sucTipKodu);

}
