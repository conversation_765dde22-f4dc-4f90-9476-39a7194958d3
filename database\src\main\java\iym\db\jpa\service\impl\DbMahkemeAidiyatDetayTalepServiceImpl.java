package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.talep.MahkemeAidiyatDetayTalep;
import iym.common.service.db.DbMahkemeAidiyatDetayTalepService;
import iym.db.jpa.dao.talep.MahkemeAidiyatDetayTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * Service implementation for MahkemeAidiyatDetayTalep entity
 */
@Service
public class DbMahkemeAidiyatDetayTalepServiceImpl extends GenericDbServiceImpl<MahkemeAidiyatDetayTalep, Long> implements DbMahkemeAidiyatDetayTalepService {

    private final MahkemeAidiyatDetayTalepRepo mahkemeAidiyatDetayTalepRepo;

    @Autowired
    public DbMahkemeAidiyatDetayTalepServiceImpl(MahkemeAidiyatDetayTalepRepo repository) {
        super(repository);
        this.mahkemeAidiyatDetayTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeAidiyatDetayTalep> findByMahkemeKararId(Long mahkemeKararId) {
        return mahkemeAidiyatDetayTalepRepo.findByMahkemeKararTalepId(mahkemeKararId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeAidiyatDetayTalep> findByIliskiliMahkemeKararId(Long iliskiliMahkemeKararId) {
        return mahkemeAidiyatDetayTalepRepo.findByIliskiliMahkemeKararId(iliskiliMahkemeKararId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeAidiyatDetayTalep> findByMahkemeKararDetayId(Long mahkemeKararDetayId) {
        return mahkemeAidiyatDetayTalepRepo.findByMahkemeKararDetayTalepId(mahkemeKararDetayId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeAidiyatDetayTalep> findByDurum(String durum) {
        return mahkemeAidiyatDetayTalepRepo.findByDurum(durum);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeAidiyatDetayTalep> findByTarihBetween(Date startDate, Date endDate) {
        return mahkemeAidiyatDetayTalepRepo.findByTarihBetween(startDate, endDate);
    }

}
