package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.MahkemeKararSucTipleriIslem;
import iym.common.service.db.mk.DbMahkemeKararSucTipleriIslemService;
import iym.db.jpa.dao.mk.MahkemeKararSucTipleriIslemRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class DbMahkemeKararSucTipleriIslemServiceImpl extends GenericDbServiceImpl<MahkemeKararSucTipleriIslem, Long> implements DbMahkemeKararSucTipleriIslemService {

    private final MahkemeKararSucTipleriIslemRepo mahkemeKararSucTipleriIslemRepo;

    @Autowired
    public DbMahkemeKararSucTipleriIslemServiceImpl(MahkemeKararSucTipleriIslemRepo repository) {
        super(repository);
        this.mahkemeKararSucTipleriIslemRepo = repository;
    }

    @Override
    public List<MahkemeKararSucTipleriIslem> findByMahkemeKararId(Long mahkemeKararId){
        return mahkemeKararSucTipleriIslemRepo.findByMahkemeKararId(mahkemeKararId);
    }

    @Override
    public Optional<MahkemeKararSucTipleriIslem> findByMahkemeKararIdAndSucTipKodu(Long mahkemeKararId, String sucTipKodu){
        return mahkemeKararSucTipleriIslemRepo.findByMahkemeKararIdAndSucTipKodu(mahkemeKararId, sucTipKodu);
    }

}
