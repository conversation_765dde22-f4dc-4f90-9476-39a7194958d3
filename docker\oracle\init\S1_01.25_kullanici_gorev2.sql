-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for KULLANICI_GOREV2 if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'KU<PERSON><PERSON><PERSON>I_GOREV2_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.KULLANICI_GOREV2_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create KULLANICI_GOREV2 table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'KULLANICI_GOREV2';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.KULLANICI_GOREV2 (
      ID NUMBER NOT NULL,
      KULLANICI_ID NUMBER NOT NULL,
      GOREV_ID NUMBER NOT NULL,
      VEKIL CHAR(1 BYTE) NOT NULL,
      TARIH DATE,
      DURUM CHAR(1 BYTE),
      TARIH2 DATE,
      ONCELIK NUMBER,
      CONSTRAINT K_GOREV21 PRIMARY KEY (ID) ENABLE
    )';
    
    -- Create indexes
    EXECUTE IMMEDIATE 'CREATE INDEX iym.IND_KULL_GOR_ID ON iym.KULLANICI_GOREV2 (KULLANICI_ID ASC)';
    EXECUTE IMMEDIATE 'CREATE INDEX iym.KULLANICI_GOREV_ID ON iym.KULLANICI_GOREV2 (GOREV_ID ASC)';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.KULLANICI_GOREV2;
  IF row_count = 0 THEN
    -- Make sure we have users in KULLANICILAR table and gorevler in GOREVLER2 table
    DECLARE
      user_count NUMBER;
      gorev_count NUMBER;
    BEGIN
      SELECT COUNT(*) INTO user_count FROM iym.KULLANICILAR;
      SELECT COUNT(*) INTO gorev_count FROM iym.GOREVLER2;
      
      IF user_count > 0 AND gorev_count > 0 THEN
        -- Get the ID of the admin user
        DECLARE
          admin_id NUMBER;
        BEGIN
          SELECT ID INTO admin_id FROM iym.KULLANICILAR WHERE KULLANICI_ADI = 'admin';
          
          -- Sample data 1 - Admin user assigned to Yönetici görevi
          INSERT INTO iym.KULLANICI_GOREV2 (
            ID, KULLANICI_ID, GOREV_ID, VEKIL, TARIH, DURUM, TARIH2, ONCELIK
          ) VALUES (
            iym.KULLANICI_GOREV2_SEQ.NEXTVAL, admin_id, 1, 'H',
            TO_DATE('2023-01-01', 'YYYY-MM-DD'), 'A', NULL, 1
          );
          
          -- Get the ID of user 'ahmet'
          DECLARE
            ahmet_id NUMBER;
          BEGIN
            SELECT ID INTO ahmet_id FROM iym.KULLANICILAR WHERE KULLANICI_ADI = 'ahmet';
            
            -- Sample data 2 - Ahmet assigned to Uzman görevi
            INSERT INTO iym.KULLANICI_GOREV2 (
              ID, KULLANICI_ID, GOREV_ID, VEKIL, TARIH, DURUM, TARIH2, ONCELIK
            ) VALUES (
              iym.KULLANICI_GOREV2_SEQ.NEXTVAL, ahmet_id, 2, 'H',
              TO_DATE('2023-01-01', 'YYYY-MM-DD'), 'A', NULL, 2
            );
            
            -- Get the ID of user 'ayse'
            DECLARE
              ayse_id NUMBER;
            BEGIN
              SELECT ID INTO ayse_id FROM iym.KULLANICILAR WHERE KULLANICI_ADI = 'ayse';
              
              -- Sample data 3 - Ayşe assigned to Memur görevi
              INSERT INTO iym.KULLANICI_GOREV2 (
                ID, KULLANICI_ID, GOREV_ID, VEKIL, TARIH, DURUM, TARIH2, ONCELIK
              ) VALUES (
                iym.KULLANICI_GOREV2_SEQ.NEXTVAL, ayse_id, 3, 'H',
                TO_DATE('2023-01-01', 'YYYY-MM-DD'), 'A', NULL, 3
              );
            EXCEPTION
              WHEN NO_DATA_FOUND THEN
                NULL; -- User 'ayse' not found, skip this insertion
            END;
          EXCEPTION
            WHEN NO_DATA_FOUND THEN
              NULL; -- User 'ahmet' not found, skip this insertion
          END;
        EXCEPTION
          WHEN NO_DATA_FOUND THEN
            NULL; -- Admin user not found, skip all insertions
        END;
      END IF;
    END;
  END IF;
END;
/

COMMIT;
