package gov.tib.iym.mahkemekarar.view;

import gov.tib.iym.mahkemekarar.base.model.HEDEF_TIPLERI;
import gov.tib.iym.mahkemekarar.base.model.MAHKEME_KARAR_TIPLERI;
import gov.tib.iym.mahkemekarar.model.XmlEvrakPojo;
import gov.tib.iym.mahkemekarar.renderer.DirectoryTreeRenderer;
import gov.tib.iym.mahkemekarar.renderer.XmlEvrakListRenderer;
import gov.tib.iym.mahkemekarar.renderer.XmlTagData;
import gov.tib.iym.mahkemekarar.thread.Canli118AboneSorguTask;
import gov.tib.iym.mahkemekarar.thread.EvrakToPdfTask;
import gov.tib.iym.mahkemekarar.thread.TesteAktarTask;
import gov.tib.iym.mahkemekarar.xmlparse.EVRAK_GELEN_KURUMLAR;
import gov.tib.iym.mahkemekarar.xmlparse.EVRAK_KAYIT;
import gov.tib.iym.mahkemekarar.xmlparse.EVRAK_TIPLERI;
import gov.tib.iym.mahkemekarar.xmlparse.HEDEFLER;
import gov.tib.iym.mahkemekarar.xmlparse.MAHKEME_KARAR;
import gov.tib.iym.mahkemekarar.xmlparse.Utility;
import gov.tib.iym.mahkemekarar.xmlparse.XMLHATA;
import gov.tib.iym.model.IymFilePojo;
import gov.tib.iym.model.LoginInfo;
import gov.tib.iym.service.IymService;
import gov.tib.kubik.view.zk.component.TibWindow;
import gov.tib.util.HttpUtils;
import gov.tib.util.TemelIslemler;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.zkoss.util.media.Media;
import org.zkoss.zk.ui.Executions;
import org.zkoss.zk.ui.Sessions;
import org.zkoss.zkplus.theme.Themes;
import org.zkoss.zul.ListModelList;
import org.zkoss.zul.Listbox;
import org.zkoss.zul.Messagebox;
import org.zkoss.zul.Tree;

import service.Log4jService;
import service.ServiceManager;

public class XmlGonderWindow extends TibWindow {

	private XmlEvrakPojo evrak;
	public Tree tree;
	private EVRAK_KAYIT xmlEvrak;
	Listbox evrakList;
	ArrayList<XmlEvrakPojo> evrList;

	@Override
	protected void initWindow() {
		Long kullaniciId = new Long(String.valueOf(Sessions.getCurrent().getAttribute("kullaniciId")));
		IymService iym = ServiceManager.getIymservice(); // (IymService) ServiceManager.getService("iymservice");
		if (!iym.kurumKullanicisiKontrol(kullaniciId)) {
			Sessions.getCurrent().invalidate();
			return;
		}else{
			Themes.setTheme(Executions.getCurrent(), "test");
			tree = (Tree) getFellowIfAny("evrakBilgileri");
			tree.setItemRenderer(new DirectoryTreeRenderer<XmlTagData>());
			evrakList = (Listbox) getFellowIfAny("evrakList");
			evrakList.setItemRenderer(new XmlEvrakListRenderer(this));
			evrakListesiBind();
		}
	}

	public void evrakListesiBind() {
		LoginInfo personel = (LoginInfo) Sessions.getCurrent().getAttribute("personel");
		if(personel != null){
			List<String> kurumList = Utility.evrakKurumGetir(personel);
			String evrakKurum = kurumList.get(0);
			evrList = Utility.evrakListesiGetir(evrakKurum,personel.getIymId());

			ListModelList lm = new ListModelList(evrList);
			evrakList.getItems().clear();
			evrakList.setModel(lm);
		}else{
			this.mesajBoxGosterUyari("OTURUM SÜRESİ DOLMUŞ VEYA OTURUM KAPALI ");
		}
	}

	/*
	private EVRAK_KAYIT eskiXmlGiris(EVRAK_KAYIT xmlEvrak, String xmlYolu,String clientIp,LoginInfo personel) {
		try {
			if(personel != null){
				List<String> kurumList = Utility.evrakKurumGetir(personel);
				String evrakKurum = kurumList.get(0);
				String evrakBirim = Utility.evrakBirimGetir(personel, evrakKurum);
				String evrakTipi = xmlEvrak.evrakTipiBelirle(evrakKurum);
				xmlEvrak = Utility.eskiXmlKayit(xmlEvrak, evrakKurum, evrakBirim,
						evrakTipi, personel.getIymId(), xmlYolu,clientIp);
			}else{
				this.mesajBoxGosterUyari("OTURUM SÜRESİ DOLMUŞ VEYA OTURUM KAPALI ");
			}
		} catch (Exception e) {
		}
		return xmlEvrak;
	}
	*/

	private EVRAK_KAYIT yeniXmlGiris(EVRAK_KAYIT xmlEvrak, String xmlYolu,String clientIp,LoginInfo personel) {
		try {
			if(personel != null){
				List<String> kurumList = Utility.evrakKurumGetir(personel);
				String evrakKurum = kurumList.get(0);
				String evrakBirim = Utility.evrakBirimGetir(personel, evrakKurum);
				String evrakTipi = xmlEvrak.evrakTipiBelirle(evrakKurum);
				if(xmlEvrak.EVRAK_TIPI.equals(EVRAK_TIPLERI.ILETISIMIN_DENETLENMESI.getTip())){
					xmlEvrak = Utility.yeniXmlKayit(xmlEvrak, evrakKurum, evrakBirim,evrakTipi, personel.getIymId(), xmlYolu,clientIp);
				}else if(xmlEvrak.EVRAK_TIPI.equals(EVRAK_TIPLERI.ILETISIMIN_TESPITI.getTip())){
					xmlEvrak = Utility.yeniHtsXmlKayit(xmlEvrak, evrakKurum, evrakBirim,evrakTipi, personel.getIymId(), xmlYolu,clientIp);
				}else if(xmlEvrak.EVRAK_TIPI.equals(EVRAK_TIPLERI.GENEL_EVRAK.getTip())){
					xmlEvrak = Utility.yeniGenelEvrakXmlKayit(xmlEvrak, evrakKurum, evrakBirim,evrakTipi, personel.getIymId(), xmlYolu,clientIp);
				}
			}else{
				this.mesajBoxGosterUyari("OTURUM SÜRESİ DOLMUŞ VEYA OTURUM KAPALI ");
			}
		} catch (Exception e) {

		}
		return xmlEvrak;
	}

	
	private String zipAc(IymFilePojo file) {
		String xmlYolu = null;
		File f = new File(file.getPath(), file.getFileName());
		int dopPos = file.getFileName().lastIndexOf(".");

		if (f.exists()) {
			String uzanti = file.getFileName().substring(dopPos);
			if (uzanti.toLowerCase().equals(".zip")) {
				String acilanYol = Utility.zipAc(f.getAbsolutePath(),
						file.getPath());
				if (Utility.dosyalarTamamMiKontrol(acilanYol)) {
					xmlYolu = Utility.xmlDosyasininYolunuVer(acilanYol);
				}
			}
			else 
				return "-1";
		}
		return xmlYolu;
	}
	
	private String dosyaAlZipAc(Media media, long iymId, String clientIp) {
		Utility.xmlIslemLog(iymId, clientIp, "Dosya Al Zip Aç", null, null,xmlEvrak.islemId);
		ArrayList<IymFilePojo> files = Utility.dosyaIndir(this.getDesktop(),media);
		String xmlYolu = "";
		for (int i = 0; i < files.size(); i++) {
			IymFilePojo file = (IymFilePojo) files.get(i);
			xmlYolu = zipAc(file);
		}
		return xmlYolu;
	}

	


	private void arsiveKopyala(String xmlYolu, Long kullaniciId, String clientIp,Long islemId) {
		Utility.arsiveKopyala(xmlYolu, kullaniciId,clientIp,islemId);
	}
	
	
	public void xmlGonder(Media media) {
		LoginInfo personel = (LoginInfo) Sessions.getCurrent().getAttribute("personel");
		
		String clientIp = HttpUtils.getClientIpAddress(Executions.getCurrent());
		
	    xmlEvrak = new EVRAK_KAYIT(0L,personel.getIymId(),clientIp);
		String xmlYolu = null;
		boolean sonuc = false;
		if(personel!=null){
			Utility.xmlIslemLog(personel.getIymId(), clientIp, "XML Evrakı Yükle İşlemine Giriş", null, null,xmlEvrak.islemId);
		
			xmlYolu = dosyaAlZipAc(media,personel.getIymId(),clientIp);
			
			if (xmlYolu == null || xmlYolu.equalsIgnoreCase("")) {
				sonuc = true;
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","XML DOSYASI BULUNAMADI",personel.getIymId(),clientIp,xmlEvrak.islemId));
			}
			else if (xmlYolu != null && xmlYolu.equalsIgnoreCase("-1")) {
				sonuc = true;
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","YUKLENEN DOSYA ZIP UZANTILI OLMALIDIR.RAR kabul edilmez",personel.getIymId(),clientIp,xmlEvrak.islemId));
			}
			if(!sonuc){
				Log4jService.getIymv3XmlIslemLog().info("xmlYolu " + xmlYolu);
				xmlEvrakKabul(personel, clientIp, xmlYolu, sonuc,false);
			}
				
			
			agacOlustur(false);
			evrakListesiBind();
			if(!xmlEvrak.hataVarMi()){
				xmlEvrak.hatalar2.add(new XMLHATA("XML", "XML BAŞARI İLE KAYDEDİLDİ",personel.getIymId(),clientIp,xmlEvrak.islemId));
				xmlEvrak.hatalar2.remove(xmlEvrak.hatalar2.size()-1);  // bir önceki uyarı hatasını sil
				this.mesajBoxGosterUyari("EVRAK BAŞARI İLE KAYDEDİLDİ");
			}
		}else{
			this.mesajBoxGosterUyari("OTURUM SÜRESİ DOLMUŞ VEYA OTURUM KAPALI ");
		}
	}

	
	public EVRAK_KAYIT xmlGonderWebService(LoginInfo personel,String clientIp,String tempXmlDosyaAdi,String tempXmlDosyaYolu) {
	    xmlEvrak = new EVRAK_KAYIT(0L,personel.getIymId(),clientIp);
		String xmlYolu = null;
		boolean sonuc = false;
		if(personel!=null){
			Utility.xmlIslemLog(personel.getIymId(), clientIp, "XML Evrakı Yükle İşlemine Giriş", null, null,xmlEvrak.islemId);
			IymFilePojo file = new IymFilePojo();
			file.setFileName(tempXmlDosyaAdi);
			file.setPath(tempXmlDosyaYolu);
			file.setType("zip");
			
			xmlYolu = zipAc(file);
			
			if (xmlYolu == null || xmlYolu.equalsIgnoreCase("")) {
				sonuc = true;
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","XML DOSYASI BULUNAMADI",personel.getIymId(),clientIp,xmlEvrak.islemId));
			}
			else if (xmlYolu != null && xmlYolu.equalsIgnoreCase("-1")) {
				sonuc = true;
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","YUKLENEN DOSYA ZIP UZANTILI OLMALIDIR.RAR kabul edilmez",personel.getIymId(),clientIp,xmlEvrak.islemId));
			}
			if(!sonuc)
				xmlEvrakKabul(personel, clientIp, xmlYolu, sonuc,true);
		}
		if(!xmlEvrak.hataVarMi())
			xmlEvrak.hatalar2.add(new XMLHATA("XML", "XML BAŞARI İLE KAYDEDİLDİ",personel.getIymId(),clientIp,xmlEvrak.islemId));
		return xmlEvrak;
		
	}
	


	private void xmlEvrakKabul(LoginInfo personel, String clientIp,
			String xmlYolu, boolean sonuc,boolean webServiceMi) {
		List<String> kurumList = Utility.evrakKurumGetir(personel);
		String evrakKurum = kurumList.get(0);

		if(!sonuc)
			sonuc = xmlIsle(xmlYolu,personel,clientIp);
		
		if (!sonuc && kurumList.size() == 1){
			if (Utility.evrakDahaOnceGelmisMiKontrol(xmlEvrak.EVRAK_NO,
					evrakKurum, xmlEvrak.GEL_IL, xmlEvrak.EVRAK_TARIHI)) {
				if(!webServiceMi){
					int secim =this.mesajBoxGosterSor("["+ xmlEvrak.EVRAK_NO+ "] NOLU EVRAK DAHA ÖNCE KURUMUNUZ TARAFINDAN "
							+ " AYNI İLDEN GÖNDERİLMİŞ! İŞLEME DEVAM ETMEK İSTEDİĞİNİZE EMİN MİSİNİZ?");
					Long mukerrerNo = Utility.sonGonderilenEvrakSayiGetir(xmlEvrak.EVRAK_NO,evrakKurum, xmlEvrak.GEL_IL, xmlEvrak.EVRAK_TARIHI)+1;
					xmlEvrak.setMukerrerNo(mukerrerNo);
					if(secim == 16){
						sonuc=false;
					}else{
						sonuc=true;
					}
				}else{
					Long mukerrerNo = Utility.sonGonderilenEvrakSayiGetir(xmlEvrak.EVRAK_NO,evrakKurum, xmlEvrak.GEL_IL, xmlEvrak.EVRAK_TARIHI)+1;
					xmlEvrak.setMukerrerNo(mukerrerNo);
				}
				
			}else{
				Long mukerrerNo = Utility.sonGonderilenEvrakSayiGetir(xmlEvrak.EVRAK_NO,evrakKurum, xmlEvrak.GEL_IL, xmlEvrak.EVRAK_TARIHI)+1;
				xmlEvrak.setMukerrerNo(mukerrerNo);
			}
		}
		if (!sonuc) {
			Utility.xmlIslemLog(personel.getIymId(), clientIp, "XML Genel Kontrol", null, null,xmlEvrak.islemId);
		    xmlEvrakGenelKontrol(personel,clientIp);
			sonuc =xmlEvrak.hataVarMi();
		}

		if (!sonuc) {
			if (xmlEvrak.XML_VERSIYON == null
					|| xmlEvrak.XML_VERSIYON.equals("")) {
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","XML DOSYASI BOZUK YA DA VERSİYON BİLGİSİ GİRİLMEMİŞ",personel.getIymId(),clientIp,xmlEvrak.islemId));
				//xmlEvrak = eskiXmlGiris(xmlEvrak, xmlYolu,clientIp,personel);
			} else {
				xmlEvrak = yeniXmlGiris(xmlEvrak, xmlYolu,clientIp,personel);
			}
			sonuc =xmlEvrak.hataVarMi();
		}

		if(!sonuc){
			if(xmlEvrak != null && xmlEvrak.getEvrakId() != null && xmlEvrak.getEvrakId() != 0L){
				this.evrak = Utility.getEvrakById(xmlEvrak.getEvrakId());
				if(this.evrak != null){
					EvrakToPdfTask task = new EvrakToPdfTask(this.evrak.getId());
					task.run();
					Canli118AboneSorguTask task2 = new Canli118AboneSorguTask(this.evrak.getId(),personel.getKullaniciId());
					task2.run();
					/*TesteAktarTask task3 = new TesteAktarTask(this.evrak.getId(),personel.getKullaniciId());
					task3.run();*/
				}
			}else
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","XML KAYDETME İŞLEMİ BAŞARISIZ OLDU",personel.getIymId(),clientIp,xmlEvrak.islemId));
			sonuc =xmlEvrak.hataVarMi();
		}
	
	}
	
	public void xmlEvrakGetir() {
		LoginInfo personel = (LoginInfo) Sessions.getCurrent().getAttribute("personel");
		
		String clientIp = HttpUtils.getClientIpAddress(Executions.getCurrent());
		
	    xmlEvrak = new EVRAK_KAYIT(0L,personel.getIymId(),clientIp);
		String xmlYolu = null;
		boolean sonuc = false;
		if(personel != null){
			Utility.xmlIslemLog(personel.getIymId(), clientIp, "XML Evrakı Getirme İşlemine Giriş", null, null,xmlEvrak.islemId);
			/**************** DOSYA UPLOAD VE ZIP AC ISLEMLERI ***********************/
			String xmlKonum = Utility.getXmlKonumByEvrakID(((XmlEvrakPojo)evrakList.getSelectedItem().getValue()).getId());
			if(xmlKonum == null || xmlKonum.equals("")){
				sonuc = true;
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","XML DOSYASI BULUNAMADI",personel.getIymId(),clientIp,xmlEvrak.islemId));
			}else{
				File f = new File(xmlKonum);
				if(f.exists()&&f.isDirectory()){
					File[] fList = f.listFiles();
					if(fList.length>0){
						for(File f2:fList){
							if(f2.getName().indexOf(".xml")<0){
								Utility.tempXmlFtpAl(xmlKonum);
							}
						}
					}else
						Utility.tempXmlFtpAl(xmlKonum);
				}else{
					if(!f.exists()){
						Utility.tempXmlFtpAl(xmlKonum);
					}
				}
				
			}
			xmlYolu = Utility.xmlDosyasininYolunuVer(xmlKonum);
			if (xmlYolu == null || xmlYolu.equalsIgnoreCase("")) {
				sonuc = true;
				xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","XML DOSYASI BULUNAMADI",personel.getIymId(),clientIp,xmlEvrak.islemId));
			}
			xmlEvrak.setEvrakId(((XmlEvrakPojo)evrakList.getSelectedItem().getValue()).getId());
			if(!sonuc)
				sonuc = xmlIsle(xmlYolu,personel,clientIp);
	
			/********************** XML KAYDET **************************************/
			sonuc =xmlEvrak.hataVarMi();
			if(!sonuc){
				if(xmlEvrak.getEvrakId() != 0L)
					this.evrak = Utility.getEvrakById(xmlEvrak.getEvrakId());
				else
					xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","XML GETİRME İŞLEMİ BAŞARISIZ OLDU",personel.getIymId(),clientIp,xmlEvrak.islemId));
			}
			/**********************************************************************/
			agacOlustur(false);
		}else{
			this.mesajBoxGosterUyari("OTURUM SÜRESİ DOLMUŞ VEYA OTURUM KAPALI ");
		}
	}
	private void agacOlustur(boolean webServiceMi) {
		try {
			xmlEvrak.agacYapisiOlustur();
			if(!webServiceMi)
				tree.setModel(xmlEvrak.getTreeModel());	
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private boolean xmlIsle(String xmlYolu,LoginInfo personel,String clientIp) {
		boolean sonuc = true;
		if(sonuc)
			arsiveKopyala(xmlYolu, personel.getIymId(),clientIp,this.xmlEvrak.islemId);
		/***********************************************************************/

		/******************** XML OKU *******************************************/
		Utility.xmlIslemLog(personel.getIymId(), clientIp, "XML Parse", null, null,xmlEvrak.islemId);
		if(sonuc)
			sonuc = xmlEvrak.xmlIsle(new File(xmlYolu));
		/***********************************************************************/

		/******************** XML KONTROL ET *************************************/
		Utility.xmlIslemLog(personel.getIymId(), clientIp, "XML Dogrula", null, null,xmlEvrak.islemId);
		if(sonuc){
			xmlEvrak.hatalar2.addAll(xmlEvrak.dogrula(null));	
		}
		sonuc =xmlEvrak.hataVarMi();
		return sonuc;
		/**********************************************************************/
	}

	public void xmlOnayla() {
		LoginInfo personel = (LoginInfo) Sessions.getCurrent().getAttribute("personel");
		String clientIp = HttpUtils.getClientIpAddress(Executions.getCurrent());
		IymService iym = ServiceManager.getIymservice();

		if ( this.xmlEvrak != null || this.xmlEvrak.evrakId != null){

			XmlEvrakPojo evrak = iym.evrakGetir(this.xmlEvrak.evrakId ,personel.getIymId());
			if(evrak != null && evrak.getDurumu() != null  && evrak.getDurumu().equals("KISMI_IADE")){
				this.mesajBoxGosterUyari(" BTK'dan kismi iade edilen evraklar tekrar onaylanamaz. Evrak silinip, açıklama dikkate alınarak kısmi iade hedefler tekrar gönderilebilir.");
			}
		}


		xmlOnaylaKabul(personel, clientIp,false);
	}

	public void xmlOnaylaKabul(LoginInfo personel, String clientIp,boolean webServiceMi) {
		try {
			if(personel != null){
				/********************** XML ONAYLA **************************************/
				if(this.xmlEvrak != null || this.xmlEvrak.hatalar2.size() == 0){
					if ( this.evrak == null || this.evrak.getId() == null
							|| this.evrak.getId() == 0) {
						this.xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","EVRAK YOK YA DA HATALI",personel.getIymId(),clientIp,xmlEvrak.islemId));
						agacOlustur(webServiceMi);
					} 
					else {
						if(xmlEvrak.EVRAK_TIPI.equals(EVRAK_TIPLERI.ILETISIMIN_DENETLENMESI.getTip()))
							Utility.evrakOnayla(this.xmlEvrak,this.evrak.getId(),personel.getIymId(),clientIp);
						else if(xmlEvrak.EVRAK_TIPI.equals(EVRAK_TIPLERI.ILETISIMIN_TESPITI.getTip()))
							Utility.htsEvrakOnayla(this.xmlEvrak,this.evrak.getId(),personel.getIymId(),clientIp,personel.getGorevKodu());
						else if(xmlEvrak.EVRAK_TIPI.equals(EVRAK_TIPLERI.GENEL_EVRAK.getTip()))
							Utility.genelEvrakOnayla(this.xmlEvrak,this.evrak.getId(),personel.getIymId(),clientIp,personel.getGorevKodu());
						else//eski evrak tipi
							Utility.evrakOnayla(this.xmlEvrak,this.evrak.getId(),personel.getIymId(),clientIp);
						if (this.evrak == null || (xmlEvrak.hataVarMi()&&!webServiceMi)) {
							this.xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","EVRAK ONAYLANAMADI",personel.getIymId(),clientIp,xmlEvrak.islemId));
							agacOlustur(webServiceMi);
						}else{
							agacOlustur(webServiceMi);
							this.evrak = null;
							this.xmlEvrak = null;
							this.tree.clear();
						}
					}
					if(!webServiceMi){
						if (this.xmlEvrak == null)
							this.mesajBoxGosterUyari("EVRAK BAŞARI İLE ONAYLANDI");
						else if (this.xmlEvrak.hatalar2.size() != 0)
							this.mesajBoxGosterUyari("EVRAKTA HATALAR VAR! ONAYLAMA İŞLEMİ YAPILAMAZ");
					}
				}else{
					this.mesajBoxGosterUyari("ONAYLANACAK EVRAK YOK VEYA SEÇİLMEMİŞ ");
				}
				
			}else{
				this.mesajBoxGosterUyari("OTURUM SÜRESİ DOLMUŞ VEYA OTURUM KAPALI ");
			}
			if(!webServiceMi){
				evrakListesiBind();
			}
		} catch (Exception e) {
		}
	}

	private void xmlEvrakGenelKontrol(LoginInfo personel,String clientIp) {
		List<String> kurumList = Utility.evrakKurumGetir(personel);
		String evrakKurum = kurumList.get(0);
		System.out.println("kurumkod"+evrakKurum +" :size: kurumList.size() == 1");
		
		//KT.2018-009284 numaralı yazı gereği: 
		//KHK 668 kapsamında gelen evrakların ele alınması sona erdi.
		Date ohalKalkis = TemelIslemler.ParseZaman("19.07.2018 01:00:00");
		Date now = new Date();
		
	
		if (kurumList.size() == 1
				&& (evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMBSM.getKurumKodu())
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMSBR.getKurumKodu())
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMIDB.getKurumKodu())
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMKOM.getKurumKodu())
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMTEM.getKurumKodu())
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.JANDARMA.getKurumKodu())
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMUMD.getKurumKodu()) 
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMASAYIS.getKurumKodu()) 
						|| evrakKurum.equals(EVRAK_GELEN_KURUMLAR.MIT.getKurumKodu()))) {
					
			
					if(Utility.mukerrerEvrakKontrol(xmlEvrak.EVRAK_NO, evrakKurum)){
						xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","["+xmlEvrak.EVRAK_NO+
								"] NOLU EVRAK DAHA ÖNCE KURUMUNUZ TARAFINDAN GÖNDERİLMİŞ",personel.getIymId(),clientIp,xmlEvrak.islemId));
					}
					
					boolean adli = Utility.adliMahkemeKararTuruKontrolu(evrakKurum);
					boolean istihbari = Utility.istihbariMahkemeKararTuruKontrolu(evrakKurum);
					
					for (MAHKEME_KARAR m : xmlEvrak.MAHKEME_KARAR) {
						
						if(now.compareTo(ohalKalkis) >= 0 && m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ADLI_KHK_YAZILI_EMIR.getKararKodu())) {
							m.hatalar2.add(new XMLHATA("MAHKEME_KARAR","19.07.2018 01:00 TARİHİNDEN İTİBAREN ADLİ KHK YAZILI EMİR GÖNDERİLEMEZ.",personel.getIymId(),clientIp,xmlEvrak.islemId));
							break;
						}
						
						if (!(adli && istihbari)) {
							
							if ((adli && !(m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ADLI_HAKIM_KARARI.getKararKodu())
									|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ADLI_YAZILI_EMIR.getKararKodu())
									|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ADLI_KHK_YAZILI_EMIR.getKararKodu())
									|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ADLI_KHK_SONLANDIRMA.getKararKodu())
									|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ADLI_SONLANDIRMA.getKararKodu())
									|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ADLI_SAVCILIK_SONLANDIRMA.getKararKodu())
									|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ADLI_HAKIM_HTS_KARARI.getKararKodu())
									|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.MAHKEME_AIDIYAT_DEGISTIRME.getKararKodu())
									  || m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.HEDEF_AD_SOYAD_DEGISTIRME.getKararKodu())
									  || m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.MAHKEME_KODU_DEGISTIRME.getKararKodu())
									))) {
								m.hatalar2.add(new XMLHATA("MAHKEME_KARAR"," KURUMUNUZ SADECE ADLİ KARAR GÖNDEREBİLİR!",personel.getIymId(),
										clientIp,xmlEvrak.islemId));
								break;
							}
							
							if ((istihbari && !(m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_YAZILI_EMIR
											.getKararKodu())
									|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_HAKIM_KARARI
													.getKararKodu())
									|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_SONLANDIRMA
													.getKararKodu())
									|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ABONE_KUTUK_BILGILERI_KARARI
													.getKararKodu())
									|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.SINYAL_BILGI_DEGERLENDIRME_KARARI
												.getKararKodu()) 
									|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ADLI_HAKIM_HTS_KARARI.getKararKodu())
									|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.MAHKEME_AIDIYAT_DEGISTIRME.getKararKodu())
									|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.HEDEF_AD_SOYAD_DEGISTIRME.getKararKodu())
									|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.MAHKEME_KODU_DEGISTIRME.getKararKodu())
									))) {
									if(!m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.HEDEF_CANAK_DEGISTIRME.getKararKodu())) {
										m.hatalar2.add(new XMLHATA("MAHKEME_KARAR", " KURUMUNUZ SADECE İSTİHBARİ KARAR GÖNDEREBİLİR!", personel.getIymId(), clientIp, xmlEvrak.islemId));
									}
								break;
							}
						}
						
						for (String aidiyatKod : m.MAHKEME_AIDIYAT.AIDIYAT_KOD) {
							if (evrakKurum.equals(EVRAK_GELEN_KURUMLAR.JANDARMA.getKurumKodu())) {
								/* Jandarma Adli Kararda JA aidiyatları olmalı */
								if (m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ADLI_HAKIM_KARARI.getKararKodu())
										|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ADLI_YAZILI_EMIR.getKararKodu())
										|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ADLI_KHK_YAZILI_EMIR.getKararKodu())) {
									if (!aidiyatKod.startsWith("JA")) {
										m.MAHKEME_AIDIYAT.hatalar2.add(new XMLHATA("MAHKEME_AIDIYAT","AIDIYATKOD ["+aidiyatKod+"] KURUMUNUZ ADLİ KARAR AİDİYATLARI <JA> İLE BAŞLAMALIDIR!",personel.getIymId(),clientIp,xmlEvrak.islemId));
										break;
									}
									/* Jandarma Adli Kararda Jİ aidiyatları olmalı */
								} else if (m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_HAKIM_KARARI.getKararKodu())
										|| m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_YAZILI_EMIR.getKararKodu())) {
								    if (!(aidiyatKod.startsWith("Jİ")||aidiyatKod.startsWith("JG")||aidiyatKod.startsWith("JK")||aidiyatKod.startsWith("JT")||aidiyatKod.startsWith("JB"))) {
										m.MAHKEME_AIDIYAT.hatalar2.add(new XMLHATA("MAHKEME_AIDIYAT","AIDIYATKOD ["+aidiyatKod+"] KURUMUNUZ İSTİHBARİ KARAR AİDİYATLARI <Jİ> İLE BAŞLAMALIDIR!",personel.getIymId(),clientIp,xmlEvrak.islemId));
										break;
									}
								}
								/* Mit aidiyatları MİTx veya MTxxxx olmalı */
							} else if (evrakKurum.equals(EVRAK_GELEN_KURUMLAR.MIT
									.getKurumKodu())) {
								if (!((aidiyatKod.length() == 7 && aidiyatKod.startsWith("MT"))
										|| (aidiyatKod.length() == 4 && aidiyatKod.startsWith("MİT")))) {
									m.MAHKEME_AIDIYAT.hatalar2.add(new XMLHATA("MAHKEME_AIDIYAT","AIDIYATKOD ["+aidiyatKod+"]  KURUMUNUZ KARAR AİDİYATLARI <MT> İLE BAŞLAMALI ve 7 KARAKTER OLMALIDIR!",personel.getIymId(),clientIp,xmlEvrak.islemId));
									break;
								}
								/* EGMIDB aidiyatları Yxxxxxxxx olmalı */
							} else if (evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMIDB
									.getKurumKodu())) {
								if (!((aidiyatKod.length() == 8 && aidiyatKod.startsWith("Y")))) {
									m.MAHKEME_AIDIYAT.hatalar2.add(new XMLHATA("MAHKEME_AIDIYAT","AIDIYATKOD ["+aidiyatKod+"]  KURUMUNUZ KARAR AİDİYATLARI <Y> İLE BAŞLAMALI ve 8 KARAKTER OLMALIDIR!",personel.getIymId(),clientIp,xmlEvrak.islemId));
									break;
								}
							}
						}
						
						if(m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ADLI_YAZILI_EMIR.getKararKodu()) ||
								m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ADLI_KHK_YAZILI_EMIR.getKararKodu()) ||
							    	m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_YAZILI_EMIR.getKararKodu())){
							for(HEDEFLER h : m.HEDEFLER){
								if(m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ADLI_YAZILI_EMIR.getKararKodu())){
									if(!h.HEDEF_TIPI.equals(HEDEF_TIPLERI.GSM_YER_TESPITI.getHedefKodu())){
										if(!(Integer.parseInt(h.SURESI)<=2&&h.SURE_TIPI.equals("0"))){
											h.hatalar2.add(new XMLHATA("HEDEFLER","ADLİ YAZILI EMİR KARAR TİPLERİNDE SÜRE 2 GÜNDEN BÜYÜK OLAMAZ!",personel.getIymId(),clientIp,xmlEvrak.islemId));
										}
									}
								}
								else if(m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_YAZILI_EMIR.getKararKodu())){
									if(!(Integer.parseInt(h.SURESI)<=3&&h.SURE_TIPI.equals("0"))){
										h.hatalar2.add(new XMLHATA("HEDEFLER","ÖNLEYİCİ YAZILI EMİR KARAR TİPLERİNDE SÜRE 3 GÜNDEN BÜYÜK OLAMAZ!",personel.getIymId(),clientIp,xmlEvrak.islemId));
									}
								}
								else if(m.KARAR_TIP.equals(MAHKEME_KARAR_TIPLERI.ADLI_KHK_YAZILI_EMIR.getKararKodu())){
									if(!(Integer.parseInt(h.SURESI)<=10&&h.SURE_TIPI.equals("0"))){
										h.hatalar2.add(new XMLHATA("HEDEFLER","ADLI KHK YAZILI EMİR KARAR TİPLERİNDE SÜRE 10 GÜNDEN BÜYÜK OLAMAZ!",personel.getIymId(),clientIp,xmlEvrak.islemId));
									}
								}
							}
						}
					}
		} else {
			xmlEvrak.hatalar2.add(new XMLHATA("EVRAK_KAYIT","HATALI KULLANICI GİRİŞİ!",personel.getIymId(),clientIp,xmlEvrak.islemId));
		}
	}

	@Override
	public void temizle() {
		this.evrak = null;
		this.xmlEvrak = null;
		this.tree.clear();
		evrakListesiBind();
	}

	
	public void evrakSil() {
	
		String clientIp = HttpUtils.getClientIpAddress(Executions.getCurrent());
		LoginInfo personel = (LoginInfo) Sessions.getCurrent().getAttribute("personel");
		if(personel != null){

			IymService iym = ServiceManager.getIymservice();

			Utility.xmlIslemLog(personel.getIymId(), clientIp, "XML Evrakı Silme İşlemine Giriş", null, null,xmlEvrak.islemId);
			if(this.xmlEvrak == null || this.xmlEvrak.getEvrakId() == null){
				this.mesajBoxGosterUyari("SİLİNECEK EVRAK YOK VEYA SEÇİLMEMİŞ ");
			}else{

				XmlEvrakPojo evrakPojo = iym.getEvrakByID(this.evrak.getId());
				if(evrakPojo != null && evrakPojo.getDurumu() != null && evrakPojo.getDurumu().equals("KISMI_IADE")){

					if(this.mesajBoxGosterSor("Kısmi iade edilen '" + evrakPojo.getEvrakSiraNo() + "' numaralı evrakı listeden kaldırmak istiyor musunuz?") != Messagebox.YES){
						return;
					}


					if(!iym.kismiIadeEvrakListedenKaldir(this.evrak.getId(), personel.getIymId(), clientIp)){
						this.mesajBoxGosterUyari("EVRAK SİLİNEMEMİŞTİR!");
					}
				}

				else{
					if(!Utility.evrakSil(this.evrak.getId(), personel.getIymId(),clientIp)){
						this.mesajBoxGosterUyari("EVRAK SİLİNEMEMİŞTİR!");
					}else{
						Utility.evrakMahkemeKararIslemSil(this.evrak.getId(),personel.getIymId(),clientIp);
						this.mesajBoxGosterUyari("EVRAK SİLİNMİŞTİR!");
					}
				}
			}
		}else{
			this.mesajBoxGosterUyari("OTURUM SÜRESİ DOLMUŞ VEYA OTURUM KAPALI ");
		}
		evrakListesiBind();
		this.evrak = null;
		this.xmlEvrak = null;
		this.tree.clear();
		
	}
	
	public XmlEvrakPojo getEvrak() {
		return evrak;
	}

	public void setEvrak(XmlEvrakPojo evrak) {
		this.evrak = evrak;
	}

}
