package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.talep.MahkemeKararGuncellemeTalep;
import iym.common.service.db.DbMahkemeKoduDetayTalepService;
import iym.db.jpa.dao.talep.MahkemeKararGuncelleTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Service implementation for MahkemeKararTalep entity
 */
@Service
public class DbMahkemeKoduDetayTalepServiceImpl extends GenericDbServiceImpl<MahkemeKararGuncellemeTalep, Long> implements DbMahkemeKoduDetayTalepService {

    private final MahkemeKararGuncelleTalepRepo mahkemeKararGuncelleTalepRepo;

    @Autowired
    public DbMahkemeKoduDetayTalepServiceImpl(MahkemeKararGuncelleTalepRepo repository) {
        super(repository);
        this.mahkemeKararGuncelleTalepRepo = repository;
    }

    @Override
    public Optional<MahkemeKararGuncellemeTalep> findByMahkemeKararDetayId(Long mahkemeKararDetayId){
        return mahkemeKararGuncelleTalepRepo.findByDetayMahkemeKararTalepId(mahkemeKararDetayId);
    }
}
