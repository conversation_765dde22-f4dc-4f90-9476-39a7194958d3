# CI Pipeline
name: CI

on:
  push:
    branches: [ main, develop, feature/github-actions-setup ]
  pull_request:
    branches: [ main, develop ]

env:
  JAVA_VERSION: '17'
  MAVEN_OPTS: '-Xmx1024m'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    permissions:
      contents: read
      checks: write
      pull-requests: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK ${{ env.JAVA_VERSION }}
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'

      - name: Cache Maven dependencies
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-m2-

      - name: Run unit tests (excluding integration tests)
        run: mvn clean test -Dspring.profiles.active=test -Dtest="!*IntegrationTest" -DfailIfNoTests=false
        env:
          TZ: Europe/Istanbul

      - name: Generate JUnit test report
        id: test-report
        uses: dorny/test-reporter@v2.1.1
        if: success() || failure()
        with:
          name: Test Report (JUnit) - Unit Tests (excluding integration tests) ✅
          path: '**/target/surefire-reports/*.xml'
          reporter: java-junit
          fail-on-error: false
          token: ${{ secrets.GITHUB_TOKEN }}

      # Backup test summary if test-reporter fails
      - name: Publish test summary
        uses: test-summary/action@v2
        if: (success() || failure()) && steps.test-report.outcome == 'failure'
        with:
          paths: '**/target/surefire-reports/*.xml'

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results
          path: |
            **/target/surefire-reports/
            **/target/site/jacoco/
          retention-days: 30

  build:
    name: Build Application
    needs: test
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up JDK ${{ env.JAVA_VERSION }}
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'
          
      - name: Cache Maven dependencies
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-m2-
            
      - name: Build with Maven
        run: mvn clean package -DskipTests
        
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: jar-artifacts
          path: |
            backend/target/*.jar
            makos/target/*.jar
            !**/*-sources.jar
            !**/*-javadoc.jar
          retention-days: 30
          
      - name: Upload build info
        uses: actions/upload-artifact@v4
        with:
          name: build-info
          path: |
            **/target/maven-archiver/
            pom.xml
            */pom.xml
          retention-days: 7

  integration-test:
    name: Integration Tests with Testcontainers
    needs: test
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK ${{ env.JAVA_VERSION }}
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'

      - name: Cache Maven dependencies
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-m2-

      - name: Set up Docker for Testcontainers
        run: |
          # Ensure Docker is running
          docker --version
          docker info

      - name: Debug environment before integration tests
        run: |
          echo "🔍 ===== ENVIRONMENT DEBUG INFO ====="
          echo "🌐 Network configuration:"
          ip addr show || ifconfig -a || echo "Network info not available"
          echo "🐳 Docker info:"
          docker info
          echo "☕ Java network properties:"
          java -XshowSettings:properties -version 2>&1 | grep -i "net\|ipv"
          echo "🔍 =================================="

      - name: Run integration tests with Testcontainers
        run: mvn verify -Dspring.profiles.active=oracle-test -DskipUTs=true
        env:
          # Timezone settings for Oracle
          TZ: Europe/Istanbul
          JAVA_TOOL_OPTIONS: -Duser.timezone=Europe/Istanbul -Djava.net.preferIPv4Stack=true -Djava.net.preferIPv6Addresses=false
          # Testcontainers settings
          TESTCONTAINERS_RYUK_DISABLED: false
          TESTCONTAINERS_CHECKS_DISABLE: false
          TESTCONTAINERS_HOST_OVERRIDE: 127.0.0.1
          # Docker settings for GitHub Actions
          DOCKER_HOST: unix:///var/run/docker.sock
          # Force IPv4 networking
          _JAVA_OPTIONS: -Djava.net.preferIPv4Stack=true -Djava.net.preferIPv6Addresses=false
          # Oracle JDBC IPv4 enforcement
          ORACLE_NET_TNS_ADMIN: ""
          
      - name: Upload integration test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: integration-test-results
          path: |
            **/target/failsafe-reports/
            **/target/surefire-reports/
          retention-days: 30

  code-quality:
    name: Code Quality Analysis
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: Set up JDK ${{ env.JAVA_VERSION }}
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'
          
      - name: Cache Maven dependencies
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-m2-
            
      - name: Run Maven verify
        run: mvn clean verify -DskipTests
        
      - name: Check code formatting
        run: |
          echo "Code formatting check would go here"
          echo "You can add Spotless or similar tools later"
