package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.MahkemeSucTipiDetayIslem;
import iym.common.model.entity.iym.talep.MahkemeSucTipiDetayTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface MahkemeSucTipiDetayIslemRepo extends JpaRepository<MahkemeSucTipiDetayIslem, Long> {

    List<MahkemeSucTipiDetayIslem> findByMahkemeKararDetayIslemId(Long mahkemeKararDetayIslemId);

    

}
