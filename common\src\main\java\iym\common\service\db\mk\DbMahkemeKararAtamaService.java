package iym.common.service.db.mk;

import iym.common.model.entity.iym.mk.MahkemeKararAtama;
import iym.common.service.db.GenericDbService;

import java.util.List;

/**
 * Service interface for MahkemeKararAtama entity
 */
public interface DbMahkemeKararAtamaService extends GenericDbService<MahkemeKararAtama, Long> {
    List<MahkemeKararAtama> findByEvrakId(Long evrakId);

    List<MahkemeKararAtama> findByEvrakIdAndDurum(Long evrakId, String durum);

}
