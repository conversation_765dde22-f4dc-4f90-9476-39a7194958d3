package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.MahkemeKararDetay;
import iym.common.service.db.mk.DbMahkemeKararDetayService;
import iym.db.jpa.dao.mk.MahkemeKararDetayRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class DbMahkemeKararDetayServiceImpl extends GenericDbServiceImpl<MahkemeKararDetay, Long> implements DbMahkemeKararDetayService {

    private final MahkemeKararDetayRepo mahkemeKararDetayRepo;

    @Autowired
    public DbMahkemeKararDetayServiceImpl(MahkemeKararDetayRepo repository) {
        super(repository);
        this.mahkemeKararDetayRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeKararDetay> findByEvrakId(Long evrakId){
        return mahkemeKararDetayRepo.findByEvrakId(evrakId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeKararDetay> findByMahkemeKararTalepId(Long mahkemeKararTalepId){
        return mahkemeKararDetayRepo.findByMahkemeKararTalepId(mahkemeKararTalepId);
    }


}
