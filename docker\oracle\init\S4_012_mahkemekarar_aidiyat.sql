-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MAHKEME_AIDIYAT if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MAHKEME_AIDIYAT_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAHKEME_AIDIYAT_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

--TODO : byte kalkacak, pk eklenecek
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_AIDIYAT';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_AIDIYAT (
      ID NUMBER NOT NULL
    , MAHKEME_ID NUMBER NOT NULL
    , AIDIYAT_KOD VARCHAR2(25) NOT NULL
    , CONSTRAINT MAHKEME_AIDIYAT_PK PRIMARY KEY (ID) ENABLE
    )';

    -- Create unique index
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX iym.MAH_AIDIYAT_ID ON iym.MAHKEME_AIDIYAT (MAHKEME_ID ASC, AIDIYAT_KOD ASC)';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.MAHKEME_AIDIYAT;
  IF row_count = 0 THEN
    -- Make sure we have mahkeme_karar records
    DECLARE
      mahkeme_count NUMBER;
    BEGIN
      SELECT COUNT(*) INTO mahkeme_count FROM iym.MAHKEME_KARAR;

      IF mahkeme_count > 0 THEN
        -- Get the IDs of the mahkeme_karar records
        FOR mahkeme_rec IN (SELECT ID FROM iym.MAHKEME_KARAR) LOOP
          -- Sample data 1 - Aidiyat 1
          INSERT INTO iym.MAHKEME_AIDIYAT (
            ID, MAHKEME_ID, AIDIYAT_KOD
          ) VALUES (
            iym.MAHKEME_AIDIYAT_SEQ.NEXTVAL, mahkeme_rec.ID, 'AIDIYAT1'
          );

          -- Sample data 2 - Aidiyat 2
          INSERT INTO iym.MAHKEME_AIDIYAT (
            ID, MAHKEME_ID, AIDIYAT_KOD
          ) VALUES (
            iym.MAHKEME_AIDIYAT_SEQ.NEXTVAL, mahkeme_rec.ID, 'AIDIYAT2'
          );
        END LOOP;
      END IF;
    END;
  END IF;
END;
/

COMMIT;
