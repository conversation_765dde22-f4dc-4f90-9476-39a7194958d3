# Test Strategy and Profiles Documentation

## Test Profile Strategy

### Overview
The IYM project uses separate Spring profiles for different test types. This approach improves test performance and ensures test isolation.

### Test Profiles

#### 1. `integration-test` Profile
**Use Case**: Tests requiring full application context
- `@SpringBootTest` tests
- Controller integration tests
- Service layer integration tests
- End-to-end tests

**Features**:
- Full Spring Boot auto-configuration enabled
- H2 in-memory database
- All application beans loaded
- Real HTTP server (if web environment)

**Example Usage**:
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("integration-test")
public class HealthCheckControllerRestTemplateTest {
    // Test with full application context
}
```

#### 2. `test` Profile
**Use Case**: Unit tests and lightweight tests
- `@WebMvcTest` tests
- `@MockitoTest` tests
- Component unit tests
- Tests that don't require database

**Features**:
- Fast execution
- Database auto-configuration disabled
- Lightweight Spring context
- Mocked dependencies

**Example Usage**:
```java
@WebMvcTest(HealthCheckController.class)
// test profile is used automatically
public class HealthCheckControllerMockMvcTest {
    // Only controller layer is tested
}
```

## Test Types and Strategies

### 1. Unit Tests
**Purpose**: Test a single component in isolation
**Profile**: `test` (or no profile specified)
**Database**: Mock/Disabled

```java
@ExtendWith(MockitoExtension.class)
public class ServiceUnitTest {
    @Mock
    private Repository repository;

    @InjectMocks
    private Service service;

    @Test
    void shouldProcessData() {
        // Test using mocks
    }
}
```

### 2. Integration Tests
**Purpose**: Test interaction between components
**Profile**: `integration-test`
**Database**: H2 in-memory

```java
@SpringBootTest
@ActiveProfiles("integration-test")
public class ServiceIntegrationTest {
    @Autowired
    private Service service;

    @Test
    void shouldPersistAndRetrieveData() {
        // Test with real database
    }
}
```

### 3. Web Layer Tests
**Controller Unit Test**:
```java
@WebMvcTest(MyController.class)
public class MyControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private MyService service;
}
```

**Controller Integration Test**:
```java
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("integration-test")
public class MyControllerIntegrationTest {
    @Autowired
    private MockMvc mockMvc;
    // Real services are used
}
```

## Database Configuration

### H2 Configuration for Integration Tests
```properties
# application.properties (test resources)
spring.profiles.active=integration-test

spring.datasource.url=jdbc:h2:mem:makostestdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# Clean schema for each test
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Connection pool settings
spring.datasource.hikari.connectionTimeout=20000
spring.datasource.hikari.maximumPoolSize=5
```

### Configuration for Unit Tests
```java
@Configuration
@Profile("test")
@EnableAutoConfiguration(exclude = {
    DataSourceAutoConfiguration.class,
    DataSourceTransactionManagerAutoConfiguration.class,
    HibernateJpaAutoConfiguration.class
})
public class TestConfig {
    // Database auto-configuration disabled
}
```

## Test Configuration Files

### File Structure
```
makos/src/test/
├── resources/
│   └── application.properties (integration-test profile)
└── java/iym/makos/config/
    ├── TestConfig.java (test profile - unit tests)
    └── IntegrationTestConfig.java (integration-test profile)
```

### Configuration Classes

**TestConfig.java** (For unit tests):
```java
@Configuration
@Profile("test")
@EnableAutoConfiguration(exclude = {
    DataSourceAutoConfiguration.class,
    DataSourceTransactionManagerAutoConfiguration.class,
    HibernateJpaAutoConfiguration.class
})
public class TestConfig {
    // Database components disabled
}
```

**IntegrationTestConfig.java** (For integration tests):
```java
@Configuration
@Profile("integration-test")
public class IntegrationTestConfig {
    // Full Spring Boot auto-configuration enabled
    // Database configuration from application.properties
}
```

## Test Execution Commands

```bash
# Run all tests
mvn test

# Run specific test class only
mvn test -Dtest=HealthCheckControllerRestTemplateTest

# Run tests with specific pattern
mvn test -Dtest=*IntegrationTest

# Run with specific profile
mvn test -Dspring.profiles.active=integration-test
```

## Best Practices

### 1. Test Type Selection
- Prefer **unit tests** for fast feedback
- Use **integration tests** for real scenarios
- Write **controller tests** for API contracts

### 2. Profile Usage
- Always use `@ActiveProfiles("integration-test")` for `@SpringBootTest`
- Don't specify profile for unit tests (automatic `test` profile)
- Choose appropriate profile for test type

### 3. Database Strategy
- H2 in-memory for integration tests
- Mock/stub for unit tests
- `create-drop` DDL for test isolation

### 4. Performance Optimization
- Keep unit tests fast
- Minimize integration tests
- Limit `@DirtiesContext` usage

## Example Test Classes

### Controller Integration Test
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("integration-test")
public class HealthCheckControllerRestTemplateTest {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Test
    void greetingShouldReturnDefaultMessage() {
        String response = this.restTemplate.getForObject(
            "http://localhost:" + port + "/check/healthCheck",
            String.class
        );
        assertThat(response).contains(HealthCheckController.API_ALIVE_MESSAGE);
    }
}
```

### Controller Unit Test
```java
@ExtendWith(MockitoExtension.class)
public class HealthCheckControllerMockitoTest {

    @InjectMocks
    private HealthCheckController healthCheckController;

    @Test
    public void shouldReturnDefaultMessage() {
        ResponseEntity<String> response = healthCheckController.healthCheck();
        assertThat(response.getBody()).isEqualTo(HealthCheckController.API_ALIVE_MESSAGE);
    }
}
```

## Troubleshooting

### Common Issues

1. **EntityManagerFactory not found**
   - Solution: Add `@ActiveProfiles("integration-test")` for `@SpringBootTest`

2. **Tests are too slow**
   - Solution: Use unit test strategy, mock dependencies

3. **Database connection errors**
   - Solution: Ensure you're using the correct profile

### Debug Tips
- Check which profile is active in test logs
- Monitor Spring context loading times
- Verify database state for test isolation

---

**Note**: This documentation explains the IYM project's test strategy and profile usage. Follow this guide when writing new tests.
