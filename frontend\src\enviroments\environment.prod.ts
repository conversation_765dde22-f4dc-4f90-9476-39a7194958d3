export const environment = {
    production: true,
    apiUrl: getApiUrl()
};

function getApiUrl(): string {
    // Runtime'da window.location'dan dinamik URL oluştur
    if (typeof window !== 'undefined') {
        const protocol = window.location.protocol;
        const hostname = window.location.hostname;
        
        // Production ortamında aynı host'u kullan ama port 8080
        // Eğer frontend port 80/443'te çalışıyorsa backend 8080'de
        return `${protocol}//${hostname}:8080`;
    }
    
    // Fallback (SSR veya window yoksa)
    return 'http://localhost:8080';
}
