package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.MahkemeKararAtama;
import iym.common.service.db.mk.DbMahkemeKararAtamaService;
import iym.db.jpa.dao.mk.MahkemeKararAtamaRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class DbMahkemeKararAtamaServiceImpl extends GenericDbServiceImpl<MahkemeKararAtama, Long> implements DbMahkemeKararAtamaService {

    private final MahkemeKararAtamaRepo mahkemeKararAtamaRepo;

    @Autowired
    public DbMahkemeKararAtamaServiceImpl(MahkemeKararAtamaRepo repository) {
        super(repository);
        this.mahkemeKararAtamaRepo = repository;
    }


    @Override
    public List<MahkemeKararAtama> findByEvrakId(Long evrakId){
        return mahkemeKararAtamaRepo.findByEvrakId(evrakId);
    }

    @Override
    public List<MahkemeKararAtama> findByEvrakIdAndDurum(Long evrakId, String durum){
        return mahkemeKararAtamaRepo.findByEvrakIdAndDurum(evrakId, durum);
    }

}
