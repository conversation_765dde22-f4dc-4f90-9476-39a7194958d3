-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MAHKEME_KARAR_ISLEM_SEQ if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = '<PERSON><PERSON><PERSON><PERSON>_KARAR_ISLEM_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAHKEME_KARAR_ISLEM_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/
--todo: byte -> null
-- Create MAHKEME_KARAR_ISLEM table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_KARAR_ISLEM';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_KARAR_ISLEM (
     ID NUMBER NOT NULL,
     <PERSON>VRAK_ID NUMBER NOT NULL,
     KU<PERSON>ANICI_ID NUMBER NOT NULL,
     KAY<PERSON>_TARIHI DATE NOT NULL,
     DURUM VARCHAR2(20),
     HUKUK_BIRIM VARCHAR2(50),
     KARAR_TIP VARCHAR2(50),
     MAH_KARAR_BAS_TAR DATE,
     MAH_KARAR_BITIS_TAR DATE,
     MAHKEME_ADI VARCHAR2(250),
     MAHKEME_KARAR_NO VARCHAR2(50),
     MAHKEME_ILI VARCHAR2(10),
     ACIKLAMA VARCHAR2(500 BYTE),
     HAKIM_SICIL_NO VARCHAR2(20),
     SORUSTURMA_NO VARCHAR2(50),
     ISLEM_TIPI VARCHAR2(10),
     MAHKEME_KARAR_ID NUMBER,
     ISLEM_NEDENI VARCHAR2(15),
     MAHKEME_KODU VARCHAR2(10),
     CONSTRAINT MAHKEME_KARAR_ISLEM_PK PRIMARY KEY(ID) ENABLE
    )';

  END IF;
END;
/


COMMIT;
