<div class="p-m-4">
  <p-button *ngIf="authService.hasRole('KULLANICI_MANAGE')" label="Yeni Grup Ekle" icon="pi pi-plus" (onClick)="openNew()" severity="success"></p-button>

  <p-divider></p-divider>

  <p-toast />
  <p-confirmdialog />

  <p-table [value]="kullaniciGruplari" tableStyleClass="p-datatable-sm">
    <ng-template pTemplate="header">
      <tr>
        <th>Ad</th>
        <th *ngIf="authService.hasRole('KULLANICI_MANAGE')"><PERSON><PERSON>lem<PERSON></th>
      </tr>
    </ng-template>

    <ng-template pTemplate="body" let-grup>
      <tr>
        <td>{{ grup.ad }}</td>
        <td *ngIf="authService.hasRole('KULLANICI_MANAGE')">
          <p-button icon="pi pi-pencil" (onClick)="editGrup(grup)" class="p-button-sm" severity="info" [ngStyle]="{'padding': '0.1rem 0.1rem'}"></p-button>
          <p-button icon="pi pi-trash" (onClick)="deleteGrup(grup)" class="p-button-sm" severity="danger" [ngStyle]="{'padding': '0.1rem 0.1rem'}"></p-button>
        </td>
      </tr>
    </ng-template>
  </p-table>

  <p-dialog 
    header="{{ isEditMode ? 'Grup Düzenle' : 'Yeni Grup Ekle' }}" 
    [(visible)]="displayDialog" 
    [modal]="true" 
    [style]="{ width: '500px' }"
    [breakpoints]="{'960px': '75vw', '640px': '90vw'}"
  >
    <p-tabView>
      <p-tabPanel header="Grup Bilgileri">
        <div class="p-fluid formgrid grid p-3">
          <div class="field col-12">
            <label for="ad">Ad</label>
            <input id="ad" pInputText [(ngModel)]="selectedGrup.ad" class="w-full" />
          </div>
        </div>
      </p-tabPanel>

      <p-tabPanel header="Yetkiler">
        <div class="p-fluid p-3">
          <label for="roller">Yetkiler</label>
          <p-multiSelect 
            id="roller"
            [options]="allYetkiler"
            optionLabel="name"
            optionValue="id"
            [(ngModel)]="selectedGrup.yetkiIdList"
            placeholder="Rolleri seçiniz"
            class="w-full"
            appendTo="body"
            display="chip"
          ></p-multiSelect>
        </div>
      </p-tabPanel>
    </p-tabView> 

    <p-divider></p-divider>

    <ng-template pTemplate="footer">
      <div class="flex justify-end gap-2">
        <p-button label="İptal" icon="pi pi-times" (onClick)="displayDialog = false" severity="secondary"></p-button>
        <p-button label="Kaydet" icon="pi pi-check" (onClick)="saveGrup()" severity="success"></p-button>
      </div>
    </ng-template>
  </p-dialog>
</div>
