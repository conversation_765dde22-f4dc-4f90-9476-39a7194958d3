package iym.common.service.db.mk;

import iym.common.model.entity.iym.mk.MahkemeKararAidiyat;
import iym.common.service.db.GenericDbService;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for MahkemeKararAidiyat entity
 */
public interface DbMahkemeKararAidiyatService extends GenericDbService<MahkemeKararAidiyat, Long> {

    List<MahkemeKararAidiyat> findByMahkemeKararId(Long mahkemeKararId);

    Optional<MahkemeKararAidiyat> findByMahkemeKararIdAndAidiyatKod(Long mahkemeKararId, String aidiyatKod);
}
