import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../enviroments/environment';

// Models
import { 
  MahkemeKararRequest,
  IDYeniKararRequest,
  IDUzatmaKarariRequest,
  IDSonlandirmaKarariRequest,
  ITKararRequest,
  IDAidiyatBilgisiGuncellemeRequest,
  IDHedefAdSoyadGuncellemeRequest,
  IDMahkemeKoduGuncellemeRequest,
  IDCanakGuncellemeRequest,
  GenelEvrakRequest
} from '../models/iym.models';

@Injectable({
  providedIn: 'root'
})
export class TalepService {
  
  private readonly apiUrl = environment.apiUrl + '/api/makos';

  constructor(private http: HttpClient) { }

  /**
   * Genel talep gönderme
   */
  genelTalepGonder(request: MahkemeKararRequest, dosya?: File | null): Observable<any> {
    const formData = new FormData();
    
    // Request objesini JSON string olarak ekle
    formData.append('mahkemeKararDetay', JSON.stringify(request));
    
    // Dosya varsa ekle
    if (dosya) {
      formData.append('mahkemeKararDosyasi', dosya, dosya.name);
    }

    return this.http.post(`${this.apiUrl}/genel-talep`, formData);
  }

  /**
   * ID Yeni Karar gönderme
   */
  idYeniKararGonder(request: IDYeniKararRequest, dosya: File): Observable<any> {
    const formData = new FormData();
    formData.append('request', JSON.stringify(request));
    formData.append('file', dosya, dosya.name);

    return this.http.post(`${this.apiUrl}/yeni-karar-id`, formData);
  }

  /**
   * ID Uzatma Kararı gönderme
   */
  idUzatmaKarariGonder(request: IDUzatmaKarariRequest, dosya: File): Observable<any> {
    const formData = new FormData();
    formData.append('mahkemeKararDetayID', JSON.stringify(request));
    formData.append('mahkemeKararDosyasiID', dosya, dosya.name);

    return this.http.post(`${this.apiUrl}/uzatmaKarariGonderID`, formData);
  }

  /**
   * ID Sonlandırma Kararı gönderme
   */
  idSonlandirmaKarariGonder(request: IDSonlandirmaKarariRequest, dosya: File): Observable<any> {
    const formData = new FormData();
    formData.append('mahkemeKararDetayID', JSON.stringify(request));
    formData.append('mahkemeKararDosyasiID', dosya, dosya.name);

    return this.http.post(`${this.apiUrl}/sonlandirmaKarariGonderID`, formData);
  }

  /**
   * IT Karar gönderme
   */
  itKararGonder(request: ITKararRequest, dosya: File): Observable<any> {
    const formData = new FormData();
    formData.append('request', JSON.stringify(request));
    formData.append('file', dosya, dosya.name);

    return this.http.post(`${this.apiUrl}/yeni-karar-it`, formData);
  }

  /**
   * Aidiyet Bilgisi Güncelleme
   */
  aidiyetBilgisiGuncelle(request: IDAidiyatBilgisiGuncellemeRequest, dosya: File): Observable<any> {
    const formData = new FormData();
    formData.append('mahkemeKararDetay', JSON.stringify(request));
    formData.append('mahkemeKararDosyasi', dosya, dosya.name);

    return this.http.post(`${this.apiUrl}/aidiyatBilgisiGuncelle`, formData);
  }

  /**
   * Hedef Ad Soyad Güncelleme
   */
  hedefAdSoyadGuncelle(request: IDHedefAdSoyadGuncellemeRequest, dosya: File): Observable<any> {
    const formData = new FormData();
    formData.append('mahkemeKararDetay', JSON.stringify(request));
    formData.append('mahkemeKararDosyasi', dosya, dosya.name);

    return this.http.post(`${this.apiUrl}/hedefAdSoyadGuncelle`, formData);
  }

  /**
   * Mahkeme Bilgisi Güncelleme
   */
  mahkemeBilgisiGuncelle(request: IDMahkemeKoduGuncellemeRequest, dosya: File): Observable<any> {
    const formData = new FormData();
    formData.append('request', new Blob([JSON.stringify(request)], { type: 'application/json' }));
    formData.append('file', dosya);

    return this.http.post(`${this.apiUrl}/mahkemeBilgisiGuncelle`, formData);
  }

  /**
   * Çanak Güncelleme
   */
  canakGuncelle(request: IDCanakGuncellemeRequest, dosya: File): Observable<any> {
    const formData = new FormData();
    formData.append('mahkemeKararDetay', JSON.stringify(request));
    formData.append('mahkemeKararDosyasi', dosya, dosya.name);

    return this.http.post(`${this.apiUrl}/canakNoGuncelle`, formData);
  }

  /**
   * Talep durumu sorgulama
   */
  talepDurumuSorgula(talepId: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/durum/${talepId}`);
  }

  /**
   * Talep geçmişi getirme
   */
  talepGecmisiGetir(sayfa: number = 0, boyut: number = 10): Observable<any> {
    return this.http.get(`${this.apiUrl}/gecmis?sayfa=${sayfa}&boyut=${boyut}`);
  }

  /**
   * Talep iptal etme
   */
  talepIptalEt(talepId: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${talepId}`);
  }

  /**
   * Dosya indirme
   */
  dosyaIndir(dosyaId: string): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/dosya/${dosyaId}`, {
      responseType: 'blob'
    });
  }

  /**
   * Mahkeme kodları listesi getirme
   */
  mahkemeKodlariGetir(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/mahkeme-kodlari`);
  }

  /**
   * İl ilçe kodları listesi getirme
   */
  ilIlceKodlariGetir(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/il-ilce-kodlari`);
  }

  /**
   * Kurum kodları listesi getirme
   */
  kurumKodlariGetir(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/kurum-kodlari`);
  }

  /**
   * Aidiyet kodları listesi getirme
   */
  aidiyetKodlariGetir(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/aidiyet-kodlari`);
  }

  /**
   * Suç tipi kodları listesi getirme
   */
  sucTipiKodlariGetir(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/suc-tipi-kodlari`);
  }

  /**
   * Hedef tipleri listesi getirme
   */
  hedefTipleriGetir(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/hedef-tipleri`);
  }

  /**
   * Süre tipleri listesi getirme
   */
  sureTipleriGetir(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/sure-tipleri`);
  }

  /**
   * BIM aidiyet kodları listesi getirme
   */
  bimAidiyetKodlariGetir(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/bim-aidiyet-kodlari`);
  }

  /**
   * Talep validasyonu
   */
  talepValidasyonu(request: MahkemeKararRequest): Observable<any> {
    return this.http.post(`${this.apiUrl}/validasyon`, request);
  }

  /**
   * Dosya validasyonu
   */
  dosyaValidasyonu(dosya: File): Observable<any> {
    const formData = new FormData();
    formData.append('dosya', dosya, dosya.name);

    return this.http.post(`${this.apiUrl}/dosya-validasyon`, formData);
  }

  /**
   * Toplu talep gönderme
   */
  topluTalepGonder(talepler: MahkemeKararRequest[], dosyalar: File[]): Observable<any> {
    const formData = new FormData();
    
    // Talepleri JSON array olarak ekle
    formData.append('talepler', JSON.stringify(talepler));
    
    // Dosyaları ekle
    dosyalar.forEach((dosya, index) => {
      formData.append(`dosya_${index}`, dosya, dosya.name);
    });

    return this.http.post(`${this.apiUrl}/toplu-talep`, formData);
  }

  /**
   * İstatistik bilgileri getirme
   */
  istatistikBilgileriGetir(): Observable<any> {
    return this.http.get(`${this.apiUrl}/istatistikler`);
  }

  /**
   * Rapor oluşturma
   */
  raporOlustur(raporParametreleri: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/rapor`, raporParametreleri);
  }

  /**
   * Hata logları getirme
   */
  hataLoglariniGetir(sayfa: number = 0, boyut: number = 10): Observable<any> {
    return this.http.get(`${this.apiUrl}/hata-loglari?sayfa=${sayfa}&boyut=${boyut}`);
  }

  /**
   * Sistem durumu kontrolü
   */
  sistemDurumuKontrol(): Observable<any> {
    return this.http.get(`${this.apiUrl}/sistem-durumu`);
  }
}
