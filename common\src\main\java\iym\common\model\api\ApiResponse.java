package iym.common.model.api;

import iym.common.enums.ResponseCode;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

@Data
@SuperBuilder
@Jacksonized
@ToString
@EqualsAndHashCode
public class ApiResponse {

    @NotNull
    @Valid
    private ResponseCode responseCode;

    private String responseMessage;
}
