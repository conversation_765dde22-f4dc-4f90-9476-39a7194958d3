package iym.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum TalepDurum {

    ONAYLANDI("ONAYLANDI"),
    SILINDI("SILINDI"),
    ISLEMDE("ISLEMDE"),
    ARSIV("ARSIV");

    private final String talepDurum;

    TalepDurum(String talepDurum){
        this.talepDurum = talepDurum;
    }

    @JsonValue
    public String getTalepDurum(){
        return this.talepDurum;
    }

    @JsonCreator
    public static TalepDurum fromName(String name) {
        for (TalepDurum talepDurum : TalepDurum.values()) {
            if (talepDurum.name().equals(name)) {
                return talepDurum;
            }
        }
        throw new IllegalArgumentException("Gecersiz talepDurum: '" + name + "'");
    }

    //@JsonCreator
    public static TalepDurum fromValue(String value) {
        for (TalepDurum talepDurum : TalepDurum.values()) {
            if (talepDurum.talepDurum.equals(value)) {
                return talepDurum;
            }
        }
        throw new IllegalArgumentException("Gecersiz talepDurum: '" + value + "'");
    }
}
