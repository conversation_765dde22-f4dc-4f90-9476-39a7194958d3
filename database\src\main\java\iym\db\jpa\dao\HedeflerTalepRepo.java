package iym.db.jpa.dao;

import iym.common.model.entity.iym.talep.HedeflerTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Repository interface for HedeflerTalep entity
 */
@Repository
public interface HedeflerTalepRepo extends JpaRepository<HedeflerTalep, Long> {

    List<HedeflerTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);
    
    List<HedeflerTalep> findByHedefTipi(Long hedefTipi);

    List<HedeflerTalep> findByHedefNo(String hedefNo);

    List<HedeflerTalep> findByBaslamaTarihiBetween(Date startDate, Date endDate);
    
    List<HedeflerTalep> findByKayitTarihiBetween(Date startDate, Date endDate);
    
    List<HedeflerTalep> findByTanimlamaTarihiBetween(Date startDate, Date endDate);
    
    List<HedeflerTalep> findByKapatmaTarihiBetween(Date startDate, Date endDate);
    
    List<HedeflerTalep> findByImhaTarihiBetween(Date startDate, Date endDate);


}
