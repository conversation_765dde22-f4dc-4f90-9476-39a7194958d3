package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.DetayMahkemeKararIslem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface DetayMahkemeKararIslemRepo extends JpaRepository<DetayMahkemeKararIslem, Long> {

    List<DetayMahkemeKararIslem> findByEvrakId(Long evrakId);

    List<DetayMahkemeKararIslem> findByMahkemeKararTalepId(Long mahkemeKararTalepId);


}
