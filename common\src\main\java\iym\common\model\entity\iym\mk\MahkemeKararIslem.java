package iym.common.model.entity.iym.mk;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MahkemeKararIslem")
@Table(name = "MAHKEME_KARAR_ISLEM")
public class MahkemeKararIslem implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    //işlem kaydedilirken mahkeme karar talebin id'sini almaktadir. Yapiyi bozmamak adina ayni yontem uygulaniyor.
    @Id
    //@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MAHKEME_KARAR_ISLEM_SEQ")
    //@SequenceGenerator(name = "MAHKEME_KARAR_ISLEM_SEQ", sequenceName = "MAHKEME_KARAR_ISLEM_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "EVRAK_ID", nullable = false)
    @NotNull
    private Long evrakId;

    @Column(name = "KULLANICI_ID", nullable = false)
    @NotNull
    private Long kullaniciId;

    @Column(name = "KAYIT_TARIHI", nullable = false)
    @NotNull
    @Temporal(TemporalType.TIMESTAMP)
    private Date kayitTarihi;

    @Column(name = "DURUM", length = 20)
    @Size(max = 20)
    private String durum;

    @Column(name = "HUKUK_BIRIM", length = 50)
    @Size(max = 50)
    private String hukukBirim;

    @Column(name = "KARAR_TIP", length = 50)
    @Size(max = 50)
    private String kararTip;

    @Column(name = "MAH_KARAR_BAS_TAR")
    @Temporal(TemporalType.TIMESTAMP)
    private Date mahkemeBaslamaTarihi;

    @Column(name = "MAH_KARAR_BITIS_TAR")
    @Temporal(TemporalType.TIMESTAMP)
    private Date mahkemeBitisTarihi;

    @Column(name = "MAHKEME_ADI", length = 250)
    @Size(max = 250)
    private String mahkemeAdi;

    @Column(name = "MAHKEME_KARAR_NO", length = 50)
    @Size(max = 50)
    private String mahkemeKararNo;

    @Column(name = "MAHKEME_ILI")
    private String mahkemeIlIlceKodu;

    @Column(name = "ACIKLAMA", length = 250)
    @Size(max = 250)
    private String aciklama;

    @Column(name = "HAKIM_SICIL_NO", length = 20)
    @Size(max = 20)
    private String hakimSicilNo;

    @Column(name = "SORUSTURMA_NO", length = 50)
    @Size(max = 50)
    private String sorusturmaNo;

    @Column(name = "ISLEM_TIPI", length = 10)
    @Size(max = 10)
    private String islemTipi;

    @Column(name = "MAHKEME_KARAR_ID")
    private Long mahkemeKararTalepId;

    @Column(name = "ISLEM_NEDENI", length = 15)
    @Size(max = 15)
    private String iptalNedeni;

    @Column(name = "MAHKEME_KODU", length = 10)
    @Size(max = 10)
    private String mahkemeKodu;


}
