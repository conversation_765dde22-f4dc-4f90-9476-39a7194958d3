package iym.common.validation;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

import java.util.Set;

public class ObjectValidator {

    private static final ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
    private static final Validator validator = factory.getValidator();

    public static <T> ValidationResult validate(T object)  {

        Set<ConstraintViolation<T>> constraintViolations = validator.validate(object);
        if (!constraintViolations.isEmpty()){
            ValidationResult result = new ValidationResult(false);
            constraintViolations.forEach( violation -> {
                result.addFailedReason(violation.getMessage());
            });

            return result;
        }

        return new ValidationResult(true);
    }
}
