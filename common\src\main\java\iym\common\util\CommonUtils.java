package iym.common.util;

import iym.common.enums.EvrakKurum;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.UUID;


@Slf4j
public class CommonUtils {

    public static String aidiyatEklemeUyarisi(String aidiyatKodu, MahkemeKararTip kararTipi, EvrakKurum evrakKurum) {
        String uyariMesaj = "";
        if (evrakKurum == EvrakKurum.JANDARMA) {
            if (kararTipi == MahkemeKararTip.ADLI_HAKIM_KARARI
                    || kararTipi == MahkemeKararTip.ADLI_YAZILI_EMIR
                    || kararTipi == MahkemeKararTip.ADLI_KHK_YAZILI_EMIR) {
                if (!aidiyatKodu.startsWith("JA")) {
                    uyariMesaj = String.format("Kurumunuz Adli Karar Aidiyatları JA ile baslamalidir. '%s' aidiyat kodu uygun değildir.", aidiyatKodu);
                }
            } else if (kararTipi == MahkemeKararTip.ONLEYICI_HAKIM_KARARI
                    || kararTipi == MahkemeKararTip.ONLEYICI_YAZILI_EMIR) {
                if (!(aidiyatKodu.startsWith("Jİ") || aidiyatKodu.startsWith("JG") || aidiyatKodu.startsWith("JK") || aidiyatKodu.startsWith("JT") || aidiyatKodu.startsWith("JB"))) {
                    uyariMesaj = String.format("Kurumunuz İstihbari Karar Aidiyatları Jİ ile baslamalidir. '%s' aidiyat kodu uygun değildir.", aidiyatKodu);
                }
            }
        } else if (evrakKurum == EvrakKurum.MIT) {
            if (!((aidiyatKodu.length() == 7 && aidiyatKodu.startsWith("MT"))
                    || (aidiyatKodu.length() == 4 && aidiyatKodu.startsWith("MİT")))) {
                uyariMesaj = String.format("Kurumunuz Karar Aidiyatları MT veya MİT ile baslamalidir. '%s' aidiyat kodu uygun değildir.", aidiyatKodu);
            }
        } else if (evrakKurum == EvrakKurum.EGMIDB) {
            if (!((aidiyatKodu.length() == 8 && aidiyatKodu.startsWith("Y")))) {
                uyariMesaj = String.format("Kurumunuz Karar Aidiyatları Y ile baslamali ve 8 karakter olmalıdır. '%s' aidiyat kodu uygun değildir.", aidiyatKodu);
            }
        }
        return uyariMesaj;
    }

    public static String evrakTipiBelirle(EvrakKurum evrakKurum, MahkemeKararTip kararTipi) {
        String evrakTipi = "";
        if (kararTipi == MahkemeKararTip.ADLI_HAKIM_KARARI
                || kararTipi == MahkemeKararTip.ADLI_SONLANDIRMA
                || kararTipi == MahkemeKararTip.ADLI_SAVCILIK_SONLANDIRMA
                || kararTipi == MahkemeKararTip.ADLI_KHK_SONLANDIRMA
                || kararTipi == MahkemeKararTip.HEDEF_AD_SOYAD_DEGISTIRME
                || kararTipi == MahkemeKararTip.MAHKEME_AIDIYAT_DEGISTIRME
                || kararTipi == MahkemeKararTip.MAHKEME_KODU_DEGISTIRME
        ) {
            evrakTipi = "402.01.01";
        } else if (kararTipi == MahkemeKararTip.ADLI_ASKERI_HAKIM_KARARI
                || kararTipi == MahkemeKararTip.ADLI_ASKERI_SONLANDIRMA) {
            evrakTipi = "402.05.03";
        } else if (kararTipi == MahkemeKararTip.ADLI_YAZILI_EMIR
                || kararTipi == MahkemeKararTip.ADLI_KHK_YAZILI_EMIR
                || kararTipi == MahkemeKararTip.ONLEYICI_YAZILI_EMIR) {
            evrakTipi = "402.02.01";
        } else if (kararTipi == MahkemeKararTip.ONLEYICI_HAKIM_KARARI
                || kararTipi == MahkemeKararTip.ONLEYICI_SONLANDIRMA
                || kararTipi == MahkemeKararTip.ABONE_KUTUK_BILGILERI_KARARI
                || kararTipi == MahkemeKararTip.SINYAL_BILGI_DEGERLENDIRME_KARARI) {

            if (evrakKurum == EvrakKurum.EGMIDB || evrakKurum == EvrakKurum.EGMSBR) {
                evrakTipi = "402.04.01";
            } else if (evrakKurum == EvrakKurum.MIT) {
                evrakTipi = "402.03.01";
            } else if (evrakKurum == EvrakKurum.JANDARMA) {
                evrakTipi = "402.05.01";
            }
        } else {
            evrakTipi = "";
        }

        /*
            String kararTip = this.MAHKEME_KARAR.get(0).KARAR_TIP;
            if(kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_HAKIM_KARARI.getKararKodu())
                    || kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_SONLANDIRMA.getKararKodu())
                    || kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_SAVCILIK_SONLANDIRMA.getKararKodu())
                    || kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_KHK_SONLANDIRMA.getKararKodu())
                    || kararTip.equals(MAHKEME_KARAR_TIPLERI.HEDEF_AD_SOYAD_DEGISTIRME.getKararKodu())
                    || kararTip.equals(MAHKEME_KARAR_TIPLERI.MAHKEME_AIDIYAT_DEGISTIRME.getKararKodu())
                    || kararTip.equals(MAHKEME_KARAR_TIPLERI.MAHKEME_KODU_DEGISTIRME.getKararKodu())
            ){
                evrakTipi = "402.01.01";
            }else if(kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_ASKERI_HAKIM_KARARI.getKararKodu())
                    || kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_ASKERI_SONLANDIRMA.getKararKodu())){
                evrakTipi = "402.05.03";
            }else if(kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_YAZILI_EMIR.getKararKodu())
                    || kararTip.equals(MAHKEME_KARAR_TIPLERI.ADLI_KHK_YAZILI_EMIR.getKararKodu())
                    || kararTip.equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_YAZILI_EMIR.getKararKodu())){
                evrakTipi = "402.02.01";
            }else if(kararTip.equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_HAKIM_KARARI.getKararKodu())
                    || kararTip.equals(MAHKEME_KARAR_TIPLERI.ONLEYICI_SONLANDIRMA.getKararKodu())
                    ||kararTip.equals(MAHKEME_KARAR_TIPLERI.ABONE_KUTUK_BILGILERI_KARARI.getKararKodu())
                    || kararTip.equals(MAHKEME_KARAR_TIPLERI.SINYAL_BILGI_DEGERLENDIRME_KARARI.getKararKodu())){
                if(evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMIDB.getKurumKodu())
                        ||evrakKurum.equals(EVRAK_GELEN_KURUMLAR.EGMSBR.getKurumKodu())
                ){
                    evrakTipi = "402.04.01";
                }else if(evrakKurum.equals(EVRAK_GELEN_KURUMLAR.MIT.getKurumKodu())){
                    evrakTipi = "402.03.01";
                }else if(evrakKurum.equals(EVRAK_GELEN_KURUMLAR.JANDARMA.getKurumKodu())){
                    evrakTipi = "402.05.01";
                }
    */

        return evrakTipi;
    }


    public static boolean yeniMahkemeKararTipi(MahkemeKararTip kararTipi) {
        return kararTipi.equals(MahkemeKararTip.ONLEYICI_HAKIM_KARARI)
                || kararTipi.equals(MahkemeKararTip.ONLEYICI_YAZILI_EMIR)
                || kararTipi.equals(MahkemeKararTip.ADLI_HAKIM_KARARI)
                || kararTipi.equals(MahkemeKararTip.ADLI_YAZILI_EMIR)
                || kararTipi.equals(MahkemeKararTip.ADLI_KHK_YAZILI_EMIR)
                || kararTipi.equals(MahkemeKararTip.ADLI_ASKERI_HAKIM_KARARI);
    }

    public static boolean uzatmaMahkemeKararTipi(MahkemeKararTip kararTipi) {
        return kararTipi.equals(MahkemeKararTip.ONLEYICI_HAKIM_KARARI)
                || kararTipi.equals(MahkemeKararTip.ONLEYICI_YAZILI_EMIR)
                || kararTipi.equals(MahkemeKararTip.ADLI_HAKIM_KARARI)
                || kararTipi.equals(MahkemeKararTip.ADLI_YAZILI_EMIR)
                || kararTipi.equals(MahkemeKararTip.ADLI_KHK_YAZILI_EMIR)
                || kararTipi.equals(MahkemeKararTip.ADLI_ASKERI_HAKIM_KARARI);
    }

    public static boolean sonlandirmaMahkemeKararTipi(MahkemeKararTip kararTipi) {

        return kararTipi.equals(MahkemeKararTip.ONLEYICI_HAKIM_KARARI)
                || kararTipi.equals(MahkemeKararTip.ONLEYICI_YAZILI_EMIR)
                || kararTipi.equals(MahkemeKararTip.ADLI_HAKIM_KARARI)
                || kararTipi.equals(MahkemeKararTip.ADLI_YAZILI_EMIR)
                || kararTipi.equals(MahkemeKararTip.ADLI_KHK_YAZILI_EMIR)
                || kararTipi.equals(MahkemeKararTip.ADLI_ASKERI_HAKIM_KARARI);
    }

    public static boolean isNullOrEmpty(String str) {
        if (str == null) {
            return true;
        }
        return str.trim().isEmpty();
    }

    public static String safeString(String input) {
        return input == null ? "" : input;
    }

    public static <T> Collection<T> safeList(Collection<T> list) {
        return list == null ? Collections.emptyList() : list;
    }

    public static EvrakKurum getEvrakKurum(String kurumKodu) {
        EvrakKurum evraKurum = EvrakKurum.BILINMEYEN;
        try {
            evraKurum = EvrakKurum.fromValue(kurumKodu);
        } catch (Exception ex) {
            log.error(kurumKodu + " default kurum kodu değildir.");
        }
        return evraKurum;
    }

    public static Date toDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static String dosyayiGeciciKlasoreKaydet(String dosyaAdi, byte[] dosyaIcerik) {
        String dosyaFullPath = "";
        try {
            // Kayıt edilecek dizin
            String dizin = "C:/gecici_dosyalar/";

            Path hedefYol = Paths.get(dizin + dosyaAdi);

            // Klasör yoksa oluştur
            Files.createDirectories(hedefYol.getParent());

            // Dosyayı diske yaz
            Files.write(hedefYol, dosyaIcerik);

            dosyaFullPath = hedefYol.toString();

        } catch (IOException e) {
            log.error("dosyayiGeciciKlasoreKaydet failed. dosyaAdi:{}", dosyaAdi, e);
        }
        return dosyaFullPath;
    }

    public static String getEvrakBasePath(){
        String evrakFilesBaseDir = ApplicationConstants.evrakFilesBaseDir();

        LocalDate today = LocalDate.now();

        return evrakFilesBaseDir + File.separator +
                today.getYear() + File.separator +
                String.format("%02d", today.getMonthValue()) + File.separator +
                String.format("%02d", today.getDayOfMonth());
    }

    public static String appendSubFolder(String path, String subFolder){
        if (!path.endsWith(File.separator))
            return path + File.separator + subFolder;

        return path + subFolder;
    }

    public static String appendFileToPath(String path, String fileName){
        if (!path.endsWith(File.separator))
            return path + File.separator + fileName;

        return path + fileName;
    }

    private static String evrakDosyaKaydet(UUID requestID, String dosyaAdi, byte[] dosyaIcerik) {
        String dosyaFullPath = "";
        try {
            LocalDate today = LocalDate.now();
            String evrakFilesBaseDir = ApplicationConstants.evrakFilesBaseDir();

            String pathStr = evrakFilesBaseDir + File.separator +
                    today.getYear() + File.separator +
                    String.format("%02d", today.getMonthValue()) + File.separator +
                    String.format("%02d", today.getDayOfMonth()) + File.separator + requestID.toString();

            Path path = Paths.get(pathStr);
            Files.createDirectories(path);

            Path hedefYol = Paths.get(pathStr + File.separator + dosyaAdi);

            // Dosyayı diske yaz
            Files.write(hedefYol, dosyaIcerik);

            dosyaFullPath = hedefYol.toString();

        } catch (IOException e) {
            log.error("evrakDosyaKaydet failed. dosyaAdi:{}", dosyaAdi, e);
        }
        return dosyaFullPath;
    }



    public static KararTuru getKararTuru(MahkemeKararTip mahkemeKararTipi) {
        KararTuru kararTuru = null;
        if (mahkemeKararTipi == MahkemeKararTip.HEDEF_BILGI_DEGISTIRME) {
            kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME;
        } else if (mahkemeKararTipi == MahkemeKararTip.MAHKEME_KARAR_BILGI_DEGISTIRME) {
            kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME;
        } else if (mahkemeKararTipi == MahkemeKararTip.MAHKEME_AIDIYAT_DEGISTIRME) {
            kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME;
        } else if (mahkemeKararTipi == MahkemeKararTip.MAHKEME_SUCTIPI_DEGISTIRME) {
            kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME;
        } else {
            if(yeniMahkemeKararTipi(mahkemeKararTipi)){
                kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR;
            }
            else  if(uzatmaMahkemeKararTipi(mahkemeKararTipi)){
                kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI;
            }
            else  if(sonlandirmaMahkemeKararTipi(mahkemeKararTipi)){
                kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI;
            }
        }

        return kararTuru;
    }
}
