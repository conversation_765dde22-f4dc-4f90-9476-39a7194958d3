package iym.common.util;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * Utility class for date and time operations
 * Provides methods for date/time conversion, formatting, and manipulation
 */
public class DateTimeUtils {

    private DateTimeUtils() {
    }

    public static final DateTimeFormatter ISO_OFFSET_DATE_TIME_FORMATTER = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
    public static final DateTimeFormatter ISO_LOCAL_DATE_TIME_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
    public static final DateTimeFormatter ISO_DATE_TIME_FORMATTER = DateTimeFormatter.ISO_DATE_TIME;
    public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    /**
     * Converts a string time to OffsetDateTime using Europe/Istanbul timezone
     *
     * @param time the time string in ISO_DATE_TIME format
     * @return OffsetDateTime in Europe/Istanbul timezone
     */
    public static OffsetDateTime toOffsetDateTime(String time) {
        LocalDateTime localDateTime = LocalDateTime.parse(time, ISO_DATE_TIME_FORMATTER);
        ZoneId zoneId = ZoneId.of("Europe/Istanbul");
        return localDateTime.atZone(zoneId).toOffsetDateTime();
    }

    /**
     * Converts a LocalDateTime to OffsetDateTime using Europe/Istanbul timezone
     *
     * @param time the LocalDateTime to convert
     * @return OffsetDateTime in Europe/Istanbul timezone
     */
    public static OffsetDateTime toOffsetDateTime(LocalDateTime time) {
        ZoneId zoneId = ZoneId.of("Europe/Istanbul");
        return time.atZone(zoneId).toOffsetDateTime();
    }

    /**
     * Converts a string time to LocalDateTime
     *
     * @param time the time string in ISO_DATE_TIME format
     * @return LocalDateTime
     */
    public static LocalDateTime toLocalDateTime(String time) {
        return LocalDateTime.parse(time, ISO_DATE_TIME_FORMATTER);
    }

    /**
     * Converts an OffsetDateTime to LocalDateTime using Europe/Istanbul timezone
     *
     * @param time the OffsetDateTime to convert
     * @return LocalDateTime in Europe/Istanbul timezone
     */
    public static LocalDateTime toLocalDateTime(OffsetDateTime time) {
        return time.atZoneSameInstant(ZoneId.of("Europe/Istanbul")).toLocalDateTime();
    }

    /**
     * Converts LocalDateTime to Date
     *
     * @param localDateTime the LocalDateTime to convert
     * @return Date object
     */
    public static Date toDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * Truncates the time portion of a Date, setting hours, minutes, seconds, and milliseconds to 0
     *
     * @param date the date to truncate
     * @return a new Date with time set to 00:00:00.000
     */
    public static Date truncateTime(Date date) {
        if (date == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * Adds the specified number of days to a Date
     *
     * @param date the date to add days to
     * @param days the number of days to add (can be negative to subtract)
     * @return a new Date with the specified number of days added
     */
    public static Date addDays(Date date, int days) {
        if (date == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, days);
        return cal.getTime();
    }
}
