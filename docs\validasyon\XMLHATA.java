package gov.tib.iym.mahkemekarar.xmlparse;

public class XMLHATA {
	private String hata;
	private String xmlTag;
	private Long islemId;
	
	
	public XMLHATA(String xmlTag, String hata,Long personelId,String clientIp,Long islemId) {
		super();
		this.hata = hata;
		this.xmlTag = xmlTag;
		this.islemId = islemId;
		
		Utility.xmlIslemLog(personelId, clientIp, "SONUC : "+this.hata,this.xmlTag, null,islemId);
		
		
	}
	
	public String getHata() {
		return hata;
	}
	public void setHata(String hata) {
		this.hata = hata;
	}
	public String getXmlTag() {
		return xmlTag;
	}
	public void setXmlTag(String xmlTag) {
		this.xmlTag = xmlTag;
	}

	public Long getIslemId() {
		return islemId;
	}

	public void setIslemId(Long islemId) {
		this.islemId = islemId;
	}


	
	
	
}
