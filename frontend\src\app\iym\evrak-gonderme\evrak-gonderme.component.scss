// <PERSON>vrak <PERSON>me Component Stilleri

.p-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  
  .p-card-header {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    font-weight: 600;
    border-radius: 8px 8px 0 0;
  }
}

// Dosya yükleme alanı
.p-fileupload {
  .p-fileupload-choose {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    border-radius: 6px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    
    &:hover {
      background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    }
  }
  
  .p-fileupload-content {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    background: #f9fafb;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #3b82f6;
      background: #eff6ff;
    }
  }
}

// Drag & Drop alanı
.upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  background: #f9fafb;
  
  &:hover {
    border-color: #3b82f6;
    background: #eff6ff;
  }
  
  &.drag-over {
    border-color: #10b981;
    background: #ecfdf5;
    transform: scale(1.02);
  }
  
  .upload-icon {
    font-size: 3rem;
    color: #9ca3af;
    margin-bottom: 1rem;
  }
  
  .upload-text {
    color: #6b7280;
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .upload-hint {
    color: #9ca3af;
    font-size: 0.875rem;
  }
}

// İlerleme çubuğu
.p-progressbar {
  height: 8px;
  border-radius: 4px;
  background: #e5e7eb;
  
  .p-progressbar-value {
    background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
  }
  
  &.p-progressbar-indeterminate .p-progressbar-value {
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  }
}

// Tablo stilleri
.p-table {
  .p-datatable-header {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
  }
  
  .p-datatable-thead > tr > th {
    background: #e9ecef;
    color: #495057;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    padding: 0.75rem;
  }
  
  .p-datatable-tbody > tr:hover {
    background: #f8f9fa;
  }
  
  .p-datatable-tbody > tr > td {
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem;
    vertical-align: middle;
  }
}

// Tag stilleri
.p-tag {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  
  &.p-tag-success {
    background: #10b981;
    color: white;
  }
  
  &.p-tag-danger {
    background: #ef4444;
    color: white;
  }
  
  &.p-tag-warn {
    background: #f59e0b;
    color: white;
  }
  
  &.p-tag-info {
    background: #3b82f6;
    color: white;
  }
}

// Buton stilleri
.p-button {
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.3s ease;
  
  &.p-button-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  &.p-button-success {
    background: #10b981;
    border-color: #10b981;
    
    &:hover {
      background: #059669;
      border-color: #059669;
      transform: translateY(-1px);
    }
  }
  
  &.p-button-danger {
    background: #ef4444;
    border-color: #ef4444;
    
    &:hover {
      background: #dc2626;
      border-color: #dc2626;
      transform: translateY(-1px);
    }
  }
  
  &.p-button-warning {
    background: #f59e0b;
    border-color: #f59e0b;
    
    &:hover {
      background: #d97706;
      border-color: #d97706;
      transform: translateY(-1px);
    }
  }
  
  &.p-button-info {
    background: #3b82f6;
    border-color: #3b82f6;
    
    &:hover {
      background: #1d4ed8;
      border-color: #1d4ed8;
      transform: translateY(-1px);
    }
  }
  
  &.p-button-secondary {
    background: #6b7280;
    border-color: #6b7280;
    
    &:hover {
      background: #4b5563;
      border-color: #4b5563;
      transform: translateY(-1px);
    }
  }
}

// İstatistik kartları
.stats-card {
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  .stats-icon {
    font-size: 2rem;
    margin-right: 1rem;
  }
  
  .stats-label {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
  }
  
  .stats-value {
    font-size: 1.5rem;
    font-weight: 700;
  }
}

// Dialog stilleri
.p-dialog {
  .p-dialog-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    
    .p-dialog-header-icon {
      color: white;
    }
  }
  
  .p-dialog-content {
    padding: 1.5rem;
    max-height: 70vh;
    overflow-y: auto;
  }
  
  .p-dialog-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
  }
}

// Validasyon mesajları
.validation-message {
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  
  &.success {
    background: #ecfdf5;
    border: 1px solid #10b981;
    color: #065f46;
  }
  
  &.error {
    background: #fef2f2;
    border: 1px solid #ef4444;
    color: #991b1b;
  }
  
  &.warning {
    background: #fffbeb;
    border: 1px solid #f59e0b;
    color: #92400e;
  }
}

// Dosya listesi
.file-list-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  background: white;
  transition: all 0.3s ease;
  
  &:hover {
    background: #f9fafb;
    border-color: #d1d5db;
  }
  
  .file-icon {
    color: #3b82f6;
    font-size: 1.25rem;
    margin-right: 0.75rem;
  }
  
  .file-info {
    flex: 1;
    
    .file-name {
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 0.25rem;
    }
    
    .file-size {
      font-size: 0.875rem;
      color: #6b7280;
    }
  }
  
  .file-actions {
    display: flex;
    gap: 0.5rem;
  }
}

// Responsive tasarım
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }
  
  .p-table {
    font-size: 0.875rem;
    
    .p-datatable-tbody > tr > td {
      padding: 0.5rem;
    }
  }
  
  .p-dialog {
    width: 95vw !important;
    margin: 1rem;
  }
  
  .stats-card {
    padding: 1rem;
    
    .stats-icon {
      font-size: 1.5rem;
    }
    
    .stats-value {
      font-size: 1.25rem;
    }
  }
}

// Animasyonlar
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

// Loading durumu
.loading-state {
  opacity: 0.6;
  pointer-events: none;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
