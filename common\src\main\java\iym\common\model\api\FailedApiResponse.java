package iym.common.model.api;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.util.UUID;

@Data
@SuperBuilder
@Jacksonized
@ToString
@EqualsAndHashCode(callSuper = true)
public class FailedApiResponse extends ApiResponse {

  @NotNull
  private UUID errorId;

}