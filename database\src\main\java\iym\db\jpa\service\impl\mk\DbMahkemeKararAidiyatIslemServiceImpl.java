package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.MahkemeKararAidiyatIslem;
import iym.common.service.db.mk.DbMahkemeKararAidiyatIslemService;
import iym.db.jpa.dao.mk.MahkemeKararAidiyatIslemRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service implementation for MahkemeKararAidiyatIslem entity
 */
@Service
public class DbMahkemeKararAidiyatIslemServiceImpl extends GenericDbServiceImpl<MahkemeKararAidiyatIslem, Long> implements DbMahkemeKararAidiyatIslemService {

    private final MahkemeKararAidiyatIslemRepo mahkemeKararAidiyatIslemRepo;

    @Autowired
    public DbMahkemeKararAidiyatIslemServiceImpl(MahkemeKararAidiyatIslemRepo repo) {
        super(repo);
        this.mahkemeKararAidiyatIslemRepo = repo;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeKararAidiyatIslem> findByMahkemeKararId(Long mahkemeKararId) {
        return mahkemeKararAidiyatIslemRepo.findByMahkemeKararId(mahkemeKararId);
    }



}
