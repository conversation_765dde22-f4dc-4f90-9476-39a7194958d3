package iym.db.jpa.dao;

import iym.common.model.entity.iym.EvrakKayit;
import iym.common.model.entity.iym.XmlIslemLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for XmlIslemLog entity
 */
@Repository
public interface XmlIslemLogRepo extends JpaRepository<XmlIslemLog, Long> {

}
