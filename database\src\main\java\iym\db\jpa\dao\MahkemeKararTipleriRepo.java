package iym.db.jpa.dao;

import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.MahkemeKararTipleri;
import iym.common.model.entity.iym.SucTipi;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MahkemeKararTipleri entity
 */
@Repository
public interface MahkemeKararTipleriRepo extends JpaRepository<MahkemeKararTipleri, Long> {

    Optional<MahkemeKararTipleri> findByKararKodu(String kararKodu);


}
