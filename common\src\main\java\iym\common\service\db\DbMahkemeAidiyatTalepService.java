package iym.common.service.db;

import iym.common.model.entity.iym.talep.MahkemeAidiyatTalep;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for MahkemeAidiyatTalep entity
 */
public interface DbMahkemeAidiyatTalepService extends GenericDbService<MahkemeAidiyatTalep, Long> {

    List<MahkemeAidiyatTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

    Optional<MahkemeAidiyatTalep> findById(Long id);

    Optional<MahkemeAidiyatTalep> findByMahkemeKararTalepIdAndAidiyatKod(Long mahkemeKararTalepId, String aidiyatKodu);

}
