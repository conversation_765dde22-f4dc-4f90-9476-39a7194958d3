-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for EVRAK_GELEN_KURUMLAR if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'EVRAK_GELEN_KURUMLAR_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.EVRAK_GELEN_KURUMLAR_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create EVRAK_GELEN_KURUMLAR table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'EVRAK_GELEN_KURUMLAR';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.EVRAK_GELEN_KURUMLAR (
      ID NUMBER NOT NULL,
      KURUM_KOD VARCHAR2(10 BYTE) NOT NULL,
      KURUM_ADI VARCHAR2(50 BYTE) NOT NULL,
      KURUM VARCHAR2(64 BYTE),
      IDX NUMBER,
      CONSTRAINT EVRAK_GELEN_KURUMLAR_PRM PRIMARY KEY (KURUM_KOD) ENABLE
    )';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.EVRAK_GELEN_KURUMLAR;
  IF row_count = 0 THEN
    -- Sample data 1
    INSERT INTO iym.EVRAK_GELEN_KURUMLAR (ID, KURUM_KOD, KURUM_ADI, KURUM, IDX)
    VALUES (iym.EVRAK_GELEN_KURUMLAR_SEQ.NEXTVAL, '01', 'ADALET BAKANLIĞI', 'ADALET BAKANLIĞI', 1);
    
    -- Sample data 2
    INSERT INTO iym.EVRAK_GELEN_KURUMLAR (ID, KURUM_KOD, KURUM_ADI, KURUM, IDX)
    VALUES (iym.EVRAK_GELEN_KURUMLAR_SEQ.NEXTVAL, '02', 'EMNIYET', 'EMNIYET', 2);
    
    -- Sample data 3
    INSERT INTO iym.EVRAK_GELEN_KURUMLAR (ID, KURUM_KOD, KURUM_ADI, KURUM, IDX)
    VALUES (iym.EVRAK_GELEN_KURUMLAR_SEQ.NEXTVAL, '03', 'JANDARMA', 'JANDARMA', 3);
    
    -- Sample data 4
    INSERT INTO iym.EVRAK_GELEN_KURUMLAR (ID, KURUM_KOD, KURUM_ADI, KURUM, IDX)
    VALUES (iym.EVRAK_GELEN_KURUMLAR_SEQ.NEXTVAL, '04', 'MİLLİ EĞİTİM BAKANLIĞI', 'MİLLİ EĞİTİM BAKANLIĞI', 4);
    
    -- Sample data 5
    INSERT INTO iym.EVRAK_GELEN_KURUMLAR (ID, KURUM_KOD, KURUM_ADI, KURUM, IDX)
    VALUES (iym.EVRAK_GELEN_KURUMLAR_SEQ.NEXTVAL, '05', 'SAĞLIK BAKANLIĞI', 'SAĞLIK BAKANLIĞI', 5);
  END IF;
END;
/

COMMIT;
