export const environment = {
    production: false,
    apiUrl: getApiUrl()
};

function getApiUrl(): string {
    // Runtime'da window.location'dan dinamik URL oluştur
    if (typeof window !== 'undefined') {
        const protocol = window.location.protocol;
        const hostname = window.location.hostname;

        // Development ortamında localhost:8080 kullan
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return `${protocol}//${hostname}:8080`;
        }

        // Production ortamında aynı host'u kullan ama port 8080
        return `${protocol}//${hostname}:8080`;
    }

    // Fallback (SSR veya window yoksa)
    return 'http://localhost:8080';
}