export const environment = {
    production: false,
    apiUrl: getApiUrl()
};

function getApiUrl(): string {
    // Priority 1: Runtime environment variable (<PERSON><PERSON>, Kubernetes vb.)
    if (typeof window !== 'undefined' && (window as any).APP_CONFIG?.API_URL) {
        console.log('Using runtime API_URL:', (window as any).APP_CONFIG.API_URL);
        return (window as any).APP_CONFIG.API_URL;
    }

    // Priority 2: Build-time environment variable
    if (typeof process !== 'undefined' && process.env && process.env['NG_APP_API_URL']) {
        console.log('Using build-time NG_APP_API_URL:', process.env['NG_APP_API_URL']);
        return process.env['NG_APP_API_URL'];
    }

    // Priority 3: Environment variable from window (injected by server)
    if (typeof window !== 'undefined' && (window as any).ENV?.API_URL) {
        console.log('Using window.ENV.API_URL:', (window as any).ENV.API_URL);
        return (window as any).ENV.API_URL;
    }

    // Priority 4: Dynamic URL based on current location (fallback)
    if (typeof window !== 'undefined') {
        const protocol = window.location.protocol;
        const hostname = window.location.hostname;

        // Development ortamında localhost:8080 kullan
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            const dynamicUrl = `${protocol}//${hostname}:8080`;
            console.log('Using dynamic development URL:', dynamicUrl);
            return dynamicUrl;
        }

        // Production ortamında aynı host'u kullan ama port 8080
        const dynamicUrl = `${protocol}//${hostname}:8080`;
        console.log('Using dynamic production URL:', dynamicUrl);
        return dynamicUrl;
    }

    // Priority 5: Static fallback
    console.log('Using static fallback URL: http://localhost:8080');
    return 'http://localhost:8080';
}