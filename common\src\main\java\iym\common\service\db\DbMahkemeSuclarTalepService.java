package iym.common.service.db;

import iym.common.model.entity.iym.talep.MahkemeSuclarTalep;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for MahkemeSuclarTalep entity
 */
public interface DbMahkemeSuclarTalepService extends GenericDbService<MahkemeSuclarTalep, Long> {

    Optional<MahkemeSuclarTalep> findById(Long id);

    List<MahkemeSuclarTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

    Optional<MahkemeSuclarTalep> findByMahkemeKararTalepIdAndSucTipKodu(Long mahkemeKararTalepId, String sucTipKodu);

}
