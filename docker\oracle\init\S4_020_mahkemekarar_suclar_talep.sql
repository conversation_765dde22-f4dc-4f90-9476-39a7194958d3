-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MAHKEME_SUCLAR_TALEP if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MAH<PERSON>ME_SUCLAR_TALEP_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAHKEME_SUCLAR_TALEP_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create MAHKEME_SUCLAR_TALEP table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_SUCLAR_TALEP';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_SUCLAR_TALEP (
      ID NUMBER NOT NULL,
      <PERSON>HKE<PERSON>_KARAR_ID NUMBER NOT NULL,
      MAHKEME_SUC_TIP_KOD VARCHAR2(10 BYTE) NOT NULL,
      DURUMU VARCHAR2(20 BYTE)
    )';
    
    -- Create unique index
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX iym.MAH_SUCLAR_TALEP_IDX ON iym.MAHKEME_SUCLAR_TALEP (MAHKEME_KARAR_ID ASC, MAHKEME_SUC_TIP_KOD ASC)';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.MAHKEME_SUCLAR_TALEP;
  IF row_count = 0 THEN
    -- Make sure we have mahkeme_karar_talep records
    DECLARE
      mahkeme_count NUMBER;
    BEGIN
      SELECT COUNT(*) INTO mahkeme_count FROM iym.MAHKEME_KARAR_TALEP;
      
      IF mahkeme_count > 0 THEN
        -- Get the IDs of the mahkeme_karar_talep records
        FOR mahkeme_rec IN (SELECT ID FROM iym.MAHKEME_KARAR_TALEP) LOOP
          -- Sample data 1 - Suç Tipi 1
          INSERT INTO iym.MAHKEME_SUCLAR_TALEP (
            ID, MAHKEME_KARAR_ID, MAHKEME_SUC_TIP_KOD, DURUMU
          ) VALUES (
            iym.MAHKEME_SUCLAR_TALEP_SEQ.NEXTVAL, mahkeme_rec.ID, 'SUC001', 'AKTIF'
          );
          
          -- Sample data 2 - Suç Tipi 2
          INSERT INTO iym.MAHKEME_SUCLAR_TALEP (
            ID, MAHKEME_KARAR_ID, MAHKEME_SUC_TIP_KOD, DURUMU
          ) VALUES (
            iym.MAHKEME_SUCLAR_TALEP_SEQ.NEXTVAL, mahkeme_rec.ID, 'SUC002', 'AKTIF'
          );
        END LOOP;
      END IF;
    END;
  END IF;
END;
/

COMMIT;
