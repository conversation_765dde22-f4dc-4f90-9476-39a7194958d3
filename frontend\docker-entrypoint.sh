#!/bin/sh

# Docker entrypoint script for dynamic environment configuration

# Default values
DEFAULT_API_URL="http://localhost:8080"

# Use environment variables or defaults
API_URL=${API_URL:-$DEFAULT_API_URL}
ENV_API_URL=${ENV_API_URL:-$API_URL}

echo "Configuring frontend with API_URL: $API_URL"

# Replace placeholders in index.html with actual environment values
sed -i "s|\${API_URL}|$API_URL|g" /usr/share/nginx/html/index.html
sed -i "s|\${ENV_API_URL}|$ENV_API_URL|g" /usr/share/nginx/html/index.html

echo "Frontend configuration completed"

# Start nginx
exec "$@"
