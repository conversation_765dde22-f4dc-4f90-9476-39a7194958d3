import { Injectable } from '@angular/core';

export interface AppConfig {
  apiUrl: string;
  production: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ConfigService {
  private config: AppConfig;

  constructor() {
    this.config = this.loadConfig();
  }

  private loadConfig(): AppConfig {
    // Runtime'da dinamik konfigürasyon
    const apiUrl = this.getApiUrl();
    const production = this.isProduction();

    return {
      apiUrl,
      production
    };
  }

  private getApiUrl(): string {
    // Environment variable'dan al (Docker için)
    if (typeof window !== 'undefined' && (window as any).APP_CONFIG?.API_URL) {
      return (window as any).APP_CONFIG.API_URL;
    }

    // Runtime'da window.location'dan dinamik URL oluştur
    if (typeof window !== 'undefined') {
      const protocol = window.location.protocol;
      const hostname = window.location.hostname;
      
      // Development ortamında localhost:8080 kullan
      if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return `${protocol}//${hostname}:8080`;
      }
      
      // Production ortamında aynı host'u kullan ama port 8080
      return `${protocol}//${hostname}:8080`;
    }
    
    // Fallback
    return 'http://localhost:8080';
  }

  private isProduction(): boolean {
    if (typeof window !== 'undefined') {
      const hostname = window.location.hostname;
      return hostname !== 'localhost' && hostname !== '127.0.0.1';
    }
    return false;
  }

  getConfig(): AppConfig {
    return this.config;
  }

  getApiUrl(): string {
    return this.config.apiUrl;
  }

  isProductionMode(): boolean {
    return this.config.production;
  }

  // Runtime'da config'i güncelleme imkanı
  updateApiUrl(newApiUrl: string): void {
    this.config.apiUrl = newApiUrl;
  }
}
