package iym.db.jpa.dao.sorgu.internal;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class IdIslenecekEvrakSorguInfo {

    private Long evrakId;
    private String evrakSiraNo;
    private String evrakNo;
    private Date evrakGirişTarihi;
    private Date evrakTarihi;
    private String evrakIlIlceKodu;
    private String evraklIlceAdi;
    private String evrakKurumKodu;
    private String evrakKurumAdı;
    private boolean acil;
    private String aciklama;
    private Long mahkemeKararTalepId;
    private Long atayanKullaniciId;
    private String atayanAdiSoyadi;
    private Long atananKullaniciId;
    private String atananAdiSoyadi;
    private String sorusturmaNo;
    private String mahkemeKararNo;
    private String mahkemeKodu;
    private String mahkemeAdi;

}
