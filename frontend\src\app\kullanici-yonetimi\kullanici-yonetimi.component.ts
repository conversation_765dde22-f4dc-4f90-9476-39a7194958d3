import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialog } from 'primeng/confirmdialog';
import { ConfirmationService, MessageService } from 'primeng/api';

import { DividerModule } from 'primeng/divider';
import { AuthService } from '../authentication/auth.service';
import { KullaniciModel } from './kullanici.model';
import { enumKullaniciStatus } from './kullanici-status.enum';
import { KullaniciService } from './kullanici.service';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { TabViewModule } from 'primeng/tabview';
import { KullaniciGrupService } from '../kullanici-grup-yonetimi/kullanici-grup.service';
import { ChipModule } from 'primeng/chip';
import { RolService } from '../rol-yonetimi/rol.service';

@Component({
  selector: 'app-kullanici-yonetimi',
  standalone: true,
  templateUrl: './kullanici-yonetimi.component.html',
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    ButtonModule,
    DialogModule,
    InputTextModule,
    DropdownModule,
    DividerModule,
    TabViewModule,
    MultiSelectModule,
    ConfirmDialog,
    ToastModule,
    ChipModule
  ],
  providers: [ConfirmationService, MessageService]
})
export class KullaniciYonetimiComponent implements OnInit {
  kullanicilar: KullaniciModel[] = [];
  selectedKullanici: KullaniciModel = this.getEmptyKullanici();
  displayDialog: boolean = false;
  isEditMode: boolean = false;
  allKullaniciGruplar: { id: number, ad: string }[] = [];
  allYetkiler: { id: number, name: string }[] = [];
  seciliKullanicininRolleri: string[] = [];

  constructor(
    private kullaniciService: KullaniciService,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    public authService: AuthService,
    private kullaniciGrupService: KullaniciGrupService,
    private yetkiService:RolService
  ) {}

  ngOnInit(): void {
    this.loadKullanicilar();
    this.loadAllKullaniciGruplar();
    this.loadAllYetkiler();
  }

  loadKullanicilar() {
    this.kullaniciService.getAllKullanicilar().subscribe(response => {
      this.kullanicilar = response;
    });
  }

  loadAllKullaniciGruplar() {
    this.kullaniciGrupService.getAllKullaniciGrup().subscribe(response => {
      this.allKullaniciGruplar = response.map(grup => ({
        id: grup.id!,
        ad: grup.ad
      }));
    });
  }

  loadAllYetkiler() {
    this.yetkiService.getAllRols().subscribe(response => {
      this.allYetkiler = response.map(yetki => ({
        id: yetki.id as number,
        name: `${yetki.ad}`
      }));
    });
  }

  getEmptyKullanici(): KullaniciModel {
    return {
      kullaniciAdi: '',
      ad: '',
      soyad: '',
      email: '',
      status: enumKullaniciStatus.SifreDegistirmeli,
      kullaniciGrupIdList: [],
      kullaniciYetkiIdList: []
    };
  }

  openNew() {
    this.selectedKullanici = this.getEmptyKullanici();
    this.seciliKullanicininRolleri = [];
    this.isEditMode = false;
    this.displayDialog = true;
  }

  editKullanici(kullanici: KullaniciModel) {
    this.selectedKullanici = { ...kullanici };

    this.seciliKullanicininRolleri =
      this.allYetkiler
        .filter(y => kullanici.kullaniciYetkiIdList?.includes(y.id))
        .map(y => y.name);

    this.isEditMode = true;
    this.displayDialog = true;
  }

  saveKullanici() {
    if (this.isEditMode) {
      this.kullaniciService.updateKullanici(this.selectedKullanici).subscribe(() => {
        this.loadKullanicilar();
        this.displayDialog = false;
        this.messageService.add({ severity: 'success', summary: 'Güncellendi', detail: 'Kullanıcı güncellendi' });
      });
    } else {
      this.kullaniciService.createKullanici(this.selectedKullanici).subscribe(() => {
        this.loadKullanicilar();
        this.displayDialog = false;
        this.messageService.add({ severity: 'success', summary: 'Oluşturuldu', detail: 'Kullanıcı eklendi' });
      });
    }
  }

  deleteKullanici(kullanici: KullaniciModel) {
    this.confirmationService.confirm({
      message: 'Bu kullanıcıyı silmek istediğinize emin misiniz?',
      header: 'Onayla',
      icon: 'pi pi-exclamation-triangle',
      acceptButtonStyleClass: 'p-button-danger',
      rejectButtonStyleClass: 'p-button-secondary',
      acceptLabel: 'Evet',
      rejectLabel: 'Hayır',
      accept: () => {
        this.kullaniciService.deleteKullanici(kullanici.id!).subscribe(() => {
          this.loadKullanicilar();
          this.messageService.add({
            severity: 'success',
            summary: 'Başarılı',
            detail: 'Kullanıcı silindi'
          });
        });
      }
    });
  }
}
